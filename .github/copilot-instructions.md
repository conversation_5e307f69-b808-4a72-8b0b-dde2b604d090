# GitHub Copilot Instructions for 8IT-Verwaltung

## Architecture Overview

This is a React+TypeScript business management application with Convex backend, organized into 5 main functional domains:

- **Verwaltung** (`/verwaltung/`): Administrative functions (customer master data, quotas, employees)
- **Erstellung** (`/erstellung/`): Document creation (services, quotes, delivery notes, reports)  
- **Kunden** (`/kunden/`): Customer-specific functions (documentation, appointments)
- **System** (`/system/`): System management (feedback, standards, categories)
- **Startseite** (`/`): Dashboard with performance metrics

## Key Development Patterns

### Frontend Structure
- **Pages**: Route components in `src/pages/[domain]/[feature]/`
- **Components**: Reusable UI in `src/components/[domain]/[feature]/`
- **Shared Components**: Common UI in `src/components/_shared/`
- **Layout**: `MainLayout` with protected routes and navigation
- **State**: Convex real-time queries/mutations (no Redux/Zustand)

### Backend (Convex) Structure
- **API**: Functions in `convex/[domain]/[feature].ts`
- **Config**: Feature configs like `lieferscheineConfig.ts`, `angeboteConfig.ts`
- **Schema**: Centralized in `convex/schema.ts`
- **Auth**: Clerk integration via `convex/auth.config.ts`

### Migration Context (June 2025)
Customer-related functions were split:
- `api.kunden.stammdaten.*` → `api.verwaltung.kunden.*`
- `api.kunden.kontingente.*` → `api.verwaltung.kontingente.*`
- Customer docs/appointments remain at `api.kunden.*`

## Essential Technical Details

### Authentication & Routing
- **Clerk** for auth with `ConvexProviderWithClerk`
- Routes in `src/App.tsx` with `ProtectedAppLayout`
- Environment: `VITE_CLERK_PUBLISHABLE_KEY` (frontend), `CLERK_DOMAIN` (backend)

### Development Workflow
```bash
npm run dev          # Development with env loading
npm run build        # Production build with env loading  
npm run check        # Biome lint+format+fix
npm run format       # Biome format only
```

### Styling & UI
- **Tailwind CSS** with dark theme
- **Shadcn/Radix UI** components in `src/components/_shared/`
- **Biome** formatter/linter (extends "ultracite" preset)
- **Lucide React** icons

### Data Patterns
- **Real-time queries**: `useQuery(api.domain.feature.functionName)`
- **Mutations**: `useMutation(api.domain.feature.functionName)`
- **File uploads**: Convex Storage API
- **PDF generation**: React-PDF in browser

### Business Logic Patterns

#### Document Numbering
- **Format**: `AGYY00001` (quotes), `YYXXXXX` (delivery notes)
- **Corrections**: Original + version suffix (`.2`, `.3`, etc.)
- **Immutability**: Finalized docs require corrections, not edits

#### Multi-Entity Operations
- **Quota splitting**: Auto-split services across quotas when exceeded
- **Batch operations**: Multi-position service entry in single dialog
- **Cascade updates**: Quota consumption tracked automatically

#### Email Integration
- **Resend Component**: Queue-based email with retry logic
- **PDF attachments**: Client-side generation with React-PDF
- **Recipients**: Main contacts + CC to all employees
- **Logging**: Structured format with user, document, recipients, email IDs

## File Organization Rules

### Component Naming
- **Pages**: `[Feature]Page.tsx` (e.g., `KundenPage.tsx`)
- **Components**: `[Feature][Type].tsx` (e.g., `KundenStammdatenForm.tsx`)
- **Detail pages**: `[id].tsx` in feature directory

### API Organization  
- **Domain functions**: `convex/[domain]/[feature].ts`
- **Config files**: `[feature]Config.ts` for PDF/email settings
- **Index exports**: `index.ts` in each domain folder

### Utilities
- **Date handling**: `src/lib/utils/dateUtils.ts` with German formatting
- **Validation**: Zod schemas with business rules
- **Formatting**: `formatUtils.ts` for currency, hours, etc.

## Critical Business Rules

1. **Data integrity**: No orphaned records (customers with services can't be deleted)
2. **Document immutability**: Finalized documents are read-only, require corrections
3. **Quota tracking**: Real-time quota consumption with automatic splitting
4. **Email delivery**: All business documents include employee CC lists
5. **Access control**: Clerk-based authentication with Convex integration

## Common Patterns to Follow

- Use `useQuery` for data fetching, `useMutation` for writes
- Implement optimistic updates for better UX
- Include error boundaries and loading states
- Follow the domain-based folder structure
- Use TypeScript strictly with proper typing
- Implement proper form validation with react-hook-form + Zod
- Use the existing shared components before creating new ones
