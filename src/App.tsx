import { RedirectToSignIn, useAuth } from '@clerk/clerk-react';
import {
  BrowserRouter,
  Navigate,
  Outlet,
  Route,
  Routes,
} from 'react-router-dom';
import { Toaster } from 'sonner';
import { SignInPage } from '@/components/_auth/SignInPage';
import { LieferscheinDetailPage } from '@/components/erstellung/lieferscheine/LieferscheinDetail';
import { MainLayout } from '@/components/layout/MainLayout';

import AngeboteDetailPage from '@/pages/erstellung/angebote/[id]';
import { AngebotePage } from '@/pages/erstellung/angebote/index';
import { LeistungenPage } from '@/pages/erstellung/leistung/index';
import { LieferscheinePage } from '@/pages/erstellung/lieferscheine/index';
import { UebersichtPage } from '@/pages/erstellung/uebersicht/index';
import { KundenDokuDetailPage } from '@/pages/kunden/doku/[id]';
import { KundenDokuPage } from '@/pages/kunden/doku/index';
import { KundenTermineDetailPage } from '@/pages/kunden/termine/[id]';
import { KundenTerminePage } from '@/pages/kunden/termine/index';
import { DashboardPage } from '@/pages/startseite/index';
import { DokuKategorienPage } from '@/pages/system/doku-kategorien/index';
import { FeedbackPage } from '@/pages/system/feedback/index';
import { StandardsPage } from '@/pages/system/standards/index';
import { KontingentePage } from '@/pages/verwaltung/kontingente/index';
import KundenStammdatenDetailPage from '@/pages/verwaltung/kunden/[id]';
import { KundenPage } from '@/pages/verwaltung/kunden/index';
import KundenStammdatenNeuPage from '@/pages/verwaltung/kunden/neu';
import MitarbeiterDetailPage from '@/pages/verwaltung/mitarbeiter/[id]';
import { MitarbeiterPage } from '@/pages/verwaltung/mitarbeiter/index';
import MitarbeiterNeuPage from '@/pages/verwaltung/mitarbeiter/neu';

/**
 * Component to handle the layout and nested routes for authenticated users.
 */
function ProtectedAppLayout() {
  // MainLayout already contains an <Outlet /> for its children.
  // This component ensures MainLayout is rendered only when signed in.
  return (
    <MainLayout>
      <Outlet /> {/* This Outlet will render the nested protected routes */}
    </MainLayout>
  );
}

export default function App() {
  const { isLoaded, isSignedIn } = useAuth();

  // Authentication state is handled by Clerk internally

  if (!isLoaded) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-900">
        <div className="h-10 w-10 animate-spin rounded-full border-blue-500 border-b-2" />
      </div>
    );
  }

  return (
    <BrowserRouter>
      <Toaster
        position="bottom-right"
        toastOptions={{
          duration: 4000,
          style: {
            '--toast-duration': '4000ms',
          } as React.CSSProperties,
        }}
      />
      <Routes>
        {/* Public route for signing in */}
        <Route element={<SignInPage />} path="/signin" />

        {/* Routes that require authentication */}
        <Route
          element={isSignedIn ? <ProtectedAppLayout /> : <RedirectToSignIn />} // This will match any path other than /signin if not caught by more specific routes
          path="/*"
        >
          {/* Default route for "/" when signed in */}
          <Route element={<DashboardPage />} index />

          {/* Kunden Routes */}
          <Route
            element={<Navigate replace to="/kunden/doku" />}
            path="kunden"
          />
          <Route element={<KundenDokuPage />} path="kunden/doku" />
          <Route element={<KundenDokuDetailPage />} path="kunden/doku/:id" />
          <Route element={<KundenTerminePage />} path="kunden/termine" />
          <Route
            element={<KundenTermineDetailPage />}
            path="kunden/termine/:id"
          />

          {/* Erstellung Routes */}
          <Route
            element={<Navigate replace to="/erstellung/leistung" />}
            path="erstellung"
          />
          <Route element={<LeistungenPage />} path="erstellung/leistung" />
          <Route element={<UebersichtPage />} path="erstellung/uebersicht" />
          <Route
            element={<LieferscheinePage />}
            path="erstellung/lieferscheine"
          />
          <Route
            element={<LieferscheinDetailPage />}
            path="erstellung/lieferscheine/:id"
          />
          <Route element={<AngebotePage />} path="erstellung/angebote" />
          <Route
            element={<AngeboteDetailPage />}
            path="erstellung/angebote/:id"
          />

          {/* Verwaltung Routes */}
          <Route
            element={<Navigate replace to="/verwaltung/kunden" />}
            path="verwaltung"
          />
          <Route element={<KundenPage />} path="verwaltung/kunden" />
          <Route
            element={<KundenStammdatenNeuPage />}
            path="verwaltung/kunden/neu"
          />
          <Route
            element={<KundenStammdatenDetailPage />}
            path="verwaltung/kunden/:id"
          />
          <Route element={<KontingentePage />} path="verwaltung/kontingente" />
          <Route element={<MitarbeiterPage />} path="verwaltung/mitarbeiter" />
          <Route
            element={<MitarbeiterNeuPage />}
            path="verwaltung/mitarbeiter/neu"
          />
          <Route
            element={<MitarbeiterDetailPage />}
            path="verwaltung/mitarbeiter/:id"
          />

          {/* System Routes */}
          <Route
            element={<Navigate replace to="/system/feedback" />}
            path="system"
          />
          <Route element={<FeedbackPage />} path="system/feedback" />
          <Route element={<StandardsPage />} path="system/standards" />
          <Route
            element={<DokuKategorienPage />}
            path="system/doku-kategorien"
          />

          {/* Catch-all for any other authenticated paths not matched above */}
          {/* This effectively means any path like /foo/bar if signed in will redirect to dashboard */}
          <Route element={<Navigate replace to="/" />} path="*" />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}
