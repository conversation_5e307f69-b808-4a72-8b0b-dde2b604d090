import { useMutation, useQuery } from 'convex/react';
import {
  ArrowLeft,
  Calendar,
  Pencil,
  Plus,
  Repeat,
  Trash2,
} from 'lucide-react';
import { useMemo, useRef, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Doc, Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import { Card, CardContent, CardHeader } from '@/components/_shared/Card';
import { Skeleton } from '@/components/_shared/Skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import { TerminForm } from '@/components/kunden/termine/TerminForm';
import { PageLayout } from '@/components/layout/PageLayout';
import { StandardDataTable } from '@/components/layout/StandardDataTable';
import {
  calculateTimeRemaining,
  formatDate,
  formatTime,
} from '@/lib/utils/dateUtils';

// Status entfernt - wird durch Kategorie ersetzt

export function KundenTermineDetailPage() {
  const { id } = useParams<{ id: string }>();
  const kundenId = id as Id<'kunden'>;
  const pageTopRef = useRef<HTMLDivElement>(null);

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingTermin, setEditingTermin] =
    useState<Doc<'kunden_termine'> | null>(null);

  const kunde = useQuery(api.verwaltung.kunden.get, { id: kundenId });
  const termine = useQuery(api.kunden.termine.getByKunde, { kundenId }) || [];

  const createTermin = useMutation(api.kunden.termine.create);
  const updateTermin = useMutation(api.kunden.termine.update);
  const deleteTermin = useMutation(api.kunden.termine.delete_);

  // Sort appointments by date (newest first)
  const sortedTermine = useMemo(() => {
    return [...termine].sort((a, b) => {
      const dateA = new Date(a.datum);
      const dateB = new Date(b.datum);
      return dateB.getTime() - dateA.getTime();
    });
  }, [termine]);

  const handleCreateTermin = async (data: any) => {
    try {
      await createTermin({
        kundenId,
        ...data,
      });
      setIsFormOpen(false);
      toast.success('Termin wurde erfolgreich erstellt');
    } catch (_error) {
      toast.error('Fehler beim Erstellen des Termins');
    }
  };

  const handleUpdateTermin = async (data: any) => {
    if (!editingTermin) {
      return;
    }

    try {
      await updateTermin({
        id: editingTermin._id,
        ...data,
      });
      setEditingTermin(null);
      setIsFormOpen(false);
      toast.success('Termin wurde erfolgreich aktualisiert');
    } catch (_error) {
      toast.error('Fehler beim Aktualisieren des Termins');
    }
  };

  const handleDeleteTermin = async (terminId: Id<'kunden_termine'>) => {
    if (!confirm('Sind Sie sicher, dass Sie diesen Termin löschen möchten?')) {
      return;
    }

    try {
      await deleteTermin({ id: terminId });
      toast.success('Termin wurde erfolgreich gelöscht');
    } catch (_error) {
      toast.error('Fehler beim Löschen des Termins');
    }
  };

  const handleEditTermin = (termin: Doc<'kunden_termine'>) => {
    setEditingTermin(termin);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingTermin(null);
  };

  const getWiederholungsText = (termin: Doc<'kunden_termine'>) => {
    if (!(termin.istWiederholend && termin.wiederholungsIntervall)) {
      return '-';
    }

    const intervalMap = {
      taeglich: 'Täglich',
      woechentlich: 'Wöchentlich',
      monatlich: 'Monatlich',
      jaehrlich: 'Jährlich',
    };

    return intervalMap[termin.wiederholungsIntervall] || '-';
  };

  const pageActionButtons = (
    <div className="flex gap-2">
      <Button
        className="gap-1"
        onClick={() => setIsFormOpen(true)}
        size="sm"
        variant="outline"
      >
        <Plus className="h-4 w-4" /> Neuer Termin
      </Button>
      <Link to="/kunden/termine">
        <Button className="gap-1" size="sm" variant="outline">
          <ArrowLeft className="h-4 w-4" /> Zurück zur Übersicht
        </Button>
      </Link>
    </div>
  );

  if (kunde === undefined) {
    return (
      <PageLayout
        action={pageActionButtons}
        ref={pageTopRef}
        subtitle="Termine werden geladen..."
        title="Kundentermine"
      >
        <Card className="border-0 shadow-lg">
          <CardHeader className="border-gray-700/50 border-b px-5 pb-3">
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent className="space-y-4 p-6">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-5/6" />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  if (!kunde) {
    return (
      <PageLayout
        action={pageActionButtons}
        ref={pageTopRef}
        subtitle="Kunde nicht gefunden"
        title="Kundentermine"
      >
        <Card className="border-0 shadow-lg">
          <CardContent className="p-6">
            <p className="text-gray-400">
              Der angeforderte Kunde wurde nicht gefunden.
            </p>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      action={pageActionButtons}
      ref={pageTopRef}
      subtitle="Verwalten Sie hier die Termine für diesen Kunden"
      title={`Termine: ${kunde.name}`}
    >
      <div className="space-y-8">
        <StandardDataTable
          infoSlot={
            <>
              <Calendar className="h-3.5 w-3.5 opacity-70" />
              <span>
                {sortedTermine.length}{' '}
                {sortedTermine.length === 1 ? 'Termin' : 'Termine'}
              </span>
            </>
          }
          title="Termine"
        >
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
                <TableHead className="px-3 py-2 text-xs">Kategorie</TableHead>
                <TableHead className="px-3 py-2 text-xs">Titel</TableHead>
                <TableHead className="px-3 py-2 text-xs">Datum</TableHead>
                <TableHead className="px-3 py-2 text-xs">Uhrzeit</TableHead>
                <TableHead className="px-3 py-2 text-xs">
                  Restlaufzeit
                </TableHead>
                <TableHead className="px-3 py-2 text-xs">
                  Wiederholung
                </TableHead>
                <TableHead className="w-24 px-3 py-2 text-center text-xs">
                  Aktionen
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedTermine.length === 0 ? (
                <TableRow>
                  <TableCell
                    className="py-8 text-center text-gray-400"
                    colSpan={7}
                  >
                    Keine Termine vorhanden. Erstellen Sie den ersten Termin für
                    diesen Kunden.
                  </TableCell>
                </TableRow>
              ) : (
                sortedTermine.map((termin) => {
                  return (
                    <TableRow key={termin._id}>
                      <TableCell className="px-3 py-1.5 text-xs">
                        <span className="inline-flex items-center rounded-full bg-blue-500/10 px-2 py-1 text-blue-400 text-xs">
                          {termin.kategorie}
                        </span>
                      </TableCell>
                      <TableCell className="px-3 py-1.5 font-medium text-xs">
                        <div className="flex items-center gap-1">
                          {termin.titel}
                          {termin.istWiederholend && (
                            <Repeat
                              className="h-3 w-3 text-blue-400"
                              title="Wiederholender Termin"
                            />
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="px-3 py-1.5 text-xs">
                        {termin.istWiederholend
                          ? formatDate(termin.naechsteWiederholung || '')
                          : formatDate(termin.datum || '')}
                      </TableCell>
                      <TableCell className="px-3 py-1.5 text-xs">
                        {formatTime(termin.uhrzeit)}
                      </TableCell>
                      <TableCell className="px-3 py-1.5 text-xs">
                        <span
                          className={
                            calculateTimeRemaining(
                              termin.istWiederholend
                                ? termin.naechsteWiederholung
                                : termin.datum,
                              termin.uhrzeit
                            ).startsWith('vor')
                              ? 'text-red-400'
                              : 'text-green-400'
                          }
                        >
                          {calculateTimeRemaining(
                            termin.istWiederholend
                              ? termin.naechsteWiederholung
                              : termin.datum,
                            termin.uhrzeit
                          )}
                        </span>
                      </TableCell>
                      <TableCell className="px-3 py-1.5 text-xs">
                        {getWiederholungsText(termin)}
                      </TableCell>
                      <TableCell className="px-3 py-1.5 text-center text-xs">
                        <div className="flex justify-center gap-1">
                          <Button
                            className="h-6 w-6 text-blue-400 hover:bg-blue-500/10 hover:text-blue-500"
                            onClick={() => handleEditTermin(termin)}
                            size="icon"
                            title="Termin bearbeiten"
                            variant="ghost"
                          >
                            <Pencil className="h-3 w-3" />
                          </Button>
                          <Button
                            className="h-6 w-6 text-red-400 hover:bg-red-500/10 hover:text-red-500"
                            onClick={() => handleDeleteTermin(termin._id)}
                            size="icon"
                            title="Termin löschen"
                            variant="ghost"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </StandardDataTable>
      </div>

      {isFormOpen && (
        <TerminForm
          initialData={editingTermin}
          isOpen={isFormOpen}
          onClose={handleCloseForm}
          onSubmit={editingTermin ? handleUpdateTermin : handleCreateTermin}
          title={editingTermin ? 'Termin bearbeiten' : 'Neuer Termin'}
        />
      )}
    </PageLayout>
  );
}
