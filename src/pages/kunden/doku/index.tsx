import { useQuery } from 'convex/react';
import { BookO<PERSON>, Briefcase } from 'lucide-react';
import { useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { api } from '@/../convex/_generated/api';
import { Button } from '@/components/_shared/Button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import { DokuFilterControls } from '@/components/kunden/doku/DokuFilterControls';
import { PageLayout } from '@/components/layout/PageLayout';
import { StandardDataTable } from '@/components/layout/StandardDataTable';

export function KundenDokuPage() {
  const kunden = useQuery(api.verwaltung.kunden.list) || [];
  const [searchTerm, setSearchTerm] = useState('');

  // Filter customers based on search term
  const filteredKunden = useMemo(() => {
    return kunden.filter((kunde) =>
      kunde.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [kunden, searchTerm]);

  // Get the main location for each customer
  const getHauptstandortOrt = (kunde: any) => {
    if (!kunde.standorte || kunde.standorte.length === 0) {
      return '-';
    }
    const hauptstandort = kunde.standorte.find((s: any) => s.istHauptstandort);
    return hauptstandort ? hauptstandort.ort : kunde.standorte[0].ort;
  };

  // Get the main contact for each customer
  const getHauptansprechpartnerName = (kunde: any) => {
    if (!kunde.ansprechpartner || kunde.ansprechpartner.length === 0) {
      return '-';
    }
    const hauptansprechpartner = kunde.ansprechpartner.find(
      (a: any) => a.istHauptansprechpartner
    );
    return hauptansprechpartner
      ? hauptansprechpartner.name
      : kunde.ansprechpartner[0].name;
  };

  return (
    <PageLayout
      subtitle="Dokumentation für alle Kunden verwalten"
      title="Kundendokumentation"
    >
      <StandardDataTable
        filterSlot={
          <DokuFilterControls
            onSearchTermChange={setSearchTerm}
            searchTerm={searchTerm}
          />
        }
        infoSlot={
          <>
            <Briefcase className="h-3.5 w-3.5 opacity-70" />
            <span>
              {filteredKunden.length}{' '}
              {filteredKunden.length === 1 ? 'Kunde' : 'Kunden'}
            </span>
          </>
        }
        title="Kundenübersicht"
      >
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
                <TableHead className="font-medium">Name</TableHead>
                <TableHead className="font-medium">Hauptstandort</TableHead>
                <TableHead className="font-medium">
                  Hauptansprechpartner
                </TableHead>
                <TableHead className="w-24 text-center">Aktionen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredKunden.length === 0 ? (
                <TableRow>
                  <TableCell className="h-24 text-center" colSpan={4}>
                    {searchTerm
                      ? `Keine Kunden gefunden für "${searchTerm}"`
                      : 'Keine Kunden vorhanden'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredKunden.map((kunde) => (
                  <TableRow
                    className="border-gray-800 border-b"
                    key={kunde._id}
                  >
                    <TableCell className="py-3">
                      <div className="flex items-center gap-2">
                        <div className="flex h-7 w-7 items-center justify-center rounded-full bg-gray-700 text-gray-300">
                          <Briefcase className="h-3.5 w-3.5" />
                        </div>
                        <span className="font-medium">{kunde.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>{getHauptstandortOrt(kunde)}</TableCell>
                    <TableCell>{getHauptansprechpartnerName(kunde)}</TableCell>
                    <TableCell className="text-center">
                      <div className="flex justify-center">
                        <Link to={`/kunden/doku/${kunde._id}`}>
                          <Button
                            className="h-8 w-8 text-gray-400 hover:text-blue-400"
                            size="icon"
                            title="Dokumentation"
                            variant="ghost"
                          >
                            <BookOpen className="h-4 w-4" />
                            <span className="sr-only">Dokumentation</span>
                          </Button>
                        </Link>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </StandardDataTable>
    </PageLayout>
  );
}

export default KundenDokuPage;
