import { useMutation, useQuery } from 'convex/react';
import { ArrowLeft } from 'lucide-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Doc, Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import { Card, CardContent } from '@/components/_shared/Card';
import { Skeleton } from '@/components/_shared/Skeleton';
import { AnsprechpartnerTable } from '@/components/kunden/doku/AnsprechpartnerTable';
import { DokuCategorySection } from '@/components/kunden/doku/DokuCategorySection';
import { DokuEntryForm } from '@/components/kunden/doku/DokuEntryForm';
import { StandorteTable } from '@/components/kunden/doku/StandorteTable';
import { TermineTable } from '@/components/kunden/doku/TermineTable';
import { PageLayout } from '@/components/layout/PageLayout';

// Define DokuEintragMitInfo to match the output of getByKunde which includes categoryName and kundeName
interface DokuEintragMitInfo {
  _id: Id<'kunden_dokumentation'>;
  _creationTime: number;
  kundenId: Id<'kunden'>;
  kategorieID: number; // Numeric ID of the category
  feldwerte: { feldId: number; feldWert: string; feldName?: string }[];
  kategorieName: string; // Added from getByKunde
}

export function KundenDokuDetailPage() {
  const { id } = useParams<{ id: string }>();
  const kundenId = id as Id<'kunden'>;
  const pageTopRef = useRef<HTMLDivElement>(null);

  const kunde = useQuery(api.verwaltung.kunden.get, { id: kundenId }) as
    | Doc<'kunden'>
    | null
    | undefined;
  const alleKategorien = useQuery(api.system.dokuKategorien.list) || [];
  const alleEintraegeFuerKunde: DokuEintragMitInfo[] =
    useQuery(api.kunden.dokumentation.getByKunde, { kundenId }) || [];
  const termine = useQuery(api.kunden.termine.getByKunde, { kundenId }) || [];

  const initializeDefaults = useMutation(
    api.system.dokuKategorien.initializeDefaults
  );
  const removeEintrag = useMutation(api.kunden.dokumentation.remove);

  const [editingState, setEditingState] = useState<{
    kategorieId: Id<'system_doku_kategorien'> | null;
    eintragId: Id<'kunden_dokumentation'> | null;
  }>({ kategorieId: null, eintragId: null });

  const [hasInitialized, setHasInitialized] = useState(false);

  useEffect(() => {
    // Prüfen ob bereits in dieser Session initialisiert wurde
    const sessionKey = 'doku-kategorien-initialized';
    const alreadyInitializedInSession =
      sessionStorage.getItem(sessionKey) === 'true';

    // Nur initialisieren wenn:
    // 1. Kategorien-Query geladen ist (nicht undefined)
    // 2. Keine Kategorien vorhanden sind
    // 3. Noch nicht initialisiert wurde (weder in Komponente noch in Session)
    if (
      alleKategorien !== undefined &&
      alleKategorien.length === 0 &&
      !hasInitialized &&
      !alreadyInitializedInSession
    ) {
      setHasInitialized(true);
      sessionStorage.setItem(sessionKey, 'true');

      initializeDefaults({})
        .then((result) => {
          // Nur bei tatsächlichen Änderungen einen Toast anzeigen
          if (!result.includes('Keine Änderungen')) {
            // Weniger aufdringlicher Toast nur bei ersten Änderungen
            if (result.includes('hinzugefügt')) {
              toast.info('Dokumentationskategorien wurden eingerichtet.');
            }
          }
        })
        .catch((_err) => {
          toast.error('Fehler bei Initialisierung der Standardkategorien.');
          // Bei Fehler Session-Flag zurücksetzen
          sessionStorage.removeItem(sessionKey);
          setHasInitialized(false);
        });
    }
  }, [alleKategorien, initializeDefaults, hasInitialized]);

  const showFormForKategorie = editingState.kategorieId;
  const editingEintragId = editingState.eintragId;

  const sortedKategorien = useMemo(() => {
    return alleKategorien.sort((a, b) => a.reihenfolge - b.reihenfolge);
  }, [alleKategorien]);

  const handleFormSubmitSuccess = () => {
    setEditingState({ kategorieId: null, eintragId: null });
    toast.success('Dokumentationseintrag gespeichert.');
  };

  const handleFormCancel = () => {
    setEditingState({ kategorieId: null, eintragId: null });
  };

  const handleAddEntry = (kategorieId: Id<'system_doku_kategorien'>) => {
    setEditingState({ kategorieId, eintragId: null });
  };

  const handleEditEntry = (
    kategorieId: Id<'system_doku_kategorien'>,
    eintragId: Id<'kunden_dokumentation'>
  ) => {
    setEditingState({ kategorieId, eintragId });
  };

  const handleDeleteEntry = async (id: Id<'kunden_dokumentation'>) => {
    if (
      window.confirm(
        'Möchten Sie diesen Dokumentationseintrag wirklich löschen?'
      )
    ) {
      try {
        await removeEintrag({ id });
        toast.success('Eintrag erfolgreich gelöscht.');
      } catch (error) {
        toast.error(`Fehler beim Löschen: ${(error as Error).message}`);
      }
    }
  };

  const pageActionButtons = (
    <Link to="/kunden/doku">
      <Button className="gap-1" size="sm" variant="outline">
        <ArrowLeft className="h-4 w-4" /> Zurück zur Übersicht
      </Button>
    </Link>
  );

  if (
    kunde === undefined ||
    alleKategorien === undefined ||
    alleEintraegeFuerKunde === undefined
  ) {
    return (
      <PageLayout
        action={pageActionButtons}
        ref={pageTopRef}
        subtitle="Dokumentation wird geladen..."
        title="Kundendokumentation"
      >
        <Card className="border-0 shadow-lg">
          <CardContent className="space-y-4 p-6">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-5/6" />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  if (kunde === null) {
    return (
      <PageLayout
        ref={pageTopRef}
        subtitle="Kunde nicht gefunden."
        title="Fehler"
      >
        <p>Der angeforderte Kunde konnte nicht gefunden werden.</p>
        <Link to="/kunden/doku">
          <Button className="mt-4" variant="outline">
            Zurück zur Übersicht
          </Button>
        </Link>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      action={pageActionButtons}
      ref={pageTopRef}
      subtitle="Verwalten Sie hier die Dokumentation für diesen Kunden"
      title={`Dokumentation: ${kunde.name}`}
    >
      <div className="space-y-5">
        {/* Standorte */}
        <StandorteTable kunde={kunde} />

        {/* Ansprechpartner mit QR-Code */}
        <AnsprechpartnerTable kunde={kunde} />

        {/* Termine */}
        <TermineTable kundenId={kundenId} termine={termine} />

        {/* Dokumentationskategorien */}
        {sortedKategorien.map((kategorie) => (
          <DokuCategorySection
            alleEintraegeFuerKunde={alleEintraegeFuerKunde}
            kategorie={kategorie}
            key={kategorie._id}
            onAddEntry={handleAddEntry}
            onCancelForm={handleFormCancel}
            onDeleteEntry={handleDeleteEntry}
            onEditEntry={handleEditEntry}
            showFormForKategorie={showFormForKategorie}
          >
            {showFormForKategorie === kategorie._id && (
              <DokuEntryForm
                editingId={editingEintragId}
                kategorieId={kategorie._id}
                kundenId={kundenId}
                onCancel={handleFormCancel}
                onSubmitSuccess={handleFormSubmitSuccess}
              />
            )}
          </DokuCategorySection>
        ))}

        {sortedKategorien.length === 0 && (
          <Card className="border-0 shadow-lg">
            <CardContent className="p-6 text-center text-gray-400 text-xs">
              Keine Dokumentationskategorien gefunden. Bitte legen Sie welche
              unter "System {'>'} Doku-Kategorien" an oder synchronisieren Sie
              die Standardkategorien.
            </CardContent>
          </Card>
        )}
      </div>
    </PageLayout>
  );
}

export default KundenDokuDetailPage;
