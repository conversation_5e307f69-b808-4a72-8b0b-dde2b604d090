import { useMutation, useQuery } from 'convex/react';
import { ArrowLeft, Save } from 'lucide-react';
import { useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import { PageLayout } from '@/components/layout/PageLayout';
import { MitarbeiterForm } from '@/components/verwaltung/mitarbeiter/MitarbeiterForm';

export function MitarbeiterDetailPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const mitarbeiterId = id as Id<'mitarbeiter'>;
  const mitarbeiter = useQuery(api.verwaltung.mitarbeiter.get, {
    id: mitarbeiterId,
  });
  const updateMitarbeiter = useMutation(api.verwaltung.mitarbeiter.update);

  const handleSave = async (data: { name: string; email: string }) => {
    if (!mitarbeiter) {
      return;
    }

    setIsSubmitting(true);
    try {
      await updateMitarbeiter({
        id: mitarbeiter._id,
        ...data,
      });
      toast.success('Mitarbeiter erfolgreich aktualisiert');
      navigate('/verwaltung/mitarbeiter');
    } catch (error: unknown) {
      const message =
        error instanceof Error ? error.message : 'Unbekannter Fehler';
      toast.error(`Fehler beim Speichern: ${message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!mitarbeiter) {
    return (
      <PageLayout subtitle="Bitte warten" title="Mitarbeiter wird geladen...">
        <div className="flex h-64 items-center justify-center">
          <div className="text-gray-400">Lade Mitarbeiterdaten...</div>
        </div>
      </PageLayout>
    );
  }

  const headerActions = (
    <div className="flex items-center gap-2">
      <Link to="/verwaltung/mitarbeiter">
        <Button className="gap-2" variant="outline">
          <ArrowLeft className="h-4 w-4" />
          Zurück
        </Button>
      </Link>

      <Button
        className="gap-2"
        disabled={isSubmitting}
        form="mitarbeiter-form"
        type="submit"
      >
        <Save className="h-4 w-4" />
        {isSubmitting ? 'Speichert...' : 'Speichern'}
      </Button>
    </div>
  );

  return (
    <PageLayout
      action={headerActions}
      subtitle="Mitarbeiterdaten bearbeiten"
      title={`Mitarbeiter bearbeiten: ${mitarbeiter.name}`}
    >
      <div className="mx-auto max-w-4xl">
        {/* Form */}
        <MitarbeiterForm
          formId="mitarbeiter-form"
          initialData={mitarbeiter}
          isSubmitting={isSubmitting}
          onSubmit={handleSave}
        />
      </div>
    </PageLayout>
  );
}

export default MitarbeiterDetailPage;
