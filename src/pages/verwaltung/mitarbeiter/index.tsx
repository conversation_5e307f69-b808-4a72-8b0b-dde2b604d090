import { useMutation, useQuery } from 'convex/react';
import { PlusCircle, Users } from 'lucide-react';
import { useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import { EmptyState } from '@/components/layout/EmptyState';
import { PageLayout } from '@/components/layout/PageLayout';
import { StandardDataTable } from '@/components/layout/StandardDataTable';
import { MitarbeiterDataTable } from '@/components/verwaltung/mitarbeiter/MitarbeiterDataTable';
import { MitarbeiterFilterControls } from '@/components/verwaltung/mitarbeiter/MitarbeiterFilterControls';

export function MitarbeiterPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const mitarbeiter = useQuery(api.verwaltung.mitarbeiter.list) || [];
  const deleteMitarbeiter = useMutation(api.verwaltung.mitarbeiter.delete_);

  const handleDelete = async (id: Id<'mitarbeiter'>) => {
    if (
      !confirm('Sind Sie sicher, dass Sie diesen Mitarbeiter löschen möchten?')
    ) {
      return;
    }

    try {
      await deleteMitarbeiter({ id });
      toast.success('Mitarbeiter erfolgreich gelöscht');
    } catch (error: any) {
      toast.error(`Fehler beim Löschen: ${error.message}`);
    }
  };

  const filteredMitarbeiter = useMemo(() => {
    return mitarbeiter.filter((m) => {
      const matchesSearch =
        m.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        m.email.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });
  }, [mitarbeiter, searchTerm]);

  const handleSearchTermChange = (value: string) => {
    setSearchTerm(value);
  };

  const resetFilters = () => {
    setSearchTerm('');
  };

  const actionButton = (
    <Link to="/verwaltung/mitarbeiter/neu">
      <Button className="gap-1" size="sm">
        <PlusCircle className="h-4 w-4" />
        Neuer Mitarbeiter
      </Button>
    </Link>
  );

  const filterControls = (
    <MitarbeiterFilterControls
      onSearchTermChange={handleSearchTermChange}
      searchTerm={searchTerm}
    />
  );

  const infoSlot = (
    <>
      <Users className="h-3.5 w-3.5 opacity-70" />
      <span>
        {filteredMitarbeiter.length} von {mitarbeiter.length} Mitarbeiter
      </span>
    </>
  );

  return (
    <PageLayout
      action={actionButton}
      subtitle="Verwaltung der Mitarbeiterdaten"
      title="Mitarbeiter"
    >
      <StandardDataTable
        filterSlot={filterControls}
        infoSlot={infoSlot}
        title="Mitarbeiterübersicht"
      >
        {filteredMitarbeiter.length === 0 && !searchTerm ? (
          <EmptyState
            actions={
              <Link to="/verwaltung/mitarbeiter/neu">
                <Button className="gap-2">
                  <PlusCircle className="h-4 w-4" />
                  Ersten Mitarbeiter erstellen
                </Button>
              </Link>
            }
            icon={<Users className="h-12 w-12" />}
            message="Erstellen Sie Ihren ersten Mitarbeiter, um loszulegen."
            title="Noch keine Mitarbeiter vorhanden"
          />
        ) : filteredMitarbeiter.length === 0 && searchTerm ? (
          <EmptyState
            actions={
              <Button onClick={resetFilters} variant="outline">
                Filter zurücksetzen
              </Button>
            }
            icon={<Users className="h-12 w-12" />}
            message="Keine Mitarbeiter entsprechen den aktuellen Filterkriterien."
            title="Keine Mitarbeiter gefunden"
          />
        ) : (
          <MitarbeiterDataTable
            mitarbeiter={filteredMitarbeiter}
            onDelete={handleDelete}
          />
        )}
      </StandardDataTable>
    </PageLayout>
  );
}

export default MitarbeiterPage;
