import { useMutation } from 'convex/react';
import { ArrowLeft, Save } from 'lucide-react';
import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import { Button } from '@/components/_shared/Button';
import { PageLayout } from '@/components/layout/PageLayout';
import { MitarbeiterForm } from '@/components/verwaltung/mitarbeiter/MitarbeiterForm';

export function MitarbeiterNeuPage() {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const createMitarbeiter = useMutation(api.verwaltung.mitarbeiter.create);

  const handleSave = async (data: { name: string; email: string }) => {
    setIsSubmitting(true);
    try {
      const mitarbeiterId = await createMitarbeiter(data);
      toast.success('Mitarbeiter erfolgreich erstellt');
      navigate(`/verwaltung/mitarbeiter/${mitarbeiterId}`);
    } catch (error: unknown) {
      const message =
        error instanceof Error ? error.message : 'Unbekannter Fehler';
      toast.error(`Fehler beim Erstellen: ${message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const headerActions = (
    <div className="flex items-center gap-2">
      <Link to="/verwaltung/mitarbeiter">
        <Button className="gap-2" variant="outline">
          <ArrowLeft className="h-4 w-4" />
          Zurück
        </Button>
      </Link>

      <Button
        className="gap-2"
        disabled={isSubmitting}
        form="mitarbeiter-form"
        type="submit"
      >
        <Save className="h-4 w-4" />
        {isSubmitting ? 'Erstellt...' : 'Erstellen'}
      </Button>
    </div>
  );

  return (
    <PageLayout
      action={headerActions}
      subtitle="Neuen Mitarbeiter erstellen"
      title="Neuer Mitarbeiter"
    >
      <div className="mx-auto max-w-4xl">
        <MitarbeiterForm
          formId="mitarbeiter-form"
          isSubmitting={isSubmitting}
          onSubmit={handleSave}
        />
      </div>
    </PageLayout>
  );
}

export default MitarbeiterNeuPage;
