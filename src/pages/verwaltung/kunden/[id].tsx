import { useMutation, useQuery } from 'convex/react';
import { ArrowLeft, Save } from 'lucide-react';
import { useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import { PageLayout } from '@/components/layout/PageLayout';
import { KundenForm } from '@/components/verwaltung/kunden/KundenForm';

export function KundenStammdatenDetailPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const kundeId = id as Id<'kunden'>;
  const kunde = useQuery(api.verwaltung.kunden.get, { id: kundeId });
  const updateKunde = useMutation(api.verwaltung.kunden.update);

  const handleSave = async (data: any) => {
    if (!kunde) {
      return;
    }

    setIsSubmitting(true);
    try {
      await updateKunde({
        id: kunde._id,
        ...data,
      });
      toast.success('Kunde erfolgreich aktualisiert');
      navigate('/verwaltung/kunden');
    } catch (error: any) {
      toast.error(`Fehler beim Speichern: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!kunde) {
    return (
      <PageLayout subtitle="Bitte warten" title="Kunde wird geladen...">
        <div className="flex h-64 items-center justify-center">
          <div className="text-gray-400">Lade Kundendaten...</div>
        </div>
      </PageLayout>
    );
  }

  const headerActions = (
    <div className="flex items-center gap-2">
      <Link to="/verwaltung/kunden">
        <Button className="gap-2" variant="outline">
          <ArrowLeft className="h-4 w-4" />
          Zurück
        </Button>
      </Link>

      <Button
        className="gap-2"
        disabled={isSubmitting}
        form="kunden-form"
        type="submit"
      >
        <Save className="h-4 w-4" />
        {isSubmitting ? 'Speichert...' : 'Speichern'}
      </Button>
    </div>
  );

  return (
    <PageLayout
      action={headerActions}
      subtitle="Stammdaten bearbeiten"
      title={`Kunde bearbeiten: ${kunde.name}`}
    >
      <div className="mx-auto max-w-6xl">
        {/* Form */}
        <KundenForm
          formId="kunden-form"
          initialData={kunde}
          isSubmitting={isSubmitting}
          onSubmit={handleSave}
        />
      </div>
    </PageLayout>
  );
}

export default KundenStammdatenDetailPage;
