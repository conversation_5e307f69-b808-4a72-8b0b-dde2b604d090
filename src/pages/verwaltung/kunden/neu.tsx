import { useMutation } from 'convex/react';
import { ArrowLeft, Save } from 'lucide-react';
import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import { Button } from '@/components/_shared/Button';
import { PageLayout } from '@/components/layout/PageLayout';
import { KundenForm } from '@/components/verwaltung/kunden/KundenForm';

export function KundenStammdatenNeuPage() {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const createKunde = useMutation(api.verwaltung.kunden.create);

  const handleSave = async (data: any) => {
    setIsSubmitting(true);
    try {
      const kundeId = await createKunde(data);
      toast.success('Kunde erfolgreich erstellt');
      navigate(`/verwaltung/kunden/${kundeId}`);
    } catch (error: any) {
      toast.error(`<PERSON><PERSON> beim <PERSON>: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const headerActions = (
    <div className="flex items-center gap-2">
      <Link to="/verwaltung/kunden">
        <Button className="gap-2" variant="outline">
          <ArrowLeft className="h-4 w-4" />
          Zurück
        </Button>
      </Link>

      <Button
        className="gap-2"
        disabled={isSubmitting}
        form="kunden-form"
        type="submit"
      >
        <Save className="h-4 w-4" />
        {isSubmitting ? 'Erstellt...' : 'Erstellen'}
      </Button>
    </div>
  );

  return (
    <PageLayout
      action={headerActions}
      subtitle="Neuen Kunden erstellen"
      title="Neuer Kunde"
    >
      <div className="mx-auto max-w-6xl">
        <KundenForm
          formId="kunden-form"
          isSubmitting={isSubmitting}
          onSubmit={handleSave}
        />
      </div>
    </PageLayout>
  );
}

export default KundenStammdatenNeuPage;
