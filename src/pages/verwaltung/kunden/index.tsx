import { useMutation, useQuery } from 'convex/react';
import { Briefcase, PlusCircle } from 'lucide-react';
import { useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import { EmptyState } from '@/components/layout/EmptyState';
import { PageLayout } from '@/components/layout/PageLayout';
import { StandardDataTable } from '@/components/layout/StandardDataTable';
import { KundenDataTable } from '@/components/verwaltung/kunden/KundenDataTable';
import { KundenFilterControls } from '@/components/verwaltung/kunden/KundenFilterControls';
import { formatCurrency } from '@/lib/utils/formatUtils';

export interface StandortData {
  strasse: string;
  plz: string;
  ort: string;
  land?: string;
  istHauptstandort: boolean;
}

export interface AnsprechpartnerData {
  name: string;
  email?: string;
  telefon?: string;
  mobil?: string;
  position?: string;
  istHauptansprechpartner: boolean;
  istEmailLieferscheinEmpfaenger?: boolean;
  istEmailUebersichtEmpfaenger?: boolean;
  istEmailAnrede?: boolean;
}

export interface Kunde {
  _id: Id<'kunden'>;
  _creationTime: number;
  name: string;
  stundenpreis: number;
  anfahrtskosten: number;
  standorte: StandortData[];
  ansprechpartner: AnsprechpartnerData[];
}

export function KundenPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const kunden = useQuery(api.verwaltung.kunden.list) || [];
  const removeKunde = useMutation(api.verwaltung.kunden.remove);

  const handleDelete = async (id: Id<'kunden'>) => {
    if (!confirm('Sind Sie sicher, dass Sie diesen Kunden löschen möchten?')) {
      return;
    }

    try {
      await removeKunde({ id });
      toast.success('Kunde erfolgreich gelöscht');
    } catch (error: any) {
      toast.error(`Fehler beim Löschen: ${error.message}`);
    }
  };

  const getHauptstandortOrt = (kunde: Kunde): string => {
    const hauptstandort = kunde.standorte.find((s) => s.istHauptstandort);
    return hauptstandort ? `${hauptstandort.plz} ${hauptstandort.ort}` : '';
  };

  const getHauptansprechpartnerInfo = (
    kunde: Kunde
  ): { email?: string; name?: string } => {
    const hauptansprechpartner = kunde.ansprechpartner.find(
      (a) => a.istHauptansprechpartner
    );
    return hauptansprechpartner
      ? { email: hauptansprechpartner.email, name: hauptansprechpartner.name }
      : {};
  };

  const filteredKunden = useMemo(() => {
    return kunden.filter((kunde) => {
      const matchesSearch = kunde.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      return matchesSearch;
    });
  }, [kunden, searchTerm]);

  const handleSearchTermChange = (value: string) => {
    setSearchTerm(value);
  };

  const resetFilters = () => {
    setSearchTerm('');
  };

  const actionButton = (
    <Link to="/verwaltung/kunden/neu">
      <Button className="gap-1" size="sm">
        <PlusCircle className="h-4 w-4" />
        Neuer Kunde
      </Button>
    </Link>
  );

  const filterControls = (
    <KundenFilterControls
      onSearchTermChange={handleSearchTermChange}
      searchTerm={searchTerm}
    />
  );

  const infoSlot = (
    <>
      <Briefcase className="h-3.5 w-3.5 opacity-70" />
      <span>
        {filteredKunden.length} von {kunden.length} Kunden
      </span>
    </>
  );

  return (
    <PageLayout
      action={actionButton}
      subtitle="Verwaltung der Kundenstammdaten"
      title="Kunden"
    >
      <StandardDataTable
        filterSlot={filterControls}
        infoSlot={infoSlot}
        title="Kundenübersicht"
      >
        {filteredKunden.length === 0 && !searchTerm ? (
          <EmptyState
            actions={
              <Link to="/verwaltung/kunden/neu">
                <Button className="gap-2">
                  <PlusCircle className="h-4 w-4" />
                  Ersten Kunden erstellen
                </Button>
              </Link>
            }
            icon={<Briefcase className="h-12 w-12" />}
            message="Erstellen Sie Ihren ersten Kunden, um loszulegen."
            title="Noch keine Kunden vorhanden"
          />
        ) : filteredKunden.length === 0 && searchTerm ? (
          <EmptyState
            actions={
              <Button onClick={resetFilters} variant="outline">
                Filter zurücksetzen
              </Button>
            }
            icon={<Briefcase className="h-12 w-12" />}
            message="Keine Kunden entsprechen den aktuellen Filterkriterien."
            title="Keine Kunden gefunden"
          />
        ) : (
          <KundenDataTable
            formatCurrency={formatCurrency}
            getHauptansprechpartnerInfo={getHauptansprechpartnerInfo}
            getHauptstandortOrt={getHauptstandortOrt}
            kunden={filteredKunden}
            onDelete={handleDelete}
          />
        )}
      </StandardDataTable>
    </PageLayout>
  );
}

export default KundenPage;
