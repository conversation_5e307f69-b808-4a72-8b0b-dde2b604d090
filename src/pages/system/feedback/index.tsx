import { useMutation, useQuery } from 'convex/react';
import {
  Bug<PERSON>con,
  CheckCircle,
  Clock,
  Lightbulb,
  RefreshCw,
  Search,
  Trash2,
} from 'lucide-react';
import { useMemo, useState } from 'react';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import { Input } from '@/components/_shared/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/_shared/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import { EmptyState } from '@/components/layout/EmptyState';
import { PageLayout } from '@/components/layout/PageLayout';
import { StandardDataTable } from '@/components/layout/StandardDataTable';
import { formatRelativeTime } from '@/lib/utils/dateUtils';

// Typen für Feedback-Einträge
interface SystemFeedback {
  _id: Id<'system_feedback'>;
  _creationTime: number;
  userName: string;
  text: string;
  type: string; // "feature" oder "bug"
  status: string; // "offen", "in_bearbeitung", "erledigt"
}

export function FeedbackPage() {
  // State für Filter
  const [statusFilter, setStatusFilter] = useState<string | undefined>(
    undefined
  );
  const [searchTerm, setSearchTerm] = useState('');

  // Hilfsfunktion: Sind Filter aktiv?
  const filtersActive = !!searchTerm || !!statusFilter;

  // Convex Queries und Mutations
  const allFeedback =
    useQuery(api.system.feedback.list, { status: statusFilter }) || [];
  const updateFeedbackStatus = useMutation(api.system.feedback.updateStatus);
  const removeFeedback = useMutation(api.system.feedback.remove);

  // Gefilterte Feedback-Listen nach Typ
  const filteredFeedback = useMemo(() => {
    let filtered = allFeedback;

    if (searchTerm) {
      filtered = filtered.filter(
        (feedback) =>
          feedback.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          feedback.text.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Aufteilen nach Typ
    const features = filtered.filter((feedback) => feedback.type === 'feature');
    const bugs = filtered.filter((feedback) => feedback.type === 'bug');

    return { features, bugs, all: filtered };
  }, [allFeedback, searchTerm]);

  // Status-Optionen
  const statusOptions = [
    { value: 'offen', label: 'Offen', color: 'blue' },
    { value: 'in_bearbeitung', label: 'In Bearbeitung', color: 'yellow' },
    { value: 'erledigt', label: 'Erledigt', color: 'green' },
  ];

  // Handler für Status-Änderung
  const handleStatusChange = async (
    id: Id<'system_feedback'>,
    newStatus: string
  ) => {
    try {
      await updateFeedbackStatus({ id, status: newStatus });
    } catch (_error) {}
  };

  // Handler für Löschen
  const handleDelete = async (id: Id<'system_feedback'>) => {
    if (window.confirm('Möchten Sie diesen Eintrag wirklich löschen?')) {
      try {
        await removeFeedback({ id });
      } catch (_error) {}
    }
  };

  // Status-Badge Komponente
  const StatusBadge = ({ status }: { status: string }) => {
    let bgColor = '';
    let borderColor = '';
    let icon = null;
    const label =
      statusOptions.find((option) => option.value === status)?.label ||
      'Unbekannt';

    switch (status) {
      case 'offen':
        bgColor = 'bg-red-500/20';
        borderColor = 'border-red-500/30';
        icon = <Clock className="mr-1 h-3 w-3 text-red-400" />;
        break;
      case 'in_bearbeitung':
        bgColor = 'bg-amber-500/20';
        borderColor = 'border-amber-500/30';
        icon = <RefreshCw className="mr-1 h-3 w-3 text-amber-400" />;
        break;
      case 'erledigt':
        bgColor = 'bg-green-500/20';
        borderColor = 'border-green-500/30';
        icon = <CheckCircle className="mr-1 h-3 w-3 text-green-400" />;
        break;
      default:
        bgColor = 'bg-gray-700';
        borderColor = 'border-gray-600';
        break;
    }

    return (
      <div
        className={`inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold text-white text-xs ${bgColor} ${borderColor}`}
      >
        {icon}
        {label}
      </div>
    );
  };

  // Filter Component
  const FeedbackFilterControls = () => {
    return (
      <div className="flex flex-wrap items-center gap-2">
        <div className="relative">
          <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-gray-500" />
          <Input
            className="w-[200px] bg-gray-800 pl-9"
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Suchen..."
            type="search"
            value={searchTerm}
          />
        </div>

        <div className="flex items-center gap-2">
          <Select
            onValueChange={(value) =>
              setStatusFilter(value === 'all' ? undefined : value)
            }
            value={statusFilter || 'all'}
          >
            <SelectTrigger className="w-[180px] bg-gray-800">
              <SelectValue placeholder="Alle Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Alle Status</SelectItem>
              {statusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Button direkt neben dem Status-Filter anzeigen */}
          {statusFilter && (
            <Button
              onClick={() => setStatusFilter(undefined)}
              size="sm"
              variant="outline"
            >
              Filter zurücksetzen
            </Button>
          )}
        </div>
      </div>
    );
  };

  // Feedback-Tabelle Komponente
  const FeedbackTable = ({
    items,
    emptyMessage,
  }: {
    items: SystemFeedback[];
    emptyMessage: string;
  }) => (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[15%]">Benutzer</TableHead>
            <TableHead className="w-[35%]">Beschreibung</TableHead>
            <TableHead className="w-[20%]">Status</TableHead>
            <TableHead className="w-[20%]">Datum</TableHead>
            <TableHead className="w-[10%] text-right">Aktionen</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.length === 0 ? (
            <TableRow>
              <TableCell className="py-8 text-center text-gray-400" colSpan={5}>
                {emptyMessage}
              </TableCell>
            </TableRow>
          ) : (
            items.map((feedback) => (
              <TableRow key={feedback._id}>
                <TableCell className="font-medium">
                  {feedback.userName}
                </TableCell>
                <TableCell className="max-w-md">
                  <div className="line-clamp-2">{feedback.text}</div>
                </TableCell>
                <TableCell>
                  <Select
                    onValueChange={(value) =>
                      handleStatusChange(feedback._id, value)
                    }
                    value={feedback.status}
                  >
                    <SelectTrigger className="w-[180px]">
                      <StatusBadge status={feedback.status} />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </TableCell>
                <TableCell>
                  {formatRelativeTime(feedback._creationTime)}
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    onClick={() => handleDelete(feedback._id)}
                    size="icon"
                    title="Löschen"
                    variant="ghost"
                  >
                    <Trash2 className="h-4 w-4 text-red-400" />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );

  const renderFeaturesSection = () => (
    <StandardDataTable
      filterSlot={null}
      infoSlot={
        <>
          <Lightbulb className="h-3.5 w-3.5 opacity-70" />
          <span>{filteredFeedback.features.length} Vorschläge</span>
        </>
      }
      title="Neue Funktionen"
    >
      {filteredFeedback.features.length === 0 ? (
        <EmptyState
          icon={<Lightbulb className="h-12 w-12 text-green-400/50" />}
          message={
            filtersActive
              ? 'Versuchen Sie, Ihre Filterkriterien anzupassen oder setzen Sie die Filter zurück.'
              : 'Es wurden keine Vorschläge für neue Funktionen erstellt'
          }
          title="Keine Vorschläge für neue Funktionen"
        />
      ) : (
        <FeedbackTable
          emptyMessage="Keine Vorschläge für neue Funktionen gefunden"
          items={filteredFeedback.features}
        />
      )}
    </StandardDataTable>
  );

  const renderBugsSection = () => (
    <StandardDataTable
      filterSlot={null}
      infoSlot={
        <>
          <BugIcon className="h-3.5 w-3.5 opacity-70" />
          <span>{filteredFeedback.bugs.length} Fehlerberichte</span>
        </>
      }
      title="Gemeldete Bugs"
    >
      {filteredFeedback.bugs.length === 0 ? (
        <EmptyState
          icon={<BugIcon className="h-12 w-12 text-red-400/50" />}
          message={
            filtersActive
              ? 'Versuchen Sie, Ihre Filterkriterien anzupassen oder setzen Sie die Filter zurück.'
              : 'Es wurden keine Fehlerberichte erstellt'
          }
          title="Keine Fehlerberichte"
        />
      ) : (
        <FeedbackTable
          emptyMessage="Keine Fehlerberichte gefunden"
          items={filteredFeedback.bugs}
        />
      )}
    </StandardDataTable>
  );

  return (
    <PageLayout
      subtitle="Verwalten Sie Funktionsvorschläge und Fehlerberichte"
      title="Feedback-Verwaltung"
    >
      <div className="space-y-6">
        {/* Zentraler Filterbereich */}
        <FeedbackFilterControls />
        {renderFeaturesSection()}
        {renderBugsSection()}
      </div>
    </PageLayout>
  );
}
