import {
  Check,
  ChevronDown,
  ChevronUp,
  DollarSign,
  FileCheck,
  FileText,
  Mail,
  Settings,
  X,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { defaultAngebotConfig } from '@/../convex/erstellung/angeboteConfig';
import { defaultLieferscheinConfig } from '@/../convex/erstellung/lieferscheineConfig';
import { defaultUebersichtConfig } from '@/../convex/erstellung/uebersichtenConfig';
import { Card, CardContent, CardHeader } from '@/components/_shared/Card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/_shared/Tabs';
import { PageLayout } from '@/components/layout/PageLayout';

type StandardsTab = 'angebote' | 'uebersicht' | 'lieferschein' | 'email';

export function StandardsPage() {
  // Get settings from the new config files
  const angebotSettings = defaultAngebotConfig.settings;
  const angebotEmailSettings = defaultAngebotConfig.emailSettings;
  const angebotEmailTemplate = defaultAngebotConfig.emailTemplate;
  const uebersichtSettings = defaultUebersichtConfig.settings;
  const lieferscheinSettings = defaultLieferscheinConfig.settings;
  const uebersichtEmailSettings = defaultUebersichtConfig.emailSettings;
  const lieferscheinEmailSettings = defaultLieferscheinConfig.emailSettings;

  // Get email templates from the new config files
  const uebersichtEmailTemplate = defaultUebersichtConfig.emailTemplate;
  const lieferscheinEmailTemplate = defaultLieferscheinConfig.emailTemplate;

  // State for selected tab
  const [selectedTab, setSelectedTab] = useState<StandardsTab>('angebote');

  // State for logo images
  const [angebotLogo, setAngebotLogo] = useState<string | null>(null);
  const [uebersichtLogo, setUebersichtLogo] = useState<string | null>(null);
  const [lieferscheinLogo, setLieferscheinLogo] = useState<string | null>(null);

  // State for expandable text fields
  const [isLegalTextExpanded, setIsLegalTextExpanded] = useState(false);
  const [expandedEmailFields, setExpandedEmailFields] = useState<
    Record<string, boolean>
  >({});

  // Load logo images
  useEffect(() => {
    // Load logo paths from configuration
    try {
      setAngebotLogo(angebotSettings.logoPath);
    } catch (_error) {
      setAngebotLogo(null);
    }

    try {
      setUebersichtLogo(uebersichtSettings.logoPath);
    } catch (_error) {
      setUebersichtLogo(null);
    }

    try {
      setLieferscheinLogo(lieferscheinSettings.logoPath);
    } catch (_error) {
      setLieferscheinLogo(null);
    }
  }, [
    angebotSettings.logoPath,
    lieferscheinSettings.logoPath,
    uebersichtSettings.logoPath,
  ]);

  // Helper function to toggle email field expansion
  const toggleEmailFieldExpansion = (fieldName: string) => {
    setExpandedEmailFields((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }));
  };

  // Helper function to render boolean values with icons
  const renderBoolean = (value: boolean) => (
    <span className={value ? 'text-green-400' : 'text-red-400'}>
      {value ? (
        <Check className="mr-1 inline-block h-4 w-4" />
      ) : (
        <X className="mr-1 inline-block h-4 w-4" />
      )}
      {value ? 'Ja' : 'Nein'}
    </span>
  );

  // Setting item component
  const SettingItem = ({
    label,
    value,
    isExpandable = false,
    isExpanded = false,
    onToggleExpand = () => {},
  }: {
    label: string;
    value: React.ReactNode;
    isExpandable?: boolean;
    isExpanded?: boolean;
    onToggleExpand?: () => void;
  }) => {
    const isTextLong = typeof value === 'string' && value.length > 100;
    const shouldBeExpandable = isExpandable || isTextLong;

    return (
      <div className="border-gray-800/40 border-b py-1.5">
        <div className="flex items-center justify-between">
          <span className="w-1/3 font-medium text-blue-400 text-sm">
            {label}
          </span>
          <div className="flex flex-1 items-center justify-between">
            <div
              className={`text-sm ${shouldBeExpandable && !isExpanded ? 'line-clamp-1' : ''}`}
            >
              {value}
            </div>
            {shouldBeExpandable && (
              <button
                aria-label={isExpanded ? 'Collapse' : 'Expand'}
                className="ml-2 flex-shrink-0 text-gray-400 hover:text-gray-300"
                onClick={onToggleExpand}
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <PageLayout
      subtitle="Übersicht der Standardeinstellungen für PDF-Dokumente"
      title="Systemeinstellungen"
    >
      <Card className="overflow-hidden border-0 shadow-lg">
        <Tabs
          className="w-full"
          onValueChange={(value) => setSelectedTab(value as StandardsTab)}
          value={selectedTab}
        >
          <CardHeader className="border-gray-700/50 border-b p-4">
            <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-center">
              <TabsList>
                <TabsTrigger value="angebote">
                  <DollarSign className="mr-1.5 h-4 w-4" /> Angebote
                </TabsTrigger>
                <TabsTrigger value="uebersicht">
                  <FileText className="mr-1.5 h-4 w-4" /> Übersichten
                </TabsTrigger>
                <TabsTrigger value="lieferschein">
                  <FileCheck className="mr-1.5 h-4 w-4" /> Lieferscheine
                </TabsTrigger>
                <TabsTrigger value="email">
                  <Mail className="mr-1.5 h-4 w-4" /> E-Mail
                </TabsTrigger>
              </TabsList>
              <div className="flex items-center gap-1.5 text-gray-400 text-xs">
                <Settings className="h-3 w-3" />
                Dokumenteinstellungen
              </div>
            </div>
          </CardHeader>

          {/* Angebote Tab Content */}
          <TabsContent className="m-0" value="angebote">
            <CardContent className="p-6">
              <div className="flex flex-col">
                {/* Logo at the top */}
                {angebotLogo && (
                  <div className="mb-6 flex justify-center">
                    <div className="relative max-h-28 max-w-full">
                      <img
                        alt="Angebot Logo"
                        className="max-h-28 object-contain"
                        onError={(e) => {
                          e.currentTarget.src =
                            'https://via.placeholder.com/200x100?text=Logo+nicht+gefunden';
                        }}
                        src={angebotLogo}
                      />
                      {!angebotSettings.showLogo && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                          <span className="rounded bg-red-500/80 px-2 py-0.5 text-white text-xs">
                            Deaktiviert
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* E-Mail Einstellungen für Angebote */}
                <div className="mb-4 border-gray-700/50 border-b pb-4">
                  <h3 className="mb-3 flex items-center gap-2 font-semibold text-blue-400 text-lg">
                    <Mail className="h-5 w-5" />
                    E-Mail Einstellungen
                  </h3>
                  <div className="space-y-1.5 rounded-lg border border-gray-700/30 p-4">
                    <SettingItem
                      label="defaultSendToMitarbeiter"
                      value={renderBoolean(
                        angebotEmailSettings.defaultSendToMitarbeiter
                      )}
                    />
                    <SettingItem
                      label="defaultSendToKunde"
                      value={renderBoolean(
                        angebotEmailSettings.defaultSendToKunde
                      )}
                    />
                  </div>
                </div>

                {/* E-Mail Template für Angebote */}
                <div className="mb-4 border-gray-700/50 border-b pb-4">
                  <h3 className="mb-3 flex items-center gap-2 font-semibold text-blue-400 text-lg">
                    <Mail className="h-5 w-5" />
                    E-Mail Template
                  </h3>
                  <div className="space-y-1.5 rounded-lg border border-gray-700/30 p-4">
                    <SettingItem
                      label="subject"
                      value={angebotEmailTemplate.subject}
                    />
                    <SettingItem
                      isExpandable={true}
                      isExpanded={expandedEmailFields['angebot-body']}
                      label="body"
                      onToggleExpand={() =>
                        toggleEmailFieldExpansion('angebot-body')
                      }
                      value={angebotEmailTemplate.body}
                    />
                  </div>
                </div>

                {/* Settings in vertical layout, one per row */}
                <div className="flex flex-col space-y-1.5">
                  {/* General Settings */}
                  <SettingItem
                    label="logoPath"
                    value={angebotSettings.logoPath}
                  />

                  <SettingItem
                    label="fusszeileText"
                    value={angebotSettings.fusszeileText}
                  />

                  <SettingItem
                    label="signatureText"
                    value={angebotSettings.signatureText}
                  />

                  <SettingItem
                    label="includeHeader"
                    value={renderBoolean(angebotSettings.includeHeader)}
                  />

                  <SettingItem
                    label="showLogo"
                    value={renderBoolean(angebotSettings.showLogo)}
                  />

                  <SettingItem
                    label="includeFooter"
                    value={renderBoolean(angebotSettings.includeFooter)}
                  />

                  <SettingItem
                    label="includeSignatureField"
                    value={renderBoolean(angebotSettings.includeSignatureField)}
                  />

                  {/* Legal text with expand/collapse functionality */}
                  <SettingItem
                    isExpandable={true}
                    isExpanded={isLegalTextExpanded}
                    label="legalText"
                    onToggleExpand={() =>
                      setIsLegalTextExpanded(!isLegalTextExpanded)
                    }
                    value={angebotSettings.legalText}
                  />
                </div>
              </div>
            </CardContent>
          </TabsContent>

          {/* Übersichten Tab Content */}
          <TabsContent className="m-0" value="uebersicht">
            <CardContent className="p-6">
              <div className="flex flex-col">
                {/* Logo at the top */}
                {uebersichtLogo && (
                  <div className="mb-6 flex justify-center">
                    <div className="relative max-h-28 max-w-full">
                      <img
                        alt="Übersicht Logo"
                        className="max-h-28 object-contain"
                        onError={(e) => {
                          e.currentTarget.src =
                            'https://via.placeholder.com/200x100?text=Logo+nicht+gefunden';
                        }}
                        src={uebersichtLogo}
                      />
                      {!uebersichtSettings.showLogo && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                          <span className="rounded bg-red-500/80 px-2 py-0.5 text-white text-xs">
                            Deaktiviert
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* E-Mail Einstellungen für Übersichten */}
                <div className="mb-4 border-gray-700/50 border-b pb-4">
                  <h3 className="mb-3 flex items-center gap-2 font-semibold text-blue-400 text-lg">
                    <Mail className="h-5 w-5" />
                    E-Mail Einstellungen
                  </h3>
                  <div className="space-y-1.5 rounded-lg border border-gray-700/30 p-4">
                    <SettingItem
                      label="defaultSendToMitarbeiter"
                      value={renderBoolean(
                        uebersichtEmailSettings.defaultSendToMitarbeiter
                      )}
                    />
                    <SettingItem
                      label="defaultSendToKunde"
                      value={renderBoolean(
                        uebersichtEmailSettings.defaultSendToKunde
                      )}
                    />
                  </div>
                </div>

                {/* E-Mail Template für Übersichten */}
                <div className="mb-4 border-gray-700/50 border-b pb-4">
                  <h3 className="mb-3 flex items-center gap-2 font-semibold text-blue-400 text-lg">
                    <Mail className="h-5 w-5" />
                    E-Mail Template
                  </h3>
                  <div className="space-y-1.5 rounded-lg border border-gray-700/30 p-4">
                    <SettingItem
                      label="subject"
                      value={uebersichtEmailTemplate.subject}
                    />
                    <SettingItem
                      isExpandable={true}
                      isExpanded={expandedEmailFields['uebersicht-body']}
                      label="body"
                      onToggleExpand={() =>
                        toggleEmailFieldExpansion('uebersicht-body')
                      }
                      value={uebersichtEmailTemplate.body}
                    />
                  </div>
                </div>

                {/* Settings in vertical layout, one per row */}
                <div className="flex flex-col space-y-1.5">
                  {/* General Settings */}
                  <SettingItem
                    label="logoPath"
                    value={uebersichtSettings.logoPath}
                  />

                  <SettingItem
                    label="fusszeileText"
                    value={uebersichtSettings.fusszeileText}
                  />

                  <SettingItem
                    label="includeHeader"
                    value={renderBoolean(uebersichtSettings.includeHeader)}
                  />

                  <SettingItem
                    label="showLogo"
                    value={renderBoolean(uebersichtSettings.showLogo)}
                  />

                  <SettingItem
                    label="includeFooter"
                    value={renderBoolean(uebersichtSettings.includeFooter)}
                  />

                  <SettingItem
                    label="includeLeistungsuebersicht"
                    value={renderBoolean(
                      uebersichtSettings.includeLeistungsuebersicht
                    )}
                  />

                  <SettingItem
                    label="includeKontingentuebersicht"
                    value={renderBoolean(
                      uebersichtSettings.includeKontingentuebersicht
                    )}
                  />

                  <SettingItem
                    label="includeSummary"
                    value={renderBoolean(uebersichtSettings.includeSummary)}
                  />
                </div>
              </div>
            </CardContent>
          </TabsContent>

          {/* Lieferscheine Tab Content */}
          <TabsContent className="m-0" value="lieferschein">
            <CardContent className="p-6">
              <div className="flex flex-col">
                {/* Logo at the top */}
                {lieferscheinLogo && (
                  <div className="mb-6 flex justify-center">
                    <div className="relative max-h-28 max-w-full">
                      <img
                        alt="Lieferschein Logo"
                        className="max-h-28 object-contain"
                        onError={(e) => {
                          e.currentTarget.src =
                            'https://via.placeholder.com/200x100?text=Logo+nicht+gefunden';
                        }}
                        src={lieferscheinLogo}
                      />
                      {!lieferscheinSettings.showLogo && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black/60">
                          <span className="rounded bg-red-500/80 px-2 py-0.5 text-white text-xs">
                            Deaktiviert
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* E-Mail Einstellungen für Lieferscheine */}
                <div className="mb-4 border-gray-700/50 border-b pb-4">
                  <h3 className="mb-3 flex items-center gap-2 font-semibold text-blue-400 text-lg">
                    <Mail className="h-5 w-5" />
                    E-Mail Einstellungen
                  </h3>
                  <div className="space-y-1.5 rounded-lg border border-gray-700/30 p-4">
                    <SettingItem
                      label="defaultSendToMitarbeiter"
                      value={renderBoolean(
                        lieferscheinEmailSettings.defaultSendToMitarbeiter
                      )}
                    />
                    <SettingItem
                      label="defaultSendToKunde"
                      value={renderBoolean(
                        lieferscheinEmailSettings.defaultSendToKunde
                      )}
                    />
                  </div>
                </div>

                {/* E-Mail Template für Lieferscheine */}
                <div className="mb-4 border-gray-700/50 border-b pb-4">
                  <h3 className="mb-3 flex items-center gap-2 font-semibold text-blue-400 text-lg">
                    <Mail className="h-5 w-5" />
                    E-Mail Template
                  </h3>
                  <div className="space-y-1.5 rounded-lg border border-gray-700/30 p-4">
                    <SettingItem
                      label="subject"
                      value={lieferscheinEmailTemplate.subject}
                    />
                    <SettingItem
                      isExpandable={true}
                      isExpanded={expandedEmailFields['lieferschein-body']}
                      label="body"
                      onToggleExpand={() =>
                        toggleEmailFieldExpansion('lieferschein-body')
                      }
                      value={lieferscheinEmailTemplate.body}
                    />
                  </div>
                </div>

                {/* Settings in vertical layout, one per row */}
                <div className="flex flex-col space-y-1.5">
                  {/* General Settings */}
                  <SettingItem
                    label="logoPath"
                    value={lieferscheinSettings.logoPath}
                  />

                  <SettingItem
                    label="fusszeileText"
                    value={lieferscheinSettings.fusszeileText}
                  />

                  <SettingItem
                    label="signatureText"
                    value={lieferscheinSettings.signatureText}
                  />

                  <SettingItem
                    label="includeHeader"
                    value={renderBoolean(lieferscheinSettings.includeHeader)}
                  />

                  <SettingItem
                    label="showLogo"
                    value={renderBoolean(lieferscheinSettings.showLogo)}
                  />

                  <SettingItem
                    label="includeFooter"
                    value={renderBoolean(lieferscheinSettings.includeFooter)}
                  />

                  <SettingItem
                    label="includeSignatureField"
                    value={renderBoolean(
                      lieferscheinSettings.includeSignatureField
                    )}
                  />

                  {/* Legal text with expand/collapse functionality */}
                  <SettingItem
                    isExpandable={true}
                    isExpanded={isLegalTextExpanded}
                    label="legalText"
                    onToggleExpand={() =>
                      setIsLegalTextExpanded(!isLegalTextExpanded)
                    }
                    value={lieferscheinSettings.legalText}
                  />
                </div>
              </div>
            </CardContent>
          </TabsContent>

          {/* Email Tab Content */}
          <TabsContent className="m-0" value="email">
            <CardContent className="p-6">
              <div className="flex flex-col space-y-4">
                {/* Email Configuration Section */}
                <div className="border-gray-700/50 border-b pb-4">
                  <h3 className="mb-3 flex items-center gap-2 font-semibold text-blue-400 text-lg">
                    <Settings className="h-5 w-5" />
                    TurboSMTP Konfiguration
                  </h3>
                  <div className="flex flex-col space-y-1.5">
                    <SettingItem
                      label="TURBOSMTP_CONSUMER_KEY"
                      value="Wird über Convex Environment Variables geladen"
                    />

                    <SettingItem
                      label="TURBOSMTP_CONSUMER_SECRET"
                      value="Wird über Convex Environment Variables geladen"
                    />

                    <SettingItem
                      label="EMAIL_FROM"
                      value="Wird über Convex Environment Variables geladen"
                    />

                    <SettingItem
                      label="EMAIL_FROM_NAME"
                      value="Wird über Convex Environment Variables geladen"
                    />

                    <SettingItem
                      label="TURBOSMTP_API_URL"
                      value="Wird über Convex Environment Variables geladen"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </TabsContent>
        </Tabs>
      </Card>
    </PageLayout>
  );
}

export default StandardsPage;
