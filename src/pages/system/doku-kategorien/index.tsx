import { useMutation, useQuery } from 'convex/react';
import {
  AlertTriangle,
  Copy,
  Database,
  EyeOff,
  Info,
  ListChecks,
  MessageSquareText,
  RefreshCw,
  Sparkles,
} from 'lucide-react';
import { useMemo, useRef, useState } from 'react';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import { FIELD_TYPES } from '@/../convex/schema';
import { Button } from '@/components/_shared/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { PageLayout } from '@/components/layout/PageLayout';
import { DokuKategorienTable } from '@/components/system/doku-kategorien/DokuKategorienTable';
import { cn } from '@/lib/utils/cn';

// Define fieldTypeColors
const fieldTypeColors: Record<string, string> = {
  [FIELD_TYPES.TEXT]: 'bg-sky-500/20 text-sky-300 border-sky-500/30',
  [FIELD_TYPES.TEXTAREA]: 'bg-teal-500/20 text-teal-300 border-teal-500/30',
  [FIELD_TYPES.NUMBER]: 'bg-amber-500/20 text-amber-300 border-amber-500/30',
  [FIELD_TYPES.PASSWORD]: 'bg-rose-500/20 text-rose-300 border-rose-500/30',
  [FIELD_TYPES.URL]: 'bg-indigo-500/20 text-indigo-300 border-indigo-500/30',
  [FIELD_TYPES.EMAIL]: 'bg-purple-500/20 text-purple-300 border-purple-500/30',
  [FIELD_TYPES.DATE]: 'bg-lime-500/20 text-lime-300 border-lime-500/30',
  [FIELD_TYPES.CHECKBOX]: 'bg-pink-500/20 text-pink-300 border-pink-500/30',
  [FIELD_TYPES.SELECT]: 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30',
  [FIELD_TYPES.PHONE]: 'bg-orange-500/20 text-orange-300 border-orange-500/30',
  default: 'bg-gray-700 text-gray-200 border-gray-600',
};

export function DokuKategorienPage() {
  const kategorien = useQuery(api.system.dokuKategorien.list) || [];
  const initializeDefaults = useMutation(
    api.system.dokuKategorien.initializeDefaults
  );
  const pageTopRef = useRef<HTMLDivElement>(null);
  const [isSynchronizing, setIsSynchronizing] = useState(false);
  const [showLegend, setShowLegend] = useState(false);

  // Just show all categories sorted by reihenfolge
  const filteredKategorien = useMemo(() => {
    return kategorien;
  }, [kategorien]);

  const handleSynchronize = async () => {
    if (
      window.confirm(
        'Möchten Sie die Kategorien wirklich mit der Konfigurationsdatei synchronisieren? ' +
          'Neue werden hinzugefügt, bestehende Kategorien ggf. aktualisiert. ' +
          'Nicht mehr in der Konfigurationsdatei enthaltene Kategorien werden entfernt, falls sie nicht verwendet werden.'
      )
    ) {
      setIsSynchronizing(true);
      try {
        const result = await initializeDefaults({});
        toast.success(result);
      } catch (error) {
        toast.error(
          `Fehler bei der Synchronisation: ${(error as Error).message}`
        );
      } finally {
        setIsSynchronizing(false);
      }
    }
  };

  // Synchronize button
  const actionButton = (
    <div className="flex items-center gap-2">
      <Button
        className="gap-1"
        onClick={() => setShowLegend(!showLegend)}
        size="sm"
        title={showLegend ? 'Legende ausblenden' : 'Legende anzeigen'}
        variant="outline"
      >
        <Info className="h-4 w-4" />
        {showLegend ? 'Legende ausblenden' : 'Legende anzeigen'}
      </Button>
      <Button
        className="gap-1 border-blue-500/30 bg-blue-500/10 text-blue-400 hover:border-blue-500/50 hover:bg-blue-500/20"
        disabled={isSynchronizing}
        onClick={handleSynchronize}
        size="sm"
        variant="outline"
      >
        <RefreshCw
          className={`h-4 w-4 ${isSynchronizing ? 'animate-spin' : ''}`}
        />
        {isSynchronizing ? 'Synchronisiere...' : 'Synchronisieren'}
      </Button>
    </div>
  );

  // Compact Legend Component
  const LegendSection = () => {
    if (!showLegend) {
      return null;
    }

    return (
      <Card className="fade-in mb-6 animate-in overflow-hidden border-0 shadow-lg duration-200">
        <CardHeader className="border-gray-800 border-b bg-gray-800/40 px-4 pt-3 pb-2">
          <CardTitle className="flex items-center gap-2 font-medium text-base">
            <Info className="h-4 w-4 text-blue-400" />
            Legende: Feldtypen und Eigenschaften
          </CardTitle>
        </CardHeader>
        <CardContent className="p-3">
          <div className="flex flex-col gap-4 md:flex-row">
            {/* Left Column: Field Types */}
            <div className="min-w-0 flex-1 space-y-1">
              <p className="mb-1 font-medium text-gray-300 text-xs uppercase tracking-wider">
                Feldtypen
              </p>
              <div className="grid grid-cols-2 gap-x-3 gap-y-1 sm:grid-cols-3">
                {Object.entries(FIELD_TYPES).map(([key, value]) => (
                  <div className="flex min-w-0 items-center text-xs" key={key}>
                    <span
                      className={cn(
                        'mr-1.5 inline-block h-2.5 w-2.5 flex-shrink-0 rounded-sm border',
                        fieldTypeColors[value as string] ||
                          fieldTypeColors.default
                      )}
                    />
                    <span className="truncate text-gray-300">
                      {(value as string).charAt(0).toUpperCase() +
                        (value as string).slice(1)}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Column: Field Properties */}
            <div className="min-w-0 flex-1 space-y-1 md:border-gray-700/50 md:border-l md:pl-4">
              <p className="mb-1 font-medium text-gray-300 text-xs uppercase tracking-wider">
                Feldeigenschaften
              </p>
              <div className="grid grid-cols-2 gap-x-3 gap-y-1 sm:grid-cols-3">
                {[
                  {
                    Icon: AlertTriangle,
                    char: 'R',
                    text: 'Erforderlich',
                    colorClass: 'bg-red-500/20 text-red-300 border-red-500/40',
                  },
                  {
                    Icon: Copy,
                    char: 'K',
                    text: 'Kopierbar',
                    colorClass:
                      'bg-blue-500/20 text-blue-300 border-blue-500/40',
                  },
                  {
                    Icon: EyeOff,
                    char: 'V',
                    text: 'Versteckt',
                    colorClass:
                      'bg-purple-500/20 text-purple-300 border-purple-500/40',
                  },
                  {
                    Icon: Sparkles,
                    char: 'E',
                    text: 'Extrafeld',
                    colorClass:
                      'bg-green-500/20 text-green-300 border-green-500/40',
                  },
                  {
                    Icon: ListChecks,
                    char: 'O',
                    text: 'Optionen',
                    colorClass:
                      'bg-indigo-500/20 text-indigo-300 border-indigo-500/40',
                  },
                  {
                    Icon: MessageSquareText,
                    char: 'P',
                    text: 'Placeholder',
                    colorClass:
                      'bg-yellow-500/20 text-yellow-300 border-yellow-500/40',
                  },
                ].map((prop) => (
                  <div
                    className="flex min-w-0 items-center text-xs"
                    key={prop.text}
                  >
                    <span
                      className={cn(
                        'mr-1.5 inline-flex h-3.5 w-3.5 flex-shrink-0 items-center justify-center rounded-sm border font-mono text-[10px]',
                        prop.colorClass
                      )}
                    >
                      {prop.char}
                    </span>
                    <span className="truncate text-gray-300">{prop.text}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <PageLayout
      action={actionButton}
      ref={pageTopRef}
      subtitle="Verwalten Sie hier die Kategorien für die Kundendokumentation"
      title="Dokumentationskategorien"
    >
      {/* Compact Legend - conditionally shown */}
      <LegendSection />

      {/* Categories Card */}
      <Card className="overflow-hidden border-0 shadow-lg">
        <CardHeader className="border-gray-800 border-b bg-gray-800/40 px-4 pt-3 pb-2">
          <CardTitle className="flex items-center gap-2 font-medium text-lg">
            <Database className="h-5 w-5 text-blue-400" />
            Kategorien
          </CardTitle>
        </CardHeader>
        <DokuKategorienTable
          kategorien={filteredKategorien.sort(
            (a, b) => a.reihenfolge - b.reihenfolge
          )}
        />
      </Card>
    </PageLayout>
  );
}

export default DokuKategorienPage;
