import { Briefcase, Car, Clock, Euro, Info } from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Skeleton } from '@/components/_shared/Skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableRow,
} from '@/components/_shared/Table';
import { EmptyState } from '@/components/layout/EmptyState';
import { formatCurrency, formatHours } from '@/lib/utils/formatUtils';

interface PerformanceData {
  stunden: number;
  umsatz: number;
  anzahlLeistungen: number;
  anzahlAnfahrten: number;
}

interface DashboardPerformanceTableProps {
  title: string;
  icon: React.ReactNode;
  data: PerformanceData | null;
  isLoading: boolean;
}

// Loading skeleton component
const LoadingSkeleton = () => (
  <Card className="overflow-hidden border-0 bg-gray-800/40 shadow-md">
    <CardHeader className="border-gray-700/50 border-b p-3">
      <Skeleton className="h-5 w-32" />
    </CardHeader>
    <CardContent className="p-0">
      <div className="p-3">
        {[...new Array(4)].map((_, i) => (
          <div className="flex justify-between py-1.5" key={i}>
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-16" />
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
);

// Empty state wrapper component
const EmptyStateWrapper = () => (
  <Card className="overflow-hidden border-0 bg-gray-800/40 shadow-md">
    <CardHeader className="border-gray-700/50 border-b p-3">
      <CardTitle className="flex items-center font-medium text-sm">
        <Info className="mr-1.5 h-4 w-4" />
        Keine Daten
      </CardTitle>
    </CardHeader>
    <CardContent className="p-3">
      <EmptyState
        message="Für den ausgewählten Zeitraum wurden keine Daten gefunden."
        title=""
      />
    </CardContent>
  </Card>
);

export function DashboardPerformanceTable({
  title,
  icon,
  data,
  isLoading,
}: DashboardPerformanceTableProps) {
  if (isLoading) {
    return <LoadingSkeleton />;
  }

  if (!data || (data.stunden === 0 && data.anzahlLeistungen === 0)) {
    return <EmptyStateWrapper />;
  }

  return (
    <Card className="overflow-hidden border-0 bg-gray-800/40 shadow-md">
      <CardHeader className="border-gray-700/50 border-b p-3">
        <CardTitle className="flex items-center font-medium text-sm">
          {icon}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableBody>
            <TableRow className="border-gray-700/50 border-b">
              <TableCell className="px-3 py-2 text-xs">
                <div className="flex items-center gap-1.5">
                  <Clock className="h-3.5 w-3.5 text-blue-400" />
                  <span>Stunden</span>
                </div>
              </TableCell>
              <TableCell className="px-3 py-2 text-right font-medium text-xs">
                {formatHours(data.stunden)}
              </TableCell>
            </TableRow>
            <TableRow className="border-gray-700/50 border-b">
              <TableCell className="px-3 py-2 text-xs">
                <div className="flex items-center gap-1.5">
                  <Euro className="h-3.5 w-3.5 text-green-400" />
                  <span>Umsatz</span>
                </div>
              </TableCell>
              <TableCell className="px-3 py-2 text-right font-medium text-xs">
                {formatCurrency(data.umsatz)}
              </TableCell>
            </TableRow>
            <TableRow className="border-gray-700/50 border-b">
              <TableCell className="px-3 py-2 text-xs">
                <div className="flex items-center gap-1.5">
                  <Briefcase className="h-3.5 w-3.5 text-indigo-400" />
                  <span>Leistungen</span>
                </div>
              </TableCell>
              <TableCell className="px-3 py-2 text-right font-medium text-xs">
                {data.anzahlLeistungen}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="px-3 py-2 text-xs">
                <div className="flex items-center gap-1.5">
                  <Car className="h-3.5 w-3.5 text-orange-400" />
                  <span>Anfahrten</span>
                </div>
              </TableCell>
              <TableCell className="px-3 py-2 text-right font-medium text-xs">
                {data.anzahlAnfahrten}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
