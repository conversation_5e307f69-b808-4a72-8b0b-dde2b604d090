import { useQuery } from 'convex/react';
import { BookO<PERSON>, FileText, Plus } from 'lucide-react';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { api } from '@/../convex/_generated/api';
import { Button } from '@/components/_shared/Button';
import { PageLayout } from '@/components/layout/PageLayout';
import { calculateDateRange, toISODateString } from '@/lib/utils/dateUtils';
import { DashboardRecentActivity } from './DashboardRecentActivity';
import { DashboardTables } from './DashboardTables';
import {
  type DashboardFilter,
  useDashboardAnalytics,
} from './hooks/useDashboardAnalytics';

export function DashboardPage() {
  const allLeistungen = useQuery(api.erstellung.leistung.list) || [];
  const allKontingente = useQuery(api.verwaltung.kontingente.list) || [];
  const allKunden = useQuery(api.verwaltung.kunden.list) || [];

  const isLoading =
    allLeistungen === undefined ||
    allKontingente === undefined ||
    allKunden === undefined;

  const [filter, setFilter] = useState<DashboardFilter>(() => {
    const { startDate, endDate } = calculateDateRange('month');
    return {
      zeitraum: 'month',
      startDatum: toISODateString(startDate),
      endDatum: toISODateString(endDate),
      kundeId: 'all',
      mitarbeiterId: 'all',
    };
  });

  const dashboardDaten = useDashboardAnalytics({
    allLeistungen,
    allKontingente,
    filter,
  });

  return (
    <PageLayout
      action={
        <div className="flex items-center gap-3">
          <Link to="/erstellung/leistung">
            <Button className="gap-1" size="sm" variant="outline">
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Leistung</span>
            </Button>
          </Link>
          <Link to="/erstellung/lieferscheine">
            <Button className="gap-1" size="sm" variant="outline">
              <FileText className="h-4 w-4" />
              <span className="hidden sm:inline">Lieferschein</span>
            </Button>
          </Link>
          <Link to="/kunden/doku">
            <Button className="gap-1" size="sm" variant="outline">
              <BookOpen className="h-4 w-4" />
              <span className="hidden sm:inline">Doku</span>
            </Button>
          </Link>
        </div>
      }
      subtitle="Übersicht Kennzahlen."
      title="Dashboard"
    >
      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="space-y-4 md:col-span-2">
            <DashboardTables
              allKontingente={allKontingente}
              allKunden={allKunden}
              allLeistungen={allLeistungen}
              dashboardDaten={dashboardDaten}
              filter={filter}
              isLoading={isLoading}
              onFilterChange={setFilter}
            />
          </div>
          <div>
            <DashboardRecentActivity filter={filter} isLoading={isLoading} />
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

export default DashboardPage;
