import { useQuery } from 'convex/react';
import { Calendar, Clock, Info, User, Users } from 'lucide-react';
import { useMemo } from 'react';
import { api } from '@/../convex/_generated/api';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Skeleton } from '@/components/_shared/Skeleton';
import { EmptyState } from '@/components/layout/EmptyState';
import { formatDate, formatHours, formatTime } from '@/lib/utils/formatUtils';

interface DashboardRecentActivityProps {
  filter: {
    zeitraum: string;
    startDatum: string;
    endDatum: string;
    kundeId: string;
    mitarbeiterId: string;
  };
  isLoading: boolean;
}

// Loading skeleton component
const LoadingSkeleton = () => (
  <Card className="overflow-hidden border-0 bg-gray-800/40 shadow-md">
    <CardHeader className="border-gray-700/50 border-b p-4">
      <Skeleton className="h-6 w-48" />
    </CardHeader>
    <CardContent className="p-0">
      <div className="space-y-4 p-4">
        {[...new Array(5)].map((_, i) => (
          <div className="space-y-2" key={i}>
            <Skeleton className="h-5 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
);

// Empty state wrapper component
const _EmptyStateWrapper = () => (
  <Card className="h-full border-gray-700 border-dashed bg-gray-800/30">
    <CardContent className="py-12">
      <EmptyState
        icon={<Info className="h-10 w-10" />}
        message="Für den ausgewählten Zeitraum wurden keine Aktivitäten gefunden."
        title="Keine Aktivitäten"
      />
    </CardContent>
  </Card>
);

export function DashboardRecentActivity({
  filter,
  isLoading,
}: DashboardRecentActivityProps) {
  // Fetch all leistungen
  const allLeistungen = useQuery(api.erstellung.leistung.list) || [];

  // Filter and sort leistungen based on the filter criteria
  const recentLeistungen = useMemo(() => {
    if (!allLeistungen) {
      return [];
    }

    // Filter leistungen based on date range and selected filters
    const filteredLeistungen = allLeistungen.filter((leistung) => {
      const leistungDatum = new Date(leistung.startZeit);
      const startDate = new Date(filter.startDatum);
      const endDate = new Date(filter.endDatum);

      // Check if leistung is within date range
      const isInDateRange =
        leistungDatum >= startDate && leistungDatum <= endDate;

      // Check if leistung matches selected kunde
      const matchesKunde =
        filter.kundeId === 'all' || leistung.kundenId === filter.kundeId;

      // Check if leistung matches selected mitarbeiter
      const matchesMitarbeiter =
        filter.mitarbeiterId === 'all' ||
        leistung.mitarbeiterId === filter.mitarbeiterId;

      return isInDateRange && matchesKunde && matchesMitarbeiter;
    });

    // Sort by date (newest first)
    return filteredLeistungen
      .sort((a, b) => b.startZeit - a.startZeit)
      .slice(0, 10); // Get only the 10 most recent
  }, [allLeistungen, filter]);

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <Card className="h-full overflow-hidden border-0 bg-gray-800/40 shadow-md">
      <CardHeader className="border-gray-700/50 border-b p-3">
        <CardTitle className="flex items-center font-medium text-sm">
          <Calendar className="mr-1.5 h-4 w-4" />
          Letzte Aktivitäten
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        {recentLeistungen.length > 0 ? (
          <div className="divide-y divide-gray-700/50">
            {recentLeistungen.map((leistung) => {
              const startDate = new Date(leistung.startZeit);
              const endDate = new Date(leistung.endZeit);

              // Bestimme den Leistungstyp (Remote/Vor-Ort/Vor-Ort (free))
              let leistungsTyp = 'Remote';
              if (leistung.mitAnfahrt) {
                leistungsTyp =
                  leistung.anfahrtskosten > 0 ? 'Vor-Ort' : 'Vor-Ort (free)';
              }

              return (
                <div className="p-3 hover:bg-gray-700/20" key={leistung._id}>
                  <div className="mb-2 flex items-start justify-between">
                    <div className="flex items-center gap-1.5">
                      <Users className="h-3 w-3 text-blue-400" />
                      <span className="font-medium text-gray-200 text-xs">
                        {leistung.kundeName}
                      </span>
                    </div>
                    <div className="text-gray-400 text-xs">
                      {formatDate(startDate)}
                    </div>
                  </div>

                  <div className="mb-1 flex items-center justify-between">
                    <div className="flex items-center gap-1.5">
                      <User className="h-3 w-3 text-gray-400" />
                      <span className="text-gray-300 text-xs">
                        {leistung.mitarbeiterName}
                      </span>
                    </div>
                    <div className="text-gray-400 text-xs">
                      {formatHours(leistung.stunden)}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5">
                      <Clock className="h-3 w-3 text-gray-400" />
                      <span className="text-gray-400 text-xs">
                        {formatTime(startDate)} - {formatTime(endDate)}
                      </span>
                    </div>
                    <div className="text-gray-400 text-xs">{leistungsTyp}</div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="p-4 text-right text-gray-400 text-sm italic">
            Für den ausgewählten Zeitraum wurden keine Daten gefunden.
          </div>
        )}
      </CardContent>
    </Card>
  );
}
