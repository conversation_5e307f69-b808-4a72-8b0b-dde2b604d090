import { FileText, Plus } from 'lucide-react';
import { Button } from '@/components/_shared/Button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import { AngeboteEmptyState } from '@/components/erstellung/angebote/AngeboteEmptyState';
import { AngebotFilterControls } from '@/components/erstellung/angebote/AngebotFilterControls';
import { AngebotRow } from '@/components/erstellung/angebote/AngebotRow';
import { NewAngebotDialog } from '@/components/erstellung/angebote/NewAngebotDialog';
import { PageLayout } from '@/components/layout/PageLayout';
import { StandardDataTable } from '@/components/layout/StandardDataTable';
import { useAngebotePageLogic } from './hooks/useAngebotePageLogic';

export function AngebotePage() {
  const {
    isNewAngebotDialogOpen,
    setIsNewAngebotDialogOpen,
    handleCloseNewAngebotDialog,
    searchTerm,
    setSearchTerm,
    filter,
    expandedRows,
    filteredAngebotGruppen,
    handleFilterChange,
    resetFilters,
    handleDelete,
    toggleExpandRow,
  } = useAngebotePageLogic();

  const actionButton = (
    <Button
      className="gap-1"
      onClick={() => setIsNewAngebotDialogOpen(true)}
      size="sm"
    >
      <Plus className="h-4 w-4" />
      Neues Angebot
    </Button>
  );

  return (
    <PageLayout
      action={actionButton}
      subtitle="Erstellen und verwalten Sie Ihre Angebote"
      title="Angebote"
    >
      <StandardDataTable
        filterSlot={
          <AngebotFilterControls
            filter={filter}
            onFilterChange={handleFilterChange}
            onSearchTermChange={setSearchTerm}
            searchTerm={searchTerm}
          />
        }
        infoSlot={
          <>
            <FileText className="h-3.5 w-3.5 opacity-70" />
            <span>
              {filteredAngebotGruppen.length}{' '}
              {filteredAngebotGruppen.length === 1 ? 'Angebot' : 'Angebote'}
            </span>
          </>
        }
        title="Angebotsübersicht"
      >
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
                <TableHead className="font-medium">Nummer</TableHead>
                <TableHead className="font-medium">Kunde</TableHead>
                <TableHead className="font-medium">Erstellt am</TableHead>
                <TableHead className="font-medium">Status</TableHead>
                <TableHead className="text-right font-medium">
                  Gesamtsumme
                </TableHead>
                <TableHead className="w-24 text-center">Aktionen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAngebotGruppen.length === 0 ? (
                <TableRow>
                  <TableCell
                    className="py-8 text-center text-gray-400"
                    colSpan={6}
                  >
                    <AngeboteEmptyState
                      filterKundeId={filter.kundeId}
                      filterStatus={filter.status}
                      filterZeitraum={filter.zeitraum}
                      onResetFilters={resetFilters}
                      searchTerm={searchTerm}
                    />
                  </TableCell>
                </TableRow>
              ) : (
                filteredAngebotGruppen.map((gruppe) => (
                  <AngebotRow
                    angebot={gruppe.hauptAngebot}
                    isExpanded={expandedRows.has(
                      gruppe.hauptAngebot._id.toString()
                    )}
                    key={gruppe.hauptAngebot._id}
                    korrekturen={gruppe.korrekturen}
                    onDelete={handleDelete}
                    onToggleExpand={() =>
                      toggleExpandRow(gruppe.hauptAngebot._id.toString())
                    }
                    original={gruppe.original}
                  />
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </StandardDataTable>

      <NewAngebotDialog
        isOpen={isNewAngebotDialogOpen}
        onClose={handleCloseNewAngebotDialog}
      />
    </PageLayout>
  );
}

export default AngebotePage;
