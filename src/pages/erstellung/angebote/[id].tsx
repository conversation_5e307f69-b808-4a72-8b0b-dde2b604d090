import { PDFDownloadLink, PDFViewer } from '@react-pdf/renderer';
import { useMutation, useQuery } from 'convex/react';
import {
  AlertTriangle,
  ArrowLeft,
  Download,
  Eye,
  FileText,
  Save,
} from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Link, useLocation, useNavigate, useParams } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import type { AngebotSettings } from '@/../convex/erstellung/angeboteConfig';
import { Button } from '@/components/_shared/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { CompactPositionForm } from '@/components/erstellung/angebote/CompactPositionForm';
import { CreateAngebotCorrectionDialog } from '@/components/erstellung/angebote/CreateAngebotCorrectionDialog';
import { FinalizeAngebotDialog } from '@/components/erstellung/angebote/FinalizeAngebotDialog';
import {
  GrunddatenCard,
  type GrunddatenCardRef,
} from '@/components/erstellung/angebote/GrunddatenCard';
import { AngebotPDFDocument } from '@/components/erstellung/angebote/PDFDocument';
import { PageLayout } from '@/components/layout/PageLayout';
import { formatCurrency } from '@/lib/utils/formatUtils';

export function AngeboteDetailPage() {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const [angebotId, setAngebotId] = useState<Id<'kunden_angebote'> | null>(
    null
  );

  const [_isFormVisible, _setIsFormVisible] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [bemerkung, setBemerkung] = useState<string | undefined>();

  // Dialog states
  const [isCorrectionDialogOpen, setIsCorrectionDialogOpen] = useState(
    location.state?.openCorrectionDialog
  );
  const [isFinalizationDialogOpen, setIsFinalizationDialogOpen] =
    useState(false);

  // Change tracking
  const [hasGrunddatenChanges, setHasGrunddatenChanges] = useState(false);
  const [hasPositionChanges, setHasPositionChanges] = useState(false);
  const [hasBemerkungChanges, setHasBemerkungChanges] = useState(false);

  // Refs for component access
  const grunddatenRef = useRef<GrunddatenCardRef>(null);

  // PDF settings
  const [includeHeaderInPDF, setIncludeHeaderInPDF] = useState(true);
  const [includeFooterInPDF, setIncludeFooterInPDF] = useState(true);
  const [showLogoInPDF, setShowLogoInPDF] = useState(true);
  const [includeLegalTextInPDF, setIncludeLegalTextInPDF] = useState(true);
  const [includeSignatureFieldInPDF, setIncludeSignatureFieldInPDF] =
    useState(true);
  const [processedLogoUrl, setProcessedLogoUrl] = useState<string | undefined>(
    undefined
  );

  // Check if the id is a Convex ID or an angebot number
  // Convex IDs are typically 32 character alphanumeric strings
  // Angebot numbers follow the pattern "*********" or similar
  const isConvexId = id && id.length > 20 && !/^AG\d+/.test(id);
  const isUndefined = id === 'undefined';

  const angebotDataById = useQuery(
    api.erstellung.angebot.get,
    isConvexId && !isUndefined ? { id: id as Id<'kunden_angebote'> } : 'skip'
  );

  const angebotDataByNummer = useQuery(
    api.erstellung.angebot.getByNummer,
    isConvexId ? 'skip' : { nummer: id || '' }
  );

  const angebotData = isConvexId ? angebotDataById : angebotDataByNummer;

  const settings = useQuery(api.system.standards.getSettings, {
    type: 'angebot',
  }) as AngebotSettings | null;

  const updateAngebot = useMutation(api.erstellung.angebot.update);
  const _deleteAngebot = useMutation(api.erstellung.angebot.remove);

  // Update angebotId when data is loaded
  useEffect(() => {
    if (angebotData && !angebotId) {
      setAngebotId(angebotData._id);
    }
  }, [angebotData, angebotId]);

  // Update bemerkung when angebot data is loaded
  useEffect(() => {
    if (angebotData) {
      setBemerkung(angebotData.bemerkung);
    }
  }, [angebotData]);

  // Update bemerkung changes state
  useEffect(() => {
    setHasBemerkungChanges(bemerkung !== angebotData?.bemerkung);
  }, [bemerkung, angebotData?.bemerkung]);

  // Redirect to angebot nummer URL when finalized
  useEffect(() => {
    if (angebotData?.status === 'fertig' && angebotData.nummer && isConvexId) {
      // If we're currently viewing via Convex ID but angebot is finalized with nummer, redirect
      navigate(`/erstellung/angebote/${angebotData.nummer}`, { replace: true });
    }
  }, [angebotData?.status, angebotData?.nummer, isConvexId, navigate]);

  // Load logo from settings
  useEffect(() => {
    if (settings?.logoPath) {
      try {
        setProcessedLogoUrl(settings.logoPath);
        const img = new Image();
        img.onerror = () => setProcessedLogoUrl(undefined);
        img.src = settings.logoPath;
      } catch (_error) {
        setProcessedLogoUrl(undefined);
      }
    }
  }, [settings]);

  const _handleUpdateBemerkung = useCallback(async () => {
    if (!angebotId) {
      toast.error('Angebot-ID nicht verfügbar.');
      return;
    }

    try {
      await updateAngebot({
        id: angebotId,
        bemerkung: bemerkung || undefined,
      });
      toast.success('Bemerkung erfolgreich aktualisiert.');
    } catch (_error) {
      toast.error('Fehler beim Aktualisieren der Bemerkung.');
    }
  }, [angebotId, bemerkung, updateAngebot]);

  const handleFormSubmit = async (formData: any) => {
    if (!angebotId) {
      return;
    }
    try {
      // Include calculated totals from the form
      const updateData = {
        ...formData,
        // Update totals with discount calculations if provided
        ...(formData.gesamtsummeNetto && {
          gesamtsummeNetto: formData.gesamtsummeNetto,
        }),
        ...(formData.gesamtsummeBrutto && {
          gesamtsummeBrutto: formData.gesamtsummeBrutto,
        }),
      };

      await updateAngebot({ id: angebotId, ...updateData });
      toast.success('Angebot erfolgreich aktualisiert');
    } catch (error: any) {
      toast.error(`Fehler beim Speichern: ${error.message}`);
    }
  };

  const handleGrunddatenUpdate = async (data: any) => {
    if (!angebotId) {
      return;
    }
    await updateAngebot({ id: angebotId, ...data });
  };

  // Save all pending changes
  const handleSaveAll = async () => {
    if (!angebotId) {
      return;
    }

    try {
      const promises = [];

      // Save bemerkung if changed
      if (hasBemerkungChanges) {
        promises.push(
          updateAngebot({
            id: angebotId,
            bemerkung: bemerkung || undefined,
          })
        );
      }

      // Save grunddaten if changed
      if (hasGrunddatenChanges && grunddatenRef.current) {
        promises.push(grunddatenRef.current.save());
      }

      // Trigger form submit for positions if there are changes
      if (hasPositionChanges) {
        const form = document.getElementById(
          'position-form'
        ) as HTMLFormElement;
        if (form) {
          form.dispatchEvent(
            new Event('submit', { cancelable: true, bubbles: true })
          );
        }
      }

      // Wait for all saves to complete
      await Promise.all(promises);

      // Reset change states
      if (hasBemerkungChanges) {
        setHasBemerkungChanges(false);
      }

      toast.success('Alle Änderungen erfolgreich gespeichert');
    } catch (_error) {
      toast.error('Fehler beim Speichern der Änderungen');
    }
  };

  // Check if there are any unsaved changes
  const hasAnyChanges =
    hasGrunddatenChanges || hasPositionChanges || hasBemerkungChanges;

  if (!angebotData) {
    return (
      <PageLayout
        action={
          <Link to="/erstellung/angebote">
            <Button variant="outline">Zurück zur Übersicht</Button>
          </Link>
        }
        subtitle="Bitte warten Sie einen Moment"
        title="Angebot wird geladen..."
      >
        <div className="flex h-64 items-center justify-center">
          <div className="h-10 w-10 animate-spin rounded-full border-blue-500 border-b-2" />
        </div>
      </PageLayout>
    );
  }

  // Create PDF document instance
  const pdfKey = `pdf-${angebotData.nummer || angebotData._id}-${includeHeaderInPDF}-${includeFooterInPDF}-${showLogoInPDF}-${includeLegalTextInPDF}-${includeSignatureFieldInPDF}-${Date.now()}`;

  const documentInstance = settings ? (
    <AngebotPDFDocument
      angebot={angebotData}
      firmenFusszeileText={settings.fusszeileText}
      firmenName="innov8-IT"
      formatCurrency={formatCurrency}
      includeFooter={includeFooterInPDF}
      includeHeader={includeHeaderInPDF}
      includeLegalText={includeLegalTextInPDF}
      includeSignatureField={includeSignatureFieldInPDF}
      key={pdfKey}
      legalText={settings.legalText}
      logoUrl={processedLogoUrl}
      showLogo={showLogoInPDF}
      signatureText={settings.signatureText}
    />
  ) : null;

  // PDF filename logic: drafts = AG<ID>.pdf, finalized = AG00001.pdf
  const pdfFileName =
    angebotData.status === 'entwurf'
      ? `AG${angebotData._id}.pdf`
      : `${angebotData.nummer}.pdf`;

  return (
    <PageLayout
      action={
        <div className="flex items-center gap-2">
          <Link to="/erstellung/angebote">
            <Button className="gap-2" variant="outline">
              <ArrowLeft className="h-4 w-4" />
              Zurück zur Übersicht
            </Button>
          </Link>
        </div>
      }
      subtitle={`Kunde: ${angebotData.kundeName} | Erstellt am: ${angebotData.erstelltAmFormatiert}`}
      title={`Angebot: ${angebotData.nummer || 'Entwurf'}`}
    >
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Left Column: Controls and Options */}
        <div className="space-y-6 lg:col-span-1">
          {/* Status Card */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="p-4">
              <CardTitle className="font-medium text-base">Status</CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="space-y-4">
                {angebotData.status === 'entwurf' ? (
                  <div className="flex items-center text-blue-400">
                    <FileText className="mr-2 h-5 w-5" />
                    <span>
                      {angebotData.istKorrektur
                        ? 'Korrektur-Entwurf'
                        : 'Angebot-Entwurf'}
                    </span>
                  </div>
                ) : angebotData.istKorrektur ? (
                  <div className="flex items-center text-orange-400">
                    <AlertTriangle className="mr-2 h-5 w-5" />
                    <span>Dies ist eine Korrektur</span>
                  </div>
                ) : angebotData.hatKorrektur ? (
                  <div className="flex items-center text-orange-400">
                    <AlertTriangle className="mr-2 h-5 w-5" />
                    <span>Zu diesem Angebot existieren Korrekturen</span>
                  </div>
                ) : (
                  <div className="flex items-center text-green-400">
                    <FileText className="mr-2 h-5 w-5" />
                    <span>Aktives Angebot</span>
                  </div>
                )}

                {/* Bemerkung */}
                <div className="mt-4">
                  <h3 className="mb-1 font-medium text-sm">Bemerkung:</h3>
                  {angebotData.status === 'entwurf' ? (
                    <textarea
                      className="w-full rounded-md border bg-gray-800 p-2 text-sm text-white"
                      onChange={(e) => setBemerkung(e.target.value)}
                      placeholder="Bemerkung hinzufügen (optional)"
                      rows={3}
                      value={bemerkung || ''}
                    />
                  ) : angebotData.bemerkung ? (
                    <p className="text-gray-400 text-sm">
                      {angebotData.bemerkung}
                    </p>
                  ) : (
                    <p className="text-gray-400 text-sm italic">
                      Keine Bemerkung vorhanden
                    </p>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="mt-4 flex flex-wrap gap-2">
                  {/* Download Button */}
                  {documentInstance && (
                    <PDFDownloadLink
                      document={documentInstance}
                      fileName={pdfFileName}
                      key={`download-${pdfKey}`}
                    >
                      {({ loading }) => (
                        <Button
                          className="h-10 w-10 p-0"
                          disabled={loading}
                          title="PDF herunterladen"
                          variant="outline"
                        >
                          <Download className="h-5 w-5" />
                        </Button>
                      )}
                    </PDFDownloadLink>
                  )}

                  {/* Vorschau Toggle Button - always show */}
                  <Button
                    className="h-10 w-10 p-0"
                    onClick={() => setShowPreview(!showPreview)}
                    title={
                      showPreview ? 'Vorschau ausblenden' : 'Vorschau anzeigen'
                    }
                    variant="outline"
                  >
                    <Eye className="h-5 w-5" />
                  </Button>

                  {/* Finalisieren Button - nur für Entwürfe */}
                  {angebotData.status === 'entwurf' && (
                    <Button
                      className="h-10 w-10 bg-green-600 p-0 hover:bg-green-700"
                      onClick={() => setIsFinalizationDialogOpen(true)}
                      title={
                        angebotData.istKorrektur
                          ? 'Korrektur finalisieren'
                          : 'Angebot finalisieren'
                      }
                    >
                      <FileText className="h-5 w-5" />
                    </Button>
                  )}

                  {/* Korrektur Button - für alle finalisierten Angebote */}
                  {angebotData.status === 'fertig' && (
                    <Button
                      className="h-10 w-10 p-0"
                      onClick={() => setIsCorrectionDialogOpen(true)}
                      title="Korrektur erstellen"
                      variant="outline"
                    >
                      <AlertTriangle className="h-5 w-5" />
                    </Button>
                  )}

                  {/* Spacer and Unified Save Button */}
                  <div className="flex-1" />
                  {angebotData.status === 'entwurf' && hasAnyChanges && (
                    <Button
                      className="h-10 w-10 bg-green-600 p-0 hover:bg-green-700"
                      onClick={handleSaveAll}
                      title="Änderungen speichern"
                    >
                      <Save className="h-5 w-5" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Grunddaten Card */}
          <GrunddatenCard
            angebot={{
              kundenId: angebotData.kundenId,
              kundeName: angebotData.kundeName,
              gueltigBis: angebotData.gueltigBis,
              status: angebotData.status,
            }}
            onChangesDetected={setHasGrunddatenChanges}
            onUpdate={handleGrunddatenUpdate}
            ref={grunddatenRef}
          />

          {/* PDF Settings Card */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="p-4">
              <CardTitle className="font-medium text-base">
                PDF-Einstellungen
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <label className="text-sm" htmlFor="includeHeader">
                    Header anzeigen
                  </label>
                  <input
                    checked={includeHeaderInPDF}
                    className="h-4 w-4"
                    id="includeHeader"
                    onChange={(e) => setIncludeHeaderInPDF(e.target.checked)}
                    type="checkbox"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label
                    className={`text-sm ${processedLogoUrl ? '' : 'opacity-50'}`}
                    htmlFor="showLogo"
                  >
                    Logo anzeigen
                    {!processedLogoUrl &&
                      ' (Logo wird aus der Konfiguration geladen)'}
                  </label>
                  <input
                    checked={showLogoInPDF}
                    className="h-4 w-4"
                    disabled={!processedLogoUrl}
                    id="showLogo"
                    onChange={(e) => setShowLogoInPDF(e.target.checked)}
                    type="checkbox"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm" htmlFor="includeFooter">
                    Footer anzeigen
                  </label>
                  <input
                    checked={includeFooterInPDF}
                    className="h-4 w-4"
                    id="includeFooter"
                    onChange={(e) => setIncludeFooterInPDF(e.target.checked)}
                    type="checkbox"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm" htmlFor="includeLegalText">
                    Rechtlichen Hinweis anzeigen
                  </label>
                  <input
                    checked={includeLegalTextInPDF}
                    className="h-4 w-4"
                    id="includeLegalText"
                    onChange={(e) => setIncludeLegalTextInPDF(e.target.checked)}
                    type="checkbox"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm" htmlFor="includeSignatureField">
                    Unterschriftsfeld anzeigen
                  </label>
                  <input
                    checked={includeSignatureFieldInPDF}
                    className="h-4 w-4"
                    id="includeSignatureField"
                    onChange={(e) =>
                      setIncludeSignatureFieldInPDF(e.target.checked)
                    }
                    type="checkbox"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column: PDF Preview or Form */}
        <div className="lg:col-span-2">
          {showPreview ? (
            <Card className="h-[calc(100vh-12rem)] border-0 shadow-lg">
              {typeof window !== 'undefined' && documentInstance && (
                <PDFViewer
                  className="rounded-md"
                  height="100%"
                  key={pdfKey}
                  showToolbar={true}
                  width="100%"
                >
                  {documentInstance}
                </PDFViewer>
              )}
            </Card>
          ) : (
            <div className="space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="font-medium text-lg text-white">
                    Angebot bearbeiten
                  </h2>
                  <p className="text-gray-400 text-sm">
                    Ändern Sie die Positionen des Angebots
                  </p>
                </div>
              </div>

              {angebotData.status === 'entwurf' ? (
                <CompactPositionForm
                  formId="position-form"
                  initialData={{ positionen: angebotData.positionen }}
                  isSubmitting={false}
                  onChangesDetected={setHasPositionChanges}
                  onSubmit={handleFormSubmit}
                />
              ) : (
                <Card className="border-0 shadow-lg">
                  <CardHeader className="p-4 pb-2">
                    <CardTitle className="font-medium text-base">
                      Angebotspositionen
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <div className="space-y-4">
                      {angebotData.positionen.map((position, index) => (
                        <div
                          className="rounded-lg border border-gray-700 bg-gray-800/50 p-4"
                          key={position.id}
                        >
                          <div className="grid gap-3 text-sm md:grid-cols-5">
                            <div>
                              <span className="text-gray-400 text-xs uppercase tracking-wide">
                                Position {index + 1}
                              </span>
                              <p className="font-medium text-white">
                                {position.titel}
                              </p>
                              {position.beschreibung && (
                                <p className="mt-1 text-gray-400 text-xs">
                                  {position.beschreibung}
                                </p>
                              )}
                            </div>
                            <div>
                              <span className="text-gray-400 text-xs uppercase tracking-wide">
                                Menge
                              </span>
                              <p className="text-white">{position.menge}</p>
                            </div>
                            <div>
                              <span className="text-gray-400 text-xs uppercase tracking-wide">
                                Einheit
                              </span>
                              <p className="text-white">{position.einheit}</p>
                            </div>
                            <div>
                              <span className="text-gray-400 text-xs uppercase tracking-wide">
                                Einzelpreis
                              </span>
                              <p className="text-white">
                                {formatCurrency(position.einzelpreis)}
                              </p>
                            </div>
                            <div>
                              <span className="text-gray-400 text-xs uppercase tracking-wide">
                                Gesamt
                              </span>
                              <p className="font-medium text-white">
                                {formatCurrency(
                                  position.menge * position.einzelpreis
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}

                      <div className="rounded-lg border border-gray-600 bg-gray-800/30 p-4">
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Zwischensumme (Netto)</span>
                            <span>
                              {formatCurrency(angebotData.gesamtsummeNetto)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Umsatzsteuer (19%)</span>
                            <span>
                              {formatCurrency(
                                angebotData.gesamtsummeBrutto -
                                  angebotData.gesamtsummeNetto
                              )}
                            </span>
                          </div>
                          <div className="flex justify-between border-gray-600 border-t pt-2 font-bold text-base">
                            <span>Gesamtsumme (Brutto)</span>
                            <span>
                              {formatCurrency(angebotData.gesamtsummeBrutto)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Dialogs */}
      {isCorrectionDialogOpen && angebotId && (
        <CreateAngebotCorrectionDialog
          isOpen={isCorrectionDialogOpen}
          onClose={() => setIsCorrectionDialogOpen(false)}
          originalId={angebotId}
        />
      )}

      {isFinalizationDialogOpen && angebotId && (
        <FinalizeAngebotDialog
          angebotId={angebotId}
          isCorrection={angebotData.istKorrektur}
          isOpen={isFinalizationDialogOpen}
          onClose={() => setIsFinalizationDialogOpen(false)}
        />
      )}
    </PageLayout>
  );
}

export default AngeboteDetailPage;
