import { PDFDownloadLink, PDFViewer, pdf } from '@react-pdf/renderer';
import { useQuery } from 'convex/react';
import { Download, Eye, Mail } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { defaultUebersichtConfig } from '@/../convex/erstellung/uebersichtenConfig';
import { Button } from '@/components/_shared/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Checkbox } from '@/components/_shared/Checkbox';
import { Label } from '@/components/_shared/Label';
import KontingentList from '@/components/erstellung/uebersicht/KontingentList';
import LeistungList from '@/components/erstellung/uebersicht/LeistungList';
import { PDFDocumentComponent } from '@/components/erstellung/uebersicht/PDFDocument';
import SummarySection from '@/components/erstellung/uebersicht/SummarySection';
import UebersichtForm from '@/components/erstellung/uebersicht/UebersichtForm';
import { PageLayout } from '@/components/layout/PageLayout';
import { EmailDialog } from '@/components/system/email/EmailDialog';
import { formatDateRangeDE } from '@/lib/utils/dateUtils';
import {
  formatCurrency,
  formatDate,
  formatHours,
  formatTime,
} from '@/lib/utils/formatUtils';
import type { SelectedItems, UebersichtFormState } from './types';

export function UebersichtPage() {
  const [formState, setFormState] = useState<UebersichtFormState>({
    kundeId: '',
    dateRange: 'thisMonth',
    startDatum: '',
    endDatum: '',
  });

  useEffect(() => {
    const now = new Date();
    const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    setFormState((prev) => ({
      ...prev,
      startDatum: startDate.toISOString().split('T')[0],
      endDatum: endDate.toISOString().split('T')[0],
    }));
  }, []);

  const [selectedItems, setSelectedItems] = useState<SelectedItems>({
    leistungen: [],
    kontingente: [],
  });

  // Get settings from the config file
  const uebersichtSettings = defaultUebersichtConfig.settings;

  // Initialize states with defaults from config
  const [includeSummaryInPDF, setIncludeSummaryInPDF] = useState(
    uebersichtSettings.includeSummary
  );
  const [includeLeistungsuebersichtInPDF, setIncludeLeistungsuebersichtInPDF] =
    useState(uebersichtSettings.includeLeistungsuebersicht);
  const [
    includeKontingentuebersichtInPDF,
    setIncludeKontingentuebersichtInPDF,
  ] = useState(uebersichtSettings.includeKontingentuebersicht);
  const [includeHeaderInPDF, setIncludeHeaderInPDF] = useState(
    uebersichtSettings.includeHeader
  );
  const [includeFooterInPDF, setIncludeFooterInPDF] = useState(
    uebersichtSettings.includeFooter
  );
  const [showLogoInPDF, setShowLogoInPDF] = useState(
    uebersichtSettings.showLogo
  );
  const [processedLogoUrl, setProcessedLogoUrl] = useState<string | undefined>(
    undefined
  );

  const [hasSearched, setHasSearched] = useState(false);
  const [showPreview, setShowPreview] = useState(false); // State to toggle PDF preview
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [pdfBase64, setPdfBase64] = useState<string>('');

  const kunden = useQuery(api.verwaltung.kunden.list) || [];

  // Load logo from the path in config
  useEffect(() => {
    // Get the logo path from the config
    const logoPath = uebersichtSettings.logoPath;

    try {
      // Set the logo URL from configuration
      setProcessedLogoUrl(logoPath);

      // Validate logo URL by attempting to load it
      const img = new Image();
      img.onerror = () => {
        // Fallback: disable logo if loading fails
        setProcessedLogoUrl(undefined);
      };
      img.src = logoPath;
    } catch (_error) {
      setProcessedLogoUrl(undefined);
    }
  }, [uebersichtSettings.logoPath]);
  const selectedKunde = useMemo(
    () => kunden.find((kunde) => kunde._id === formState.kundeId),
    [kunden, formState.kundeId]
  );

  const allLeistungen = useQuery(api.erstellung.leistung.list) || [];
  const filteredLeistungen = useMemo(() => {
    if (!(formState.kundeId && formState.startDatum && formState.endDatum)) {
      return [];
    }
    const startDate = new Date(formState.startDatum);
    startDate.setHours(0, 0, 0, 0);
    const startTimestamp = startDate.getTime();
    const endDate = new Date(formState.endDatum);
    endDate.setHours(23, 59, 59, 999);
    const endTimestamp = endDate.getTime();
    return allLeistungen
      .filter(
        (l) =>
          l.kundenId === formState.kundeId &&
          l.startZeit >= startTimestamp &&
          l.startZeit <= endTimestamp
      )
      .sort((a, b) => {
        // Sortiere nach: 1. Datum (startZeit), 2. Startzeit (startZeit), 3. Mitarbeiter
        const dateA = new Date(a.startZeit);
        const dateB = new Date(b.startZeit);

        // Vergleiche zuerst das Datum (ohne Zeit)
        const dayA = new Date(
          dateA.getFullYear(),
          dateA.getMonth(),
          dateA.getDate()
        ).getTime();
        const dayB = new Date(
          dateB.getFullYear(),
          dateB.getMonth(),
          dateB.getDate()
        ).getTime();

        if (dayA !== dayB) {
          return dayA - dayB; // Sortiere nach Datum aufsteigend
        }

        // Wenn das Datum gleich ist, sortiere nach Startzeit
        if (a.startZeit !== b.startZeit) {
          return a.startZeit - b.startZeit; // Sortiere nach Startzeit aufsteigend
        }

        // Wenn Datum und Startzeit gleich sind, sortiere nach Mitarbeiter
        return a.mitarbeiterName.localeCompare(b.mitarbeiterName);
      });
  }, [
    allLeistungen,
    formState.kundeId,
    formState.startDatum,
    formState.endDatum,
  ]);

  const allKontingente = useQuery(api.verwaltung.kontingente.list) || [];
  const filteredKontingente = useMemo(() => {
    if (!(formState.kundeId && formState.startDatum && formState.endDatum)) {
      return [];
    }
    const startDate = new Date(formState.startDatum);
    startDate.setHours(0, 0, 0, 0);
    const startTimestamp = startDate.getTime();
    const endDate = new Date(formState.endDatum);
    endDate.setHours(23, 59, 59, 999);
    const endTimestamp = endDate.getTime();
    return allKontingente.filter(
      (k) =>
        k.kundenId === formState.kundeId &&
        ((k.startDatum <= endTimestamp && k.endDatum >= startTimestamp) ||
          (k.istAktiv &&
            k.startDatum <= endTimestamp &&
            k.endDatum >= startTimestamp))
    );
  }, [
    allKontingente,
    formState.kundeId,
    formState.startDatum,
    formState.endDatum,
  ]);

  const handleFormChange = (key: keyof UebersichtFormState, value: any) => {
    setFormState((prev) => ({ ...prev, [key]: value }));
    setHasSearched(false); // Reset search state if form changes
    setShowPreview(false); // Hide preview if form changes
  };

  const handleFormSubmit = useCallback(() => {
    if (!formState.kundeId) {
      toast.error('Bitte wählen Sie einen Kunden aus.');
      return;
    }
    const leistungenIds = filteredLeistungen.map((l) => l._id);
    const kontingenteIds = filteredKontingente.map((k) => k._id);
    setSelectedItems({
      leistungen: leistungenIds,
      kontingente: kontingenteIds,
    });
    setHasSearched(true);
    setShowPreview(false); // Initially, don't show preview, only after "Daten laden" and then "Vorschau anzeigen"
  }, [formState.kundeId, filteredLeistungen, filteredKontingente]);

  const handleLeistungSelectionChange = useCallback(
    (id: Id<'kunden_leistungen'>, selected: boolean) => {
      setSelectedItems((prev) => ({
        ...prev,
        leistungen: selected
          ? [...prev.leistungen, id]
          : prev.leistungen.filter((lId) => lId !== id),
      }));
    },
    []
  );

  const handleKontingentSelectionChange = useCallback(
    (id: Id<'kunden_kontingente'>, selected: boolean) => {
      setSelectedItems((prev) => ({
        ...prev,
        kontingente: selected
          ? [...prev.kontingente, id]
          : prev.kontingente.filter((kId) => kId !== id),
      }));
    },
    []
  );

  const handleSelectAllLeistungen = useCallback(
    (selected: boolean) => {
      setSelectedItems((prev) => ({
        ...prev,
        leistungen: selected ? filteredLeistungen.map((l) => l._id) : [],
      }));
    },
    [filteredLeistungen]
  );

  const handleSelectAllKontingente = useCallback(
    (selected: boolean) => {
      setSelectedItems((prev) => ({
        ...prev,
        kontingente: selected ? filteredKontingente.map((k) => k._id) : [],
      }));
    },
    [filteredKontingente]
  );

  const selectedLeistungenData = useMemo(
    () =>
      filteredLeistungen.filter((l) =>
        selectedItems.leistungen.includes(l._id)
      ),
    [filteredLeistungen, selectedItems.leistungen]
  );

  const selectedKontingenteData = useMemo(
    () =>
      filteredKontingente.filter((k) =>
        selectedItems.kontingente.includes(k._id)
      ),
    [filteredKontingente, selectedItems.kontingente]
  );

  const formatDateRange = () =>
    formatDateRangeDE(formState.startDatum, formState.endDatum);

  // Include a timestamp in the key to ensure re-rendering when checkbox changes
  const pdfKey = `pdf-${formState.kundeId}-${formState.startDatum}-${formState.endDatum}-${selectedItems.leistungen.length}-${selectedItems.kontingente.length}-${includeSummaryInPDF}-${includeLeistungsuebersichtInPDF}-${includeKontingentuebersichtInPDF}-${includeHeaderInPDF}-${includeFooterInPDF}-${showLogoInPDF}-${Date.now()}`;

  const documentInstance = selectedKunde ? (
    <PDFDocumentComponent
      firmenFusszeileText={uebersichtSettings.fusszeileText} // Ensure re-creation on data change for PDFDownloadLink
      firmenName="innov8-IT"
      formatCurrency={formatCurrency} // Use company name directly
      formatDate={formatDate} // Pass processed logo URL
      formatHours={formatHours} // Pass showLogo state directly
      formatTime={formatTime}
      includeFooter={includeFooterInPDF}
      includeHeader={includeHeaderInPDF}
      includeKontingentuebersicht={includeKontingentuebersichtInPDF}
      includeLeistungsuebersicht={includeLeistungsuebersichtInPDF}
      includeSummary={includeSummaryInPDF}
      key={pdfKey}
      kontingente={selectedKontingenteData}
      kundeName={selectedKunde.name}
      leistungen={selectedLeistungenData}
      logoUrl={processedLogoUrl}
      showLogo={showLogoInPDF}
      // Pass settings from config
      zeitraum={formatDateRange()}
    />
  ) : null;

  const pdfFileName = selectedKunde
    ? `Uebersicht_${selectedKunde.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`
    : 'Uebersicht.pdf';

  // Function to generate PDF as Base64 for email
  const generatePdfBase64 = async () => {
    if (!documentInstance) {
      return '';
    }
    try {
      const blob = await pdf(documentInstance).toBlob();
      const buffer = await blob.arrayBuffer();
      const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
      return base64;
    } catch (_error) {
      toast.error('Fehler beim Generieren der PDF');
      return '';
    }
  };

  // Function to handle email button click
  const handleEmailClick = async () => {
    const base64 = await generatePdfBase64();
    if (base64) {
      setPdfBase64(base64);
      setShowEmailDialog(true);
    }
  };

  return (
    <PageLayout
      subtitle="Interaktive Erstellung von Leistungs- und Kontingentübersichten"
      title="Übersicht Erstellung"
    >
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Left Column: Controls and Options */}
        <div className="space-y-6 lg:col-span-1">
          <Card className="border-0 shadow-lg">
            <CardHeader className="p-4">
              <CardTitle className="font-medium text-base">
                1. Daten auswählen
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <UebersichtForm
                buttonLabel="Daten für Übersicht laden"
                formState={formState}
                onFormChange={handleFormChange}
                onSubmit={handleFormSubmit}
              />
            </CardContent>
          </Card>

          {hasSearched && selectedKunde && (
            <Card className="border-0 shadow-lg">
              <CardHeader className="p-4">
                <CardTitle className="font-medium text-base">
                  2. PDF-Inhalt konfigurieren
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 p-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={includeHeaderInPDF}
                    id="includeHeader"
                    onCheckedChange={(c) => setIncludeHeaderInPDF(!!c)}
                  />
                  <Label className="text-sm" htmlFor="includeHeader">
                    Header einblenden
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={showLogoInPDF}
                    disabled={!processedLogoUrl}
                    id="showLogoInPDF"
                    onCheckedChange={(c) => {
                      setShowLogoInPDF(!!c);
                      // Force PDF preview to update by toggling and restoring showPreview
                      if (showPreview) {
                        setShowPreview(false);
                        setTimeout(() => setShowPreview(true), 50);
                      }
                    }}
                  />
                  <Label
                    className={`text-sm ${processedLogoUrl ? '' : 'opacity-50'}`}
                    htmlFor="showLogoInPDF"
                  >
                    Firmenlogo im Header anzeigen
                    {!processedLogoUrl &&
                      ' (Logo wird aus der Konfiguration geladen)'}
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={includeLeistungsuebersichtInPDF}
                    disabled={selectedLeistungenData.length === 0}
                    id="includeLeistungsuebersicht"
                    onCheckedChange={(c) =>
                      setIncludeLeistungsuebersichtInPDF(!!c)
                    }
                  />
                  <Label
                    className={`text-sm ${selectedLeistungenData.length === 0 ? 'opacity-50' : ''}`}
                    htmlFor="includeLeistungsuebersicht"
                  >
                    Leistungsübersicht
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={includeKontingentuebersichtInPDF}
                    disabled={selectedKontingenteData.length === 0}
                    id="includeKontingentuebersicht"
                    onCheckedChange={(c) =>
                      setIncludeKontingentuebersichtInPDF(!!c)
                    }
                  />
                  <Label
                    className={`text-sm ${selectedKontingenteData.length === 0 ? 'opacity-50' : ''}`}
                    htmlFor="includeKontingentuebersicht"
                  >
                    Kontingentübersicht
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={includeSummaryInPDF}
                    id="includeSummary"
                    onCheckedChange={(c) => setIncludeSummaryInPDF(!!c)}
                  />
                  <Label className="text-sm" htmlFor="includeSummary">
                    Zusammenfassung
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={includeFooterInPDF}
                    id="includeFooter"
                    onCheckedChange={(c) => setIncludeFooterInPDF(!!c)}
                  />
                  <Label className="text-sm" htmlFor="includeFooter">
                    Fußzeile einblenden
                  </Label>
                </div>
                <div className="flex space-x-2 border-gray-700/50 border-t pt-3">
                  <Button
                    className="h-10 w-10 p-0"
                    onClick={() => setShowPreview(!showPreview)}
                    title={
                      showPreview ? 'Vorschau ausblenden' : 'Vorschau anzeigen'
                    }
                    variant="outline"
                  >
                    <Eye className="h-5 w-5" />
                  </Button>
                  {documentInstance && (
                    <PDFDownloadLink
                      document={documentInstance}
                      fileName={pdfFileName}
                      key={`download-${pdfKey}`}
                    >
                      {({ loading }) => (
                        <Button
                          className="h-10 w-10 p-0"
                          disabled={loading}
                          title="PDF herunterladen"
                        >
                          <Download className="h-5 w-5" />
                        </Button>
                      )}
                    </PDFDownloadLink>
                  )}
                  {documentInstance && (
                    <Button
                      className="h-10 w-10 p-0"
                      onClick={handleEmailClick}
                      title="Per E-Mail versenden"
                      variant="outline"
                    >
                      <Mail className="h-5 w-5" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column: PDF Preview */}
        <div className="lg:col-span-2">
          {hasSearched && showPreview && selectedKunde && documentInstance ? (
            <Card className="h-[calc(100vh-12rem)] border-0 shadow-lg">
              {/* Ensure PDFViewer is only mounted on client */}
              {typeof window !== 'undefined' && (
                <PDFViewer
                  className="rounded-md" // Force re-render on data change
                  height="100%"
                  key={pdfKey}
                  showToolbar={true}
                  width="100%"
                >
                  {documentInstance}
                </PDFViewer>
              )}
            </Card>
          ) : hasSearched ? (
            <Card className="flex h-[calc(100vh-12rem)] items-center justify-center border-0 bg-gray-800/30 shadow-lg">
              <div className="text-center text-gray-400">
                <Eye className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p className="font-medium">PDF-Vorschau</p>
                <p className="text-sm">
                  Klicken Sie auf "Vorschau", um das Dokument zu sehen.
                </p>
              </div>
            </Card>
          ) : (
            <Card className="flex h-[calc(100vh-12rem)] items-center justify-center border-0 bg-gray-800/30 shadow-lg">
              <div className="text-center text-gray-400">
                <p>
                  Bitte laden Sie zuerst Daten, um eine Übersicht zu erstellen.
                </p>
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* Data Selection Lists (Leistungen, Kontingente, Summary) - below the main layout */}
      {hasSearched && (
        <div className="mt-6 space-y-6">
          {filteredLeistungen.length === 0 &&
          filteredKontingente.length === 0 ? (
            <Card className="border-0 shadow-lg">
              <CardContent className="p-8 text-center">
                <p className="text-gray-400">
                  Keine Daten für den ausgewählten Zeitraum und Kunden gefunden.
                </p>
              </CardContent>
            </Card>
          ) : (
            <>
              {filteredLeistungen.length > 0 && (
                <LeistungList
                  formatCurrency={formatCurrency}
                  formatDate={formatDate}
                  formatHours={formatHours}
                  formatTime={formatTime}
                  leistungen={filteredLeistungen}
                  onSelectAll={handleSelectAllLeistungen}
                  onSelectionChange={handleLeistungSelectionChange}
                  selectedLeistungen={selectedItems.leistungen}
                />
              )}
              {filteredKontingente.length > 0 && (
                <KontingentList
                  formatDate={formatDate}
                  kontingente={filteredKontingente}
                  onSelectAll={handleSelectAllKontingente}
                  onSelectionChange={handleKontingentSelectionChange}
                  selectedKontingente={selectedItems.kontingente}
                />
              )}
              {(selectedLeistungenData.length > 0 ||
                selectedKontingenteData.length > 0) && (
                <SummarySection
                  formatHours={formatHours}
                  includeSummaryInPDF={includeSummaryInPDF}
                  kontingente={selectedKontingenteData} // This prop might be less relevant here now
                  leistungen={selectedLeistungenData} // This prop might be less relevant here now
                  onIncludeSummaryChange={setIncludeSummaryInPDF}
                />
              )}
            </>
          )}
        </div>
      )}

      {/* Email Dialog */}
      {showEmailDialog && selectedKunde && (
        <EmailDialog
          isOpen={showEmailDialog}
          kundeId={selectedKunde._id}
          onClose={() => setShowEmailDialog(false)}
          pdfBase64={pdfBase64}
          type="uebersicht"
          zeitraum={formatDateRange()}
        />
      )}
    </PageLayout>
  );
}

export default UebersichtPage;
