import type { Id } from '@/../convex/_generated/dataModel';

// Date range options
export type DateRangeOption =
  | 'thisMonth'
  | 'lastMonth'
  | 'thisQuarter'
  | 'lastQuarter'
  | 'thisYear'
  | 'lastYear'
  | 'custom';

// Form state for the Uebersicht page
export interface UebersichtFormState {
  kundeId: string;
  dateRange: DateRangeOption;
  startDatum: string;
  endDatum: string;
}

// Interface for the LeistungMitNamen type (copied from existing code)
export interface LeistungMitNamen {
  _id: Id<'kunden_leistungen'>;
  _creationTime: number;
  kundenId: Id<'kunden'>;
  mitarbeiterId: Id<'mitarbeiter'>;
  kontingentId: Id<'kunden_kontingente'>;
  startZeit: number;
  endZeit: number;
  art: string;
  mitAnfahrt: boolean;
  beschreibung: string;
  stunden: number;
  stundenpreis: number;
  anfahrtskosten: number;
  kundeName: string;
  mitarbeiterName: string;
  datum: string;
  kontingentName: string;
  kontingentName2?: string;
  lieferscheinID?: string;
}

// Interface for the KontingentMitKunde type (copied from existing code)
export interface KontingentMitKunde {
  _id: Id<'kunden_kontingente'>;
  _creationTime: number;
  kundenId: Id<'kunden'>;
  name: string;
  stunden: number;
  verbrauchteStunden: number;
  startDatum: number;
  endDatum: number;
  istAktiv: boolean;
  kundeName: string;
  restStunden: number;
}

// Interface for the Kunde type (copied from existing code)
// This should align with the new schema for kunden.
// For Uebersicht, we primarily need _id and name, and potentially default rates.
// The full Standort/Ansprechpartner arrays might not be needed here unless the PDF needs them.
export interface Kunde {
  _id: Id<'kunden'>;
  name: string;
  stundenpreis: number; // Assuming this is still top-level
  anfahrtskosten: number; // Assuming this is still top-level
  // standorte and ansprechpartner arrays are part of the full Doc<"kunden">
  // but might not be explicitly needed by all Uebersicht logic using this simplified Kunde type.
}

// Report types
export type ReportType = 'leistungen' | 'kontingente' | 'combined';

// Selected items for report
export interface SelectedItems {
  leistungen: Id<'kunden_leistungen'>[];
  kontingente: Id<'kunden_kontingente'>[];
}
