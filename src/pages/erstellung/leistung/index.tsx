import { useQuery } from 'convex/react';
import { Clock, PlusCircle, TimerIcon, X } from 'lucide-react'; // Added Clock
import { api } from '@/../convex/_generated/api';
import type { Doc, Id } from '@/../convex/_generated/dataModel'; // Keep Id if used for types like KontingentOption
import { Button } from '@/components/_shared/Button';
import { CreateLieferscheinDialog } from '@/components/erstellung/leistung/CreateLieferscheinDialog';
import {
  LeistungDataTable,
  type LeistungMitNamen,
} from '@/components/erstellung/leistung/LeistungDataTable'; // Import type
import { LeistungFilterControls } from '@/components/erstellung/leistung/LeistungFilterControls';
import { LeistungForm } from '@/components/erstellung/leistung/LeistungForm';
import { EmptyState } from '@/components/layout/EmptyState'; // Import new EmptyState
import { PageLayout } from '@/components/layout/PageLayout';
import { StandardDataTable } from '@/components/layout/StandardDataTable';
import {
  formatCurrency,
  formatDate, // Keep this import from formatUtils
  formatHours,
  formatTime,
} from '@/lib/utils/formatUtils';
import { useLeistungenPageLogic } from './hooks/useLeistungenPageLogic'; // Import the new hook

interface KontingentOption {
  _id: Id<'kunden_kontingente'>;
  name: string;
  startDatum: number;
  stunden: number;
  verbrauchteStunden: number;
}

export function LeistungenPage() {
  const leistungenRaw = useQuery(api.erstellung.leistung.list) || [];
  const kundenRaw = useQuery(api.verwaltung.kunden.list) || [];
  const mitarbeiterRaw = useQuery(api.verwaltung.mitarbeiter.list) || [];

  const {
    editingId,
    showForm,
    setShowForm,
    searchTerm,
    filter,
    expandedDescriptions,
    showLieferscheinDialog,
    setShowLieferscheinDialog,
    selectedLeistungForLieferschein,
    initialFormData,
    handleFormSubmitSuccess,
    handleFormCancel,
    handleEdit,
    handleCreateLieferschein,
    handleDelete,
    handleFilterChange,
    handleSearchTermChange,
    resetFilters,
    toggleDescription,
    filteredLeistungen,
  } = useLeistungenPageLogic({
    leistungen: leistungenRaw as LeistungMitNamen[], // Cast as hook expects LeistungMitNamen
    kunden: kundenRaw as Doc<'kunden'>[], // Cast based on hook's expectation
    mitarbeiter: mitarbeiterRaw as Doc<'mitarbeiter'>[], // Cast based on hook's expectation
  });

  const actionButton = (
    <Button className="gap-1" onClick={() => setShowForm(!showForm)} size="sm">
      {showForm ? (
        <>
          <X className="h-4 w-4" /> Abbrechen
        </>
      ) : (
        <>
          <PlusCircle className="h-4 w-4" /> Neue Leistung
        </>
      )}
    </Button>
  );

  const sumStunden = filteredLeistungen.reduce(
    (acc, curr) => acc + curr.stunden,
    0
  );

  return (
    <PageLayout
      action={actionButton}
      subtitle="Erfassen und verwalten Sie erbrachte Leistungen"
      title="Leistungen"
    >
      {showForm && (
        <LeistungForm
          initialData={initialFormData}
          isEditing={!!editingId}
          kunden={kundenRaw as Doc<'kunden'>[]} // Pass raw kunden data
          mitarbeiter={mitarbeiterRaw as Doc<'mitarbeiter'>[]} // Pass raw mitarbeiter data
          onCancel={handleFormCancel}
          onSubmitSuccess={handleFormSubmitSuccess}
        />
      )}

      <StandardDataTable
        filterSlot={
          <LeistungFilterControls
            filter={filter}
            kunden={kundenRaw as Doc<'kunden'>[]}
            onFilterChange={handleFilterChange} // Pass raw kunden data
            onSearchTermChange={handleSearchTermChange}
            searchTerm={searchTerm}
          />
        }
        infoSlot={
          <>
            <TimerIcon className="h-3.5 w-3.5 opacity-70" />
            <span>
              {filteredLeistungen.length} Einträge | {formatHours(sumStunden)}
            </span>
          </>
        }
        title="Leistungsübersicht"
      >
        {filteredLeistungen.length === 0 ? (
          <EmptyState
            actions={
              (searchTerm || filter.kundeId || filter.zeitraum !== 'last7') && (
                <Button onClick={resetFilters} size="sm" variant="outline">
                  Filter zurücksetzen
                </Button>
              )
            } // Adjusted icon size for consistency
            icon={<Clock className="h-12 w-12" />}
            message={
              searchTerm || filter.kundeId || filter.zeitraum !== 'last7'
                ? 'Versuchen Sie, Ihre Filterkriterien anzupassen'
                : 'Erstellen Sie eine neue Leistung mit dem Button oben rechts'
            }
            title="Keine Leistungen gefunden"
          />
        ) : (
          <LeistungDataTable
            expandedDescriptions={expandedDescriptions}
            formatCurrency={formatCurrency}
            formatDate={formatDate}
            formatHours={formatHours}
            formatTime={formatTime}
            leistungen={filteredLeistungen}
            onCreateLieferschein={handleCreateLieferschein} // Use imported formatDate
            onDelete={handleDelete} // Use imported formatTime
            onEdit={handleEdit} // Use imported formatHours
            onToggleDescription={toggleDescription} // Use imported formatCurrency
          />
        )}
      </StandardDataTable>

      {showLieferscheinDialog && selectedLeistungForLieferschein && (
        <CreateLieferscheinDialog
          isOpen={showLieferscheinDialog}
          leistung={selectedLeistungForLieferschein}
          onClose={() => setShowLieferscheinDialog(false)}
        />
      )}
    </PageLayout>
  );
}

export default LeistungenPage;
