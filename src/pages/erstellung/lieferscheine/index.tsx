import { FileText, Plus } from 'lucide-react';
import { Button } from '@/components/_shared/Button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import { LieferscheinEmptyState } from '@/components/erstellung/lieferscheine/LieferscheinEmptyState';
import { LieferscheinFilterControls } from '@/components/erstellung/lieferscheine/LieferscheinFilterControls';
import { LieferscheinRow } from '@/components/erstellung/lieferscheine/LieferscheinRow';
import { NewLieferscheinDialog } from '@/components/erstellung/lieferscheine/NewLieferscheinDialog';
import { PageLayout } from '@/components/layout/PageLayout';
import { StandardDataTable } from '@/components/layout/StandardDataTable';
import { useLieferscheinePageLogic } from './hooks/useLieferscheinePageLogic';

export function LieferscheinePage() {
  const {
    isNewLieferscheinDialogOpen,
    setIsNewLieferscheinDialogOpen,
    handleCloseNewLieferscheinDialog,
    prefilledKundeId,
    prefilledLeistungIds,
    searchTerm,
    setSearchTerm,
    filter,
    expandedRows,
    filteredLieferscheinGruppen,
    handleFilterChange,
    resetFilters,
    handleDelete,
    toggleExpandRow,
  } = useLieferscheinePageLogic();

  const actionButton = (
    <Button
      className="gap-1"
      onClick={() => setIsNewLieferscheinDialogOpen(true)}
      size="sm"
    >
      <Plus className="h-4 w-4" />
      Neuer Lieferschein
    </Button>
  );

  return (
    <PageLayout
      action={actionButton}
      subtitle="Erstellen und verwalten Sie Lieferscheine für erbrachte Leistungen"
      title="Lieferscheine"
    >
      <StandardDataTable
        filterSlot={
          <LieferscheinFilterControls
            filter={filter}
            onFilterChange={handleFilterChange}
            onSearchTermChange={setSearchTerm}
            searchTerm={searchTerm}
          />
        }
        infoSlot={
          <>
            <FileText className="h-3.5 w-3.5 opacity-70" />
            <span>
              {filteredLieferscheinGruppen.length}{' '}
              {filteredLieferscheinGruppen.length === 1
                ? 'Lieferschein'
                : 'Lieferscheine'}
            </span>
          </>
        }
        title="Lieferscheinübersicht"
      >
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
                <TableHead className="font-medium">Nummer</TableHead>
                <TableHead className="font-medium">Kunde</TableHead>
                <TableHead className="font-medium">Erstellt am</TableHead>
                <TableHead className="font-medium">Status</TableHead>
                <TableHead className="w-24 text-center">Aktionen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLieferscheinGruppen.length === 0 ? (
                <TableRow>
                  <TableCell
                    className="py-8 text-center text-gray-400"
                    colSpan={5}
                  >
                    <LieferscheinEmptyState
                      filterKundeId={filter.kundeId}
                      filterStatus={filter.status}
                      filterZeitraum={filter.zeitraum}
                      onResetFilters={resetFilters}
                      searchTerm={searchTerm}
                    />
                  </TableCell>
                </TableRow>
              ) : (
                filteredLieferscheinGruppen.map((gruppe) => (
                  <LieferscheinRow
                    isExpanded={expandedRows.has(
                      gruppe.hauptLieferschein._id.toString()
                    )}
                    key={gruppe.hauptLieferschein._id}
                    korrekturen={gruppe.korrekturen}
                    lieferschein={gruppe.hauptLieferschein}
                    onDelete={handleDelete}
                    onToggleExpand={() =>
                      toggleExpandRow(gruppe.hauptLieferschein._id.toString())
                    }
                    original={gruppe.original}
                  />
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </StandardDataTable>

      <NewLieferscheinDialog
        isOpen={isNewLieferscheinDialogOpen}
        onClose={handleCloseNewLieferscheinDialog}
        prefilledKundeId={prefilledKundeId}
        prefilledLeistungIds={prefilledLeistungIds}
      />
    </PageLayout>
  );
}

export default LieferscheinePage;
