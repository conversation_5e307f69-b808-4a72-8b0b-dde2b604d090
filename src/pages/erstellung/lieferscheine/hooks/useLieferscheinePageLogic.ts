import { useMutation, useQuery } from 'convex/react';
import { useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { calculateDateRange, toISODateString } from '@/lib/utils/dateUtils';

export interface LieferscheinFilter {
  status: 'all' | 'entwurf' | 'fertig';
  zeitraum: 'all' | 'last7' | 'last30' | 'month' | 'lastMonth' | 'custom';
  startDatum: string;
  endDatum: string;
  kundeId: string;
}

export function useLieferscheinePageLogic() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [isNewLieferscheinDialogOpen, setIsNewLieferscheinDialogOpen] =
    useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<LieferscheinFilter>({
    status: 'all',
    zeitraum: 'month', // Standardmäßig "Dieser Monat" anzeigen
    startDatum: '',
    endDatum: '',
    kundeId: 'all', // Standardmäßig alle Kunden anzeigen
  });
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // URL-Parameter für vorausgefüllte Daten
  const prefilledKundeId = searchParams.get('kundenId') as Id<'kunden'> | null;
  const prefilledLeistungIds = searchParams.get('leistungIds')
    ? (searchParams.get('leistungIds')?.split(',') as Id<'kunden_leistungen'>[])
    : [];

  // Dialog automatisch öffnen, wenn openDialog=true in URL
  useEffect(() => {
    if (searchParams.get('openDialog') === 'true') {
      setIsNewLieferscheinDialogOpen(true);
      // URL-Parameter entfernen, aber vorausgefüllte Daten behalten
      const newParams = new URLSearchParams(searchParams);
      newParams.delete('openDialog');
      setSearchParams(newParams, { replace: true });
    }
  }, [searchParams, setSearchParams]);

  // Mutation zum Löschen eines Lieferscheins
  const removeLieferschein = useMutation(api.erstellung.lieferschein.remove);

  // Setze die Start- und Enddaten basierend auf dem Zeitraum
  useMemo(() => {
    if (filter.zeitraum !== 'all' && filter.zeitraum !== 'custom') {
      const { startDate, endDate } = calculateDateRange(filter.zeitraum);
      endDate.setHours(23, 59, 59, 999);

      setFilter((prev) => ({
        ...prev,
        startDatum: toISODateString(startDate),
        endDatum: toISODateString(endDate),
      }));
    }
  }, [filter.zeitraum]);

  const lieferscheinGruppen = useQuery(api.erstellung.lieferschein.list) || [];

  // Filtere die Lieferscheine basierend auf den Filterkriterien
  const filteredLieferscheinGruppen = useMemo(() => {
    return lieferscheinGruppen.filter((gruppe) => {
      const lieferschein = gruppe.hauptLieferschein;

      // Suche
      const searchMatch =
        searchTerm === '' ||
        lieferschein.nummer?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lieferschein.kundeName.toLowerCase().includes(searchTerm.toLowerCase());

      // Status-Filter
      const statusMatch =
        filter.status === 'all' || lieferschein.status === filter.status;

      // Zeitraum-Filter
      let dateMatch = true;
      if (filter.zeitraum !== 'all') {
        const lieferscheinTime = lieferschein.erstelltAm;
        const startTime = filter.startDatum
          ? new Date(filter.startDatum).getTime()
          : 0;
        const endTime = filter.endDatum
          ? new Date(filter.endDatum).getTime() + 86_400_000 - 1 // Ende des Tages
          : new Date(8_640_000_000_000_000).getTime(); // Maximales Datum

        dateMatch =
          lieferscheinTime >= startTime && lieferscheinTime <= endTime;
      }

      // Kunden-Filter
      const kundeMatch =
        filter.kundeId === 'all' || lieferschein.kundenId === filter.kundeId;

      return searchMatch && statusMatch && dateMatch && kundeMatch;
    });
  }, [lieferscheinGruppen, searchTerm, filter]);

  // Handler für Filteränderungen
  const handleFilterChange = (key: keyof LieferscheinFilter, value: string) => {
    setFilter((prev) => ({
      ...prev,
      [key]: value,
      // Wenn der Zeitraum geändert wird und nicht "custom" ist, werden Start- und Enddatum automatisch gesetzt
      ...(key === 'zeitraum' && value !== 'custom' && value !== prev.zeitraum
        ? { startDatum: '', endDatum: '' }
        : {}),
    }));
  };

  // Handler zum Zurücksetzen der Filter
  const resetFilters = () => {
    setSearchTerm('');
    setFilter({
      status: 'all',
      zeitraum: 'month',
      startDatum: '',
      endDatum: '',
      kundeId: 'all',
    });
  };

  // Handler zum Löschen eines Lieferscheins
  const handleDelete = async (id: Id<'kunden_lieferscheine'>) => {
    if (
      window.confirm(
        'Möchten Sie diesen Lieferschein wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.'
      )
    ) {
      try {
        await removeLieferschein({ id });
        toast.success('Lieferschein erfolgreich gelöscht');
      } catch (error: any) {
        toast.error(
          `Fehler beim Löschen: ${error.message || 'Unbekannter Fehler'}`
        );
      }
    }
  };

  // Handler zum Ein-/Ausklappen einer Zeile
  const toggleExpandRow = (id: string) => {
    setExpandedRows((prevExpandedRows) => {
      const newExpandedRows = new Set(prevExpandedRows);
      if (newExpandedRows.has(id)) {
        newExpandedRows.delete(id);
      } else {
        newExpandedRows.add(id);
      }
      return newExpandedRows;
    });
  };

  // Handler zum Schließen des Dialogs und Entfernen der URL-Parameter
  const handleCloseNewLieferscheinDialog = () => {
    setIsNewLieferscheinDialogOpen(false);
    // URL-Parameter entfernen
    const newParams = new URLSearchParams(searchParams);
    newParams.delete('kundenId');
    newParams.delete('leistungIds');
    setSearchParams(newParams, { replace: true });
  };

  return {
    isNewLieferscheinDialogOpen,
    setIsNewLieferscheinDialogOpen,
    handleCloseNewLieferscheinDialog,
    prefilledKundeId,
    prefilledLeistungIds,
    searchTerm,
    setSearchTerm,
    filter,
    expandedRows,
    filteredLieferscheinGruppen,
    handleFilterChange,
    resetFilters,
    handleDelete,
    toggleExpandRow,
  };
}
