import { Check, Pencil, Plus, Trash2, X } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/_shared/Button';
import { Checkbox } from '@/components/_shared/Checkbox';
import { Input } from '@/components/_shared/Input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';

interface StandortData {
  strasse: string;
  plz: string;
  ort: string;
  land?: string;
  istHauptstandort: boolean;
}

interface StandorteTableProps {
  standorte: StandortData[];
  onChange: (standorte: StandortData[]) => void;
}

export function StandorteTable({ standorte, onChange }: StandorteTableProps) {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const handleAddStandort = () => {
    const newStandort: StandortData = {
      strasse: '',
      plz: '',
      ort: '',
      land: 'Deutschland',
      istHauptstandort: false,
    };
    const newStandorte = [...standorte, newStandort];
    onChange(newStandorte);
    setEditingIndex(newStandorte.length - 1);
  };

  const handleRemoveStandort = (index: number) => {
    if (standorte.length <= 1) {
      return;
    }
    const newStandorte = standorte.filter((_, i) => i !== index);
    onChange(newStandorte);
    setEditingIndex(null);
  };

  const handleInputChange = (
    index: number,
    field: keyof StandortData,
    value: string | boolean
  ) => {
    const newStandorte = [...standorte];

    if (field === 'istHauptstandort' && value === true) {
      newStandorte.forEach((s, i) => {
        s.istHauptstandort = i === index;
      });
    } else {
      (newStandorte[index] as any)[field] = value;
    }

    onChange(newStandorte);
  };

  const handleSave = () => {
    setEditingIndex(null);
  };

  const handleEdit = (index: number) => {
    setEditingIndex(index);
  };

  const handleCancel = () => {
    setEditingIndex(null);
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="font-medium text-sm">Standorte</div>
        <Button
          className="h-7 text-xs"
          onClick={handleAddStandort}
          size="sm"
          type="button"
          variant="outline"
        >
          <Plus className="mr-1 h-3 w-3" />
          Standort hinzufügen
        </Button>
      </div>

      <div className="overflow-hidden rounded-lg border border-gray-700">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
              <TableHead className="w-16 px-3 py-2 text-center text-xs">
                Haupt
              </TableHead>
              <TableHead className="px-3 py-2 text-xs">Straße</TableHead>
              <TableHead className="px-3 py-2 text-xs">PLZ</TableHead>
              <TableHead className="px-3 py-2 text-xs">Ort</TableHead>
              <TableHead className="px-3 py-2 text-xs">Land</TableHead>
              <TableHead className="w-20 px-3 py-2 text-center text-xs">
                Aktionen
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {standorte.map((standort, index) => (
              <TableRow key={index}>
                <TableCell className="px-3 py-2 text-center">
                  {editingIndex === index ? (
                    <Checkbox
                      checked={standort.istHauptstandort}
                      onCheckedChange={(checked) =>
                        handleInputChange(index, 'istHauptstandort', !!checked)
                      }
                    />
                  ) : (
                    standort.istHauptstandort && (
                      <Check className="mx-auto h-4 w-4 text-green-400" />
                    )
                  )}
                </TableCell>
                <TableCell className="px-3 py-2">
                  {editingIndex === index ? (
                    <Input
                      className="h-8 text-sm"
                      onChange={(e) =>
                        handleInputChange(index, 'strasse', e.target.value)
                      }
                      placeholder="Straße und Hausnummer"
                      value={standort.strasse}
                    />
                  ) : (
                    <span className="text-sm">{standort.strasse || '-'}</span>
                  )}
                </TableCell>
                <TableCell className="px-3 py-2">
                  {editingIndex === index ? (
                    <Input
                      className="h-8 text-sm"
                      onChange={(e) =>
                        handleInputChange(index, 'plz', e.target.value)
                      }
                      placeholder="PLZ"
                      value={standort.plz}
                    />
                  ) : (
                    <span className="text-sm">{standort.plz || '-'}</span>
                  )}
                </TableCell>
                <TableCell className="px-3 py-2">
                  {editingIndex === index ? (
                    <Input
                      className="h-8 text-sm"
                      onChange={(e) =>
                        handleInputChange(index, 'ort', e.target.value)
                      }
                      placeholder="Ort"
                      value={standort.ort}
                    />
                  ) : (
                    <span className="text-sm">{standort.ort || '-'}</span>
                  )}
                </TableCell>
                <TableCell className="px-3 py-2">
                  {editingIndex === index ? (
                    <Input
                      className="h-8 text-sm"
                      onChange={(e) =>
                        handleInputChange(index, 'land', e.target.value)
                      }
                      placeholder="Land"
                      value={standort.land || ''}
                    />
                  ) : (
                    <span className="text-sm">{standort.land || '-'}</span>
                  )}
                </TableCell>
                <TableCell className="px-3 py-2 text-center">
                  <div className="flex justify-center gap-1">
                    {editingIndex === index ? (
                      <>
                        <Button
                          className="h-6 w-6 text-green-400 hover:text-green-300"
                          onClick={handleSave}
                          size="icon"
                          title="Speichern"
                          type="button"
                          variant="ghost"
                        >
                          <Check className="h-3 w-3" />
                        </Button>
                        <Button
                          className="h-6 w-6 text-gray-400 hover:text-gray-300"
                          onClick={handleCancel}
                          size="icon"
                          title="Abbrechen"
                          type="button"
                          variant="ghost"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          className="h-6 w-6 text-blue-400 hover:text-blue-300"
                          onClick={() => handleEdit(index)}
                          size="icon"
                          title="Bearbeiten"
                          type="button"
                          variant="ghost"
                        >
                          <Pencil className="h-3 w-3" />
                        </Button>
                        {standorte.length > 1 && (
                          <Button
                            className="h-6 w-6 text-red-400 hover:text-red-300"
                            onClick={() => handleRemoveStandort(index)}
                            size="icon"
                            title="Löschen"
                            type="button"
                            variant="ghost"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
