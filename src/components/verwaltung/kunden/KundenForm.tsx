import { Euro } from 'lucide-react';
import { useEffect, useState } from 'react';
import type { Doc } from '@/../convex/_generated/dataModel';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Input } from '@/components/_shared/Input';
import { Label } from '@/components/_shared/Label';
import { AnsprechpartnerTable } from './AnsprechpartnerTable';
import { StandorteTable } from './StandorteTable';

interface StandortData {
  strasse: string;
  plz: string;
  ort: string;
  land?: string;
  istHauptstandort: boolean;
}

interface AnsprechpartnerData {
  name: string;
  email?: string;
  telefon?: string;
  mobil?: string;
  position?: string;
  istHauptansprechpartner: boolean;
  istEmailLieferscheinEmpfaenger?: boolean;
  istEmailUebersichtEmpfaenger?: boolean;
  istEmailAnrede?: boolean;
}

interface KundeFormData {
  name: string;
  stundensatz: string;
  anfahrtskosten: string;
  standorte: StandortData[];
  ansprechpartner: AnsprechpartnerData[];
}

interface KundenFormProps {
  initialData?: Doc<'kunden'>;
  onSubmit: (data: any) => void;
  isSubmitting: boolean;
  formId: string;
}

export function KundenForm({
  initialData,
  onSubmit,
  isSubmitting,
  formId,
}: KundenFormProps) {
  const [formState, setFormState] = useState<KundeFormData>({
    name: '',
    stundensatz: '',
    anfahrtskosten: '',
    standorte: [
      {
        strasse: '',
        plz: '',
        ort: '',
        land: 'Deutschland',
        istHauptstandort: true,
      },
    ],
    ansprechpartner: [
      {
        name: '',
        email: '',
        telefon: '',
        mobil: '',
        position: '',
        istHauptansprechpartner: true,
        istEmailLieferscheinEmpfaenger: false,
        istEmailUebersichtEmpfaenger: false,
        istEmailAnrede: false,
      },
    ],
  });

  useEffect(() => {
    if (initialData) {
      setFormState({
        name: initialData.name,
        stundensatz: initialData.stundenpreis?.toString() || '',
        anfahrtskosten: initialData.anfahrtskosten?.toString() || '',
        standorte: initialData.standorte?.length
          ? initialData.standorte.map((s) => ({
              strasse: s.strasse,
              plz: s.plz,
              ort: s.ort,
              land: s.land || 'Deutschland',
              istHauptstandort: s.istHauptstandort,
            }))
          : [
              {
                strasse: '',
                plz: '',
                ort: '',
                land: 'Deutschland',
                istHauptstandort: true,
              },
            ],
        ansprechpartner: initialData.ansprechpartner?.length
          ? initialData.ansprechpartner.map((a) => ({
              name: a.name,
              email: a.email || '',
              telefon: a.telefon || '',
              mobil: a.mobil || '',
              position: a.position || '',
              istHauptansprechpartner: a.istHauptansprechpartner,
              istEmailLieferscheinEmpfaenger: a.istEmailLieferscheinEmpfaenger,
              istEmailUebersichtEmpfaenger: a.istEmailUebersichtEmpfaenger,
              istEmailAnrede: a.istEmailAnrede,
            }))
          : [
              {
                name: '',
                email: '',
                telefon: '',
                mobil: '',
                position: '',
                istHauptansprechpartner: true,
                istEmailLieferscheinEmpfaenger: false,
                istEmailUebersichtEmpfaenger: false,
                istEmailAnrede: false,
              },
            ],
      });
    }
  }, [initialData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormState((prev) => ({ ...prev, [name]: value }));
  };

  const handleStandorteChange = (standorte: StandortData[]) => {
    setFormState((prev) => ({
      ...prev,
      standorte,
    }));
  };

  const handleAnsprechpartnerChange = (
    ansprechpartner: AnsprechpartnerData[]
  ) => {
    setFormState((prev) => ({
      ...prev,
      ansprechpartner,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const submitData = {
      ...formState,
      stundenpreis: Number.parseFloat(formState.stundensatz) || 0,
      anfahrtskosten: Number.parseFloat(formState.anfahrtskosten) || 0,
    };
    (submitData as any).stundensatz = undefined;

    onSubmit(submitData);
  };

  return (
    <form className="space-y-6" id={formId} onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle>Grunddaten</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <Label htmlFor="name">Firmenname *</Label>
            <Input
              id="name"
              name="name"
              onChange={handleInputChange}
              placeholder="Firmenname eingeben"
              required
              value={formState.name}
            />
          </div>

          <div>
            <Label htmlFor="stundensatz">Stundensatz *</Label>
            <div className="relative">
              <Input
                className="pl-10"
                id="stundensatz"
                name="stundensatz"
                onChange={handleInputChange}
                placeholder="0.00"
                required
                step="0.01"
                type="number"
                value={formState.stundensatz}
              />
              <Euro className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-gray-400" />
            </div>
          </div>

          <div>
            <Label htmlFor="anfahrtskosten">Anfahrtskosten</Label>
            <div className="relative">
              <Input
                className="pl-10"
                id="anfahrtskosten"
                name="anfahrtskosten"
                onChange={handleInputChange}
                placeholder="0.00"
                step="0.01"
                type="number"
                value={formState.anfahrtskosten}
              />
              <Euro className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-gray-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Standorte</CardTitle>
        </CardHeader>
        <CardContent>
          <StandorteTable
            onChange={handleStandorteChange}
            standorte={formState.standorte}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Ansprechpartner</CardTitle>
        </CardHeader>
        <CardContent>
          <AnsprechpartnerTable
            ansprechpartner={formState.ansprechpartner}
            onChange={handleAnsprechpartnerChange}
          />
        </CardContent>
      </Card>


    </form>
  );
}
