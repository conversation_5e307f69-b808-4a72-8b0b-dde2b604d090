import {
  Book<PERSON>pen,
  <PERSON>riefcase,
  Mail,
  MapPin,
  Pencil,
  Trash2,
} from 'lucide-react';
import { Link } from 'react-router-dom';
import type { Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import type { Kunde } from '@/pages/verwaltung/kunden/index';

interface KundenDataTableProps {
  kunden: Kunde[];
  onDelete: (id: Id<'kunden'>) => void;
  formatCurrency: (amount: number | null | undefined) => string;
  getHauptstandortOrt: (kunde: Kunde) => string;
  getHauptansprechpartnerInfo: (kunde: Kunde) => {
    email?: string;
    name?: string;
  };
}

export function KundenDataTable({
  kunden,
  onDelete,
  formatCurrency,
  getHauptstandortOrt,
  getHauptansprechpartnerInfo,
}: KundenDataTableProps) {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
            <TableHead className="font-medium">Name</TableHead>
            <TableHead className="font-medium">Hauptstandort</TableHead>
            <TableHead className="font-medium">Hauptansprechpartner</TableHead>
            <TableHead className="text-right font-medium">
              Stundenpreis
            </TableHead>
            <TableHead className="text-right font-medium">
              Anfahrtskosten
            </TableHead>
            <TableHead className="w-24 text-center">Aktionen</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {kunden.map((kunde) => {
            const hauptstandortOrt = getHauptstandortOrt(kunde);
            const hauptansprechpartner = getHauptansprechpartnerInfo(kunde);

            return (
              <TableRow className="border-gray-800 border-b" key={kunde._id}>
                <TableCell className="py-3">
                  <div className="flex items-center gap-2">
                    <div className="flex h-7 w-7 items-center justify-center rounded-full bg-gray-700 text-gray-300">
                      <Briefcase className="h-3.5 w-3.5" />
                    </div>
                    <span className="font-medium">{kunde.name}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {hauptstandortOrt !== '-' ? (
                    <div className="flex items-center gap-1 text-gray-300 text-sm">
                      <MapPin className="h-3.5 w-3.5 text-gray-400" />
                      {hauptstandortOrt}
                    </div>
                  ) : (
                    '-'
                  )}
                </TableCell>
                <TableCell>
                  {hauptansprechpartner.email &&
                  hauptansprechpartner.email !== '-' ? (
                    <a
                      className="flex items-center gap-1 text-blue-400 text-sm hover:underline"
                      href={`mailto:${hauptansprechpartner.email}`}
                    >
                      <Mail className="h-3.5 w-3.5" />
                      {hauptansprechpartner.email}
                    </a>
                  ) : (
                    '-'
                  )}
                  {hauptansprechpartner.name &&
                    hauptansprechpartner.name !== '-' && (
                      <div className="mt-0.5 text-gray-400 text-xs">
                        ({hauptansprechpartner.name})
                      </div>
                    )}
                </TableCell>
                <TableCell className="text-right">
                  {formatCurrency(kunde.stundenpreis)}
                </TableCell>
                <TableCell className="text-right">
                  {formatCurrency(kunde.anfahrtskosten)}
                </TableCell>
                <TableCell>
                  <div className="flex justify-center gap-1">
                    <Link to={`/verwaltung/kunden/${kunde._id}`}>
                      <Button
                        className="h-8 w-8 text-gray-400 hover:text-blue-400"
                        size="icon"
                        title="Bearbeiten"
                        variant="ghost"
                      >
                        <Pencil className="h-4 w-4" />
                        <span className="sr-only">Bearbeiten</span>
                      </Button>
                    </Link>
                    <Link to={`/kunden/doku/${kunde._id}`}>
                      <Button
                        className="h-8 w-8 text-gray-400 hover:text-green-400"
                        size="icon"
                        title="Dokumentation"
                        variant="ghost"
                      >
                        <BookOpen className="h-4 w-4" />
                        <span className="sr-only">Dokumentation</span>
                      </Button>
                    </Link>
                    <Button
                      className="h-8 w-8 text-gray-400 hover:text-red-400"
                      onClick={() => void onDelete(kunde._id)}
                      size="icon"
                      title="Löschen"
                      variant="ghost"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Löschen</span>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
