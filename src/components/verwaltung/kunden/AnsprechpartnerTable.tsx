import { Check, Mail, Pencil, Plus, Trash2, X } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/_shared/Button';
import { Checkbox } from '@/components/_shared/Checkbox';
import { Input } from '@/components/_shared/Input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';

interface AnsprechpartnerData {
  name: string;
  email?: string;
  telefon?: string;
  mobil?: string;
  position?: string;
  istHauptansprechpartner: boolean;
  istEmailLieferscheinEmpfaenger?: boolean;
  istEmailUebersichtEmpfaenger?: boolean;
  istEmailAnrede?: boolean;
}

interface AnsprechpartnerTableProps {
  ansprechpartner: AnsprechpartnerData[];
  onChange: (ansprechpartner: AnsprechpartnerData[]) => void;
}

export function AnsprechpartnerTable({
  ansprechpartner,
  onChange,
}: AnsprechpartnerTableProps) {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const handleAddAnsprechpartner = () => {
    const newAnsprechpartner: AnsprechpartnerData = {
      name: '',
      email: '',
      telefon: '',
      mobil: '',
      position: '',
      istHauptansprechpartner: false,
      istEmailLieferscheinEmpfaenger: false,
      istEmailUebersichtEmpfaenger: false,
      istEmailAnrede: false,
    };
    const newAnsprechpartnerList = [...ansprechpartner, newAnsprechpartner];
    onChange(newAnsprechpartnerList);
    setEditingIndex(newAnsprechpartnerList.length - 1);
  };

  const handleRemoveAnsprechpartner = (index: number) => {
    if (ansprechpartner.length <= 1) {
      return;
    }
    const newAnsprechpartnerList = ansprechpartner.filter(
      (_, i) => i !== index
    );
    onChange(newAnsprechpartnerList);
    setEditingIndex(null);
  };

  const handleInputChange = (
    index: number,
    field: keyof AnsprechpartnerData,
    value: string | boolean
  ) => {
    const newAnsprechpartnerList = [...ansprechpartner];

    if (field === 'istHauptansprechpartner' && value === true) {
      newAnsprechpartnerList.forEach((a, i) => {
        a.istHauptansprechpartner = i === index;
      });
    } else if (field === 'istEmailAnrede' && value === true) {
      newAnsprechpartnerList.forEach((a, i) => {
        a.istEmailAnrede = i === index;
      });
    } else {
      (newAnsprechpartnerList[index] as any)[field] = value;
    }

    onChange(newAnsprechpartnerList);
  };

  const handleSave = () => {
    setEditingIndex(null);
  };

  const handleEdit = (index: number) => {
    setEditingIndex(index);
  };

  const handleCancel = () => {
    setEditingIndex(null);
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="font-medium text-sm">Ansprechpartner</div>
        <Button
          className="h-7 text-xs"
          onClick={handleAddAnsprechpartner}
          size="sm"
          type="button"
          variant="outline"
        >
          <Plus className="mr-1 h-3 w-3" />
          Ansprechpartner hinzufügen
        </Button>
      </div>

      <div className="overflow-hidden rounded-lg border border-gray-700">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
              <TableHead className="w-16 px-3 py-2 text-center text-xs">
                Haupt
              </TableHead>
              <TableHead className="px-3 py-2 text-xs">Name</TableHead>
              <TableHead className="px-3 py-2 text-xs">Position</TableHead>
              <TableHead className="px-3 py-2 text-xs">E-Mail</TableHead>
              <TableHead className="px-3 py-2 text-xs">Telefon</TableHead>
              <TableHead className="px-3 py-2 text-xs">Mobil</TableHead>
              <TableHead className="w-24 px-3 py-2 text-center text-xs">
                E-Mail Einstellungen
              </TableHead>
              <TableHead className="w-20 px-3 py-2 text-center text-xs">
                Aktionen
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {ansprechpartner.map((partner, index) => (
              <TableRow key={index}>
                <TableCell className="px-3 py-2 text-center">
                  {editingIndex === index ? (
                    <Checkbox
                      checked={partner.istHauptansprechpartner}
                      onCheckedChange={(checked) =>
                        handleInputChange(
                          index,
                          'istHauptansprechpartner',
                          !!checked
                        )
                      }
                    />
                  ) : (
                    partner.istHauptansprechpartner && (
                      <Check className="mx-auto h-4 w-4 text-green-400" />
                    )
                  )}
                </TableCell>
                <TableCell className="px-3 py-2">
                  {editingIndex === index ? (
                    <Input
                      className="h-8 text-sm"
                      onChange={(e) =>
                        handleInputChange(index, 'name', e.target.value)
                      }
                      placeholder="Vor- und Nachname"
                      value={partner.name}
                    />
                  ) : (
                    <span className="text-sm">{partner.name || '-'}</span>
                  )}
                </TableCell>
                <TableCell className="px-3 py-2">
                  {editingIndex === index ? (
                    <Input
                      className="h-8 text-sm"
                      onChange={(e) =>
                        handleInputChange(index, 'position', e.target.value)
                      }
                      placeholder="Position/Abteilung"
                      value={partner.position || ''}
                    />
                  ) : (
                    <span className="text-sm">{partner.position || '-'}</span>
                  )}
                </TableCell>
                <TableCell className="px-3 py-2">
                  {editingIndex === index ? (
                    <Input
                      className="h-8 text-sm"
                      onChange={(e) =>
                        handleInputChange(index, 'email', e.target.value)
                      }
                      placeholder="<EMAIL>"
                      type="email"
                      value={partner.email || ''}
                    />
                  ) : partner.email ? (
                    <a
                      className="text-cyan-400 text-sm hover:text-cyan-300"
                      href={`mailto:${partner.email}`}
                    >
                      {partner.email}
                    </a>
                  ) : (
                    <span className="text-sm">-</span>
                  )}
                </TableCell>
                <TableCell className="px-3 py-2">
                  {editingIndex === index ? (
                    <Input
                      className="h-8 text-sm"
                      onChange={(e) =>
                        handleInputChange(index, 'telefon', e.target.value)
                      }
                      placeholder="+49 123 456789"
                      type="tel"
                      value={partner.telefon || ''}
                    />
                  ) : (
                    <span className="text-sm">{partner.telefon || '-'}</span>
                  )}
                </TableCell>
                <TableCell className="px-3 py-2">
                  {editingIndex === index ? (
                    <Input
                      className="h-8 text-sm"
                      onChange={(e) =>
                        handleInputChange(index, 'mobil', e.target.value)
                      }
                      placeholder="+49 123 456789"
                      type="tel"
                      value={partner.mobil || ''}
                    />
                  ) : (
                    <span className="text-sm">{partner.mobil || '-'}</span>
                  )}
                </TableCell>
                <TableCell className="px-3 py-2 text-center">
                  {editingIndex === index ? (
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-1">
                        <Checkbox
                          checked={partner.istEmailLieferscheinEmpfaenger}
                          onCheckedChange={(checked) =>
                            handleInputChange(
                              index,
                              'istEmailLieferscheinEmpfaenger',
                              !!checked
                            )
                          }
                        />
                        <span className="text-blue-400 text-xs">LS</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Checkbox
                          checked={partner.istEmailUebersichtEmpfaenger}
                          onCheckedChange={(checked) =>
                            handleInputChange(
                              index,
                              'istEmailUebersichtEmpfaenger',
                              !!checked
                            )
                          }
                        />
                        <span className="text-green-400 text-xs">ÜS</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Checkbox
                          checked={partner.istEmailAnrede}
                          onCheckedChange={(checked) =>
                            handleInputChange(
                              index,
                              'istEmailAnrede',
                              !!checked
                            )
                          }
                        />
                        <span className="text-purple-400 text-xs">AN</span>
                      </div>
                    </div>
                  ) : (
                    <div className="flex justify-center gap-1">
                      {partner.istEmailLieferscheinEmpfaenger && (
                        <div
                          className="h-3 w-3 text-blue-400"
                          title="Lieferschein-Empfänger"
                        >
                          <Mail className="h-3 w-3" />
                        </div>
                      )}
                      {partner.istEmailUebersichtEmpfaenger && (
                        <div
                          className="h-3 w-3 text-green-400"
                          title="Übersicht-Empfänger"
                        >
                          <Mail className="h-3 w-3" />
                        </div>
                      )}
                      {partner.istEmailAnrede && (
                        <div
                          className="h-3 w-3 text-purple-400"
                          title="E-Mail-Anrede"
                        >
                          <Mail className="h-3 w-3" />
                        </div>
                      )}
                    </div>
                  )}
                </TableCell>
                <TableCell className="px-3 py-2 text-center">
                  <div className="flex justify-center gap-1">
                    {editingIndex === index ? (
                      <>
                        <Button
                          className="h-6 w-6 text-green-400 hover:text-green-300"
                          onClick={handleSave}
                          size="icon"
                          title="Speichern"
                          type="button"
                          variant="ghost"
                        >
                          <Check className="h-3 w-3" />
                        </Button>
                        <Button
                          className="h-6 w-6 text-gray-400 hover:text-gray-300"
                          onClick={handleCancel}
                          size="icon"
                          title="Abbrechen"
                          type="button"
                          variant="ghost"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          className="h-6 w-6 text-blue-400 hover:text-blue-300"
                          onClick={() => handleEdit(index)}
                          size="icon"
                          title="Bearbeiten"
                          type="button"
                          variant="ghost"
                        >
                          <Pencil className="h-3 w-3" />
                        </Button>
                        {ansprechpartner.length > 1 && (
                          <Button
                            className="h-6 w-6 text-red-400 hover:text-red-300"
                            onClick={() => handleRemoveAnsprechpartner(index)}
                            size="icon"
                            title="Löschen"
                            type="button"
                            variant="ghost"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
