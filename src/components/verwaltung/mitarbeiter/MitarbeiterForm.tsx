import { Mail, User } from 'lucide-react';
import { useEffect, useState } from 'react';
import type { Doc } from '@/../convex/_generated/dataModel';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Input } from '@/components/_shared/Input';
import { Label } from '@/components/_shared/Label';

interface MitarbeiterFormData {
  name: string;
  email: string;
}

interface MitarbeiterFormProps {
  initialData?: Doc<'mitarbeiter'>;
  onSubmit: (data: any) => void;
  isSubmitting: boolean;
  formId: string;
}

export function MitarbeiterForm({
  initialData,
  onSubmit,
  isSubmitting,
  formId,
}: MitarbeiterFormProps) {
  const [formState, setFormState] = useState<MitarbeiterFormData>({
    name: '',
    email: '',
  });

  useEffect(() => {
    if (initialData) {
      setFormState({
        name: initialData.name,
        email: initialData.email,
      });
    }
  }, [initialData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormState((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formState);
  };

  return (
    <form id={formId} onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle>Mitarbeiterdaten</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="name">Name *</Label>
            <div className="relative">
              <Input
                className="pl-10"
                id="name"
                name="name"
                onChange={handleInputChange}
                placeholder="Vor- und Nachname eingeben"
                required
                value={formState.name}
              />
              <User className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-gray-400" />
            </div>
          </div>

          <div>
            <Label htmlFor="email">E-Mail *</Label>
            <div className="relative">
              <Input
                className="pl-10"
                id="email"
                name="email"
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                required
                type="email"
                value={formState.email}
              />
              <Mail className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-gray-400" />
            </div>
          </div>
        </CardContent>
      </Card>
    </form>
  );
}
