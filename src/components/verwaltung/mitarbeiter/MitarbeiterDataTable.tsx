import { <PERSON><PERSON><PERSON>, Trash2, User<PERSON>ir<PERSON> } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import type { Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';

interface Mitarbeiter {
  _id: Id<'mitarbeiter'>;
  name: string;
  email: string;
}

interface MitarbeiterDataTableProps {
  mitarbeiter: Mitarbeiter[];
  onDelete: (id: Id<'mitarbeiter'>) => void;
}

export function MitarbeiterDataTable({
  mitarbeiter,
  onDelete,
}: MitarbeiterDataTableProps) {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
            <TableHead className="font-medium">Name</TableHead>
            <TableHead className="font-medium">E-Mail</TableHead>
            <TableHead className="w-24 text-center">Aktionen</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {mitarbeiter.map((m) => (
            <TableRow className="border-gray-800 border-b" key={m._id}>
              <TableCell className="py-3">
                <div className="flex items-center gap-2">
                  <div className="flex h-7 w-7 items-center justify-center rounded-full bg-gray-700 text-gray-300">
                    <UserCircle className="h-3.5 w-3.5" />
                  </div>
                  <span className="font-medium">{m.name}</span>
                </div>
              </TableCell>
              <TableCell>{m.email}</TableCell>
              <TableCell>
                <div className="flex justify-center gap-1">
                  <Link to={`/verwaltung/mitarbeiter/${m._id}`}>
                    <Button
                      className="h-8 w-8 text-gray-400 hover:text-blue-400"
                      size="icon"
                      title="Bearbeiten"
                      variant="ghost"
                    >
                      <Pencil className="h-4 w-4" />
                      <span className="sr-only">Bearbeiten</span>
                    </Button>
                  </Link>
                  <Button
                    className="h-8 w-8 text-gray-400 hover:text-red-400"
                    onClick={() => void onDelete(m._id)}
                    size="icon"
                    title="Löschen"
                    variant="ghost"
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">Löschen</span>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
