import {
  type FilterConfig,
  GenericFilterControls,
} from '@/components/layout/GenericFilterControls';

interface MitarbeiterFilterControlsProps {
  searchTerm: string;
  onSearchTermChange: (value: string) => void;
  onResetAllFilters?: () => void;
}

export function MitarbeiterFilterControls({
  searchTerm,
  onSearchTermChange,
  onResetAllFilters,
}: MitarbeiterFilterControlsProps) {
  const filtersConfig: FilterConfig[] = [
    {
      type: 'search',
      id: 'mitarbeiterSearch',
      value: searchTerm,
      onChange: onSearchTermChange,
      placeholder: 'Mitarbeiter suchen...',
      className: 'flex-grow',
      inputClassName: 'h-8 text-xs pl-8 w-full bg-gray-800 border-gray-700',
    },
  ];

  return (
    <GenericFilterControls
      filters={filtersConfig}
      onResetAll={onResetAllFilters}
    />
  );
}
