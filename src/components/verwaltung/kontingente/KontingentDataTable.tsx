import {
  Briefcase,
  CalendarDays,
  Hourglass,
  Pencil,
  Trash2,
} from 'lucide-react';
import type React from 'react';
import type { Id } from '@/../convex/_generated/dataModel';
import { Badge } from '@/components/_shared/Badge';
import { Button } from '@/components/_shared/Button';
import { Progress } from '@/components/_shared/Progress';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';

// Übernommenen Typ oder neu definieren
interface KontingentMitKunde {
  _id: Id<'kunden_kontingente'>;
  _creationTime: number;
  kundenId: Id<'kunden'>;
  name: string;
  stunden: number;
  verbrauchteStunden: number;
  startDatum: number;
  endDatum: number;
  istAktiv: boolean;
  kundeName: string;
  restStunden: number;
}

interface StatusInfo {
  text: string;
  color: string;
  icon: React.ReactNode;
}

interface KontingentDataTableProps {
  kontingente: KontingentMitKunde[];
  onEdit: (kontingent: KontingentMitKunde) => void;
  onDelete: (id: Id<'kunden_kontingente'>) => void;
  formatDate: (timestamp: number) => string;
  getStatusInfo: (kontingent: KontingentMitKunde) => StatusInfo;
}

// Hilfsfunktion zur Berechnung der verbleibenden Tage
const calculateRemainingDays = (endTimestamp: number): number => {
  const now = new Date();
  const endDate = new Date(endTimestamp);
  // Zeiten auf Mitternacht setzen für genauen Tagesvergleich
  now.setHours(0, 0, 0, 0);
  endDate.setHours(0, 0, 0, 0);

  const diffTime = endDate.getTime() - now.getTime();
  if (diffTime < 0) {
    return 0; // Bereits abgelaufen
  }
  // Millisekunden in Tage umrechnen und aufrunden
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

export function KontingentDataTable({
  kontingente,
  onEdit,
  onDelete,
  formatDate,
  getStatusInfo,
}: KontingentDataTableProps) {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
            <TableHead className="font-medium">Kunde & Kontingent</TableHead>
            <TableHead className="text-right font-medium">Nutzung</TableHead>
            <TableHead className="font-medium">Gültigkeit & Status</TableHead>
            <TableHead className="w-24 text-center">Aktionen</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {kontingente.map((kontingent) => {
            const progress =
              kontingent.stunden > 0
                ? (kontingent.verbrauchteStunden / kontingent.stunden) * 100
                : 0;
            const status = getStatusInfo(kontingent);
            return (
              <TableRow
                className="border-gray-800 border-b"
                key={kontingent._id}
              >
                <TableCell className="py-3">
                  <div className="flex items-center gap-2">
                    <div className="flex h-7 w-7 items-center justify-center rounded-full bg-gray-700 text-gray-300">
                      <Briefcase className="h-3.5 w-3.5" />
                    </div>
                    <span className="font-medium">{kontingent.kundeName}</span>
                  </div>
                  <div className="mt-1 ml-9 text-gray-400 text-xs">
                    {kontingent.name}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="mb-1 font-medium">
                    {kontingent.restStunden.toFixed(2)} h{' '}
                    <span className="text-gray-400 text-xs">Rest</span>
                  </div>
                  <Progress
                    className="h-1.5 bg-gray-700"
                    indicatorClassName={`bg-blue-500 ${progress > 85 ? 'bg-red-500' : ''}`}
                    value={progress}
                  />
                  <div className="mt-1 text-gray-400 text-xs">
                    {kontingent.verbrauchteStunden.toFixed(2)} /{' '}
                    {kontingent.stunden.toFixed(1)} h
                  </div>
                </TableCell>
                <TableCell>
                  <div className="mb-1 flex items-center gap-1.5 text-sm">
                    <CalendarDays className="h-3.5 w-3.5 text-gray-400" />
                    <span>
                      {formatDate(kontingent.startDatum)} -{' '}
                      {formatDate(kontingent.endDatum)}
                      {/* Verbleibende Tage anzeigen, wenn aktiv und nicht abgelaufen */}
                      {status.text === 'Aktiv' ||
                      status.text === 'Zukünftig' ? (
                        <span className="ml-1.5 inline-flex items-center text-gray-400 text-xs">
                          <Hourglass className="mr-0.5 h-3 w-3" />{' '}
                          {calculateRemainingDays(kontingent.endDatum)} T.
                        </span>
                      ) : null}
                    </span>
                  </div>
                  <Badge
                    className={`text-xs ${status.color} border-current`}
                    variant="outline"
                  >
                    {status.icon}
                    {status.text}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex justify-center gap-1">
                    <Button
                      className="h-8 w-8 text-gray-400 hover:text-blue-400"
                      onClick={() => onEdit(kontingent)}
                      size="icon"
                      title="Bearbeiten"
                      variant="ghost"
                    >
                      <Pencil className="h-4 w-4" />
                      <span className="sr-only">Bearbeiten</span>
                    </Button>
                    <Button
                      className="h-8 w-8 text-gray-400 hover:text-red-400"
                      onClick={() => void onDelete(kontingent._id)}
                      size="icon"
                      title="Löschen"
                      variant="ghost"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Löschen</span>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
