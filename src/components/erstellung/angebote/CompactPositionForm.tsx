import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Plus, Trash2 } from 'lucide-react';
import { useEffect } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import * as z from 'zod';
import type { Doc } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Input } from '@/components/_shared/Input';
import { Label } from '@/components/_shared/Label';
import { Textarea } from '@/components/_shared/Textarea';
import { formatCurrency } from '@/lib/utils/formatUtils';

const positionSchema = z.object({
  id: z.string(),
  titel: z.string().min(1, 'Titel ist erforderlich'),
  beschreibung: z.string().optional(),
  menge: z.coerce.number().min(0.1, 'Menge muss > 0 sein'),
  einheit: z.string().min(1, 'Einheit ist erforderlich'),
  einzelpreis: z.coerce.number(),
});

const positionFormSchema = z.object({
  positionen: z
    .array(positionSchema)
    .min(1, 'Es muss mindestens eine Position vorhanden sein'),
});

type PositionFormData = z.infer<typeof positionFormSchema>;

interface CompactPositionFormProps {
  initialData?: { positionen: Doc<'kunden_angebote'>['positionen'] };
  onSubmit: (data: {
    positionen: any[];
    gesamtsummeNetto: number;
    gesamtsummeBrutto: number;
  }) => void;
  isSubmitting: boolean;
  formId: string;
  onChangesDetected?: (hasChanges: boolean) => void;
}

export function CompactPositionForm({
  initialData,
  onSubmit,
  isSubmitting,
  formId,
  onChangesDetected,
}: CompactPositionFormProps) {
  const {
    register,
    control,
    handleSubmit,
    watch,
    formState: { errors, isDirty },
  } = useForm<PositionFormData>({
    resolver: zodResolver(positionFormSchema),
    defaultValues: initialData || {
      positionen: [],
    },
  });

  // Notify parent about changes
  useEffect(() => {
    onChangesDetected?.(isDirty);
  }, [isDirty, onChangesDetected]);

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'positionen',
  });

  const positionen = watch('positionen');

  const subtotal = positionen.reduce((acc, pos) => {
    const menge = Number(pos.menge) || 0;
    const einzelpreis = Number(pos.einzelpreis) || 0;
    return acc + menge * einzelpreis;
  }, 0);

  const taxAmount = subtotal * 0.19;
  const total = subtotal + taxAmount;

  const handleFormSubmit = (data: PositionFormData) => {
    onSubmit({
      positionen: data.positionen,
      gesamtsummeNetto: subtotal,
      gesamtsummeBrutto: total,
    });
  };

  return (
    <form
      className="space-y-4"
      id={formId}
      onSubmit={handleSubmit(handleFormSubmit)}
    >
      <Card>
        <CardHeader className="p-4 pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="font-medium text-base">
              Angebotspositionen
            </CardTitle>
            <Button
              className="gap-1"
              onClick={() =>
                append({
                  id: crypto.randomUUID(),
                  titel: '',
                  beschreibung: '',
                  einheit: 'Stk',
                  menge: 1,
                  einzelpreis: 0,
                })
              }
              size="sm"
              type="button"
            >
              <Plus className="h-3.5 w-3.5" />
              Position
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-4 pt-0">
          <div className="space-y-2">
            {fields.length === 0 && (
              <div className="py-8 text-center text-gray-400 text-sm">
                Noch keine Positionen hinzugefügt.
                <br />
                Klicken Sie auf "Position" um zu beginnen.
              </div>
            )}
            {fields.map((field, index) => (
              <div
                className="rounded-lg border border-gray-600 bg-gray-800/50 p-3"
                key={field.id}
              >
                {/* Title and Description */}
                <div className="mb-2 grid grid-cols-[1fr_auto] gap-2">
                  <div className="space-y-1">
                    <Input
                      {...register(`positionen.${index}.titel`)}
                      className="font-medium text-sm"
                      placeholder="Titel der Position"
                    />
                    <Textarea
                      {...register(`positionen.${index}.beschreibung`)}
                      className="min-h-[60px] resize-y text-gray-400 text-xs"
                      placeholder="Beschreibung (optional) - Unterstützt Absätze"
                      rows={2}
                    />
                  </div>
                  <Button
                    className="h-8 w-8 p-0 text-red-400 hover:text-red-300"
                    onClick={() => remove(index)}
                    size="sm"
                    type="button"
                    variant="outline"
                  >
                    <Trash2 className="h-3.5 w-3.5" />
                  </Button>
                </div>

                {/* Quantity, Unit, Price */}
                <div className="grid grid-cols-3 gap-2">
                  <div>
                    <Label
                      className="text-gray-400 text-xs"
                      htmlFor={`menge-${index}`}
                    >
                      Menge
                    </Label>
                    <Input
                      {...register(`positionen.${index}.menge`)}
                      className="text-sm"
                      placeholder="1"
                      step="0.1"
                      type="number"
                    />
                  </div>
                  <div>
                    <Label
                      className="text-gray-400 text-xs"
                      htmlFor={`einheit-${index}`}
                    >
                      Einheit
                    </Label>
                    <Input
                      {...register(`positionen.${index}.einheit`)}
                      className="text-sm"
                      placeholder="Stk"
                    />
                  </div>
                  <div>
                    <Label
                      className="text-gray-400 text-xs"
                      htmlFor={`preis-${index}`}
                    >
                      Einzelpreis (€)
                    </Label>
                    <Input
                      {...register(`positionen.${index}.einzelpreis`)}
                      className="text-sm"
                      placeholder="0.00"
                      step="0.01"
                      type="number"
                    />
                  </div>
                </div>

                {/* Position total */}
                <div className="mt-2 text-right text-gray-400 text-sm">
                  {formatCurrency(
                    (Number(positionen[index]?.menge) || 0) *
                      (Number(positionen[index]?.einzelpreis) || 0)
                  )}
                </div>
              </div>
            ))}
          </div>
          {errors.positionen && (
            <p className="mt-2 text-red-500 text-xs">
              {errors.positionen.message}
            </p>
          )}
        </CardContent>
      </Card>

      {/* Totals Summary */}
      <Card>
        <CardHeader className="p-4 pb-2">
          <CardTitle className="font-medium text-base">
            Zusammenfassung
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4 pt-0">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Zwischensumme (Netto)</span>
              <span>{formatCurrency(subtotal)}</span>
            </div>
            <div className="flex justify-between">
              <span>Umsatzsteuer (19%)</span>
              <span>{formatCurrency(taxAmount)}</span>
            </div>
            <div className="flex justify-between border-gray-600 border-t pt-2 font-bold text-base">
              <span>Gesamtsumme (Brutto)</span>
              <span>{formatCurrency(total)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </form>
  );
}
