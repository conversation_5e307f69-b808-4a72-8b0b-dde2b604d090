import { FileText, Search } from 'lucide-react';
import { Button } from '@/components/_shared/Button';
import { EmptyState } from '@/components/layout/EmptyState';

interface AngeboteEmptyStateProps {
  searchTerm: string;
  filterStatus: 'all' | 'entwurf' | 'fertig';
  filterZeitraum: 'all' | 'last7' | 'last30' | 'month' | 'lastMonth' | 'custom';
  filterKundeId?: string;
  onResetFilters: () => void;
}

export function AngeboteEmptyState({
  searchTerm,
  filterStatus,
  filterZeitraum,
  filterKundeId = 'all',
  onResetFilters,
}: AngeboteEmptyStateProps) {
  const hasFilters =
    searchTerm !== '' ||
    filterStatus !== 'all' ||
    filterZeitraum !== 'all' ||
    filterKundeId !== 'all';

  if (hasFilters) {
    return (
      <EmptyState
        actions={
          <Button onClick={onResetFilters} size="sm" variant="outline">
            Filter zurücksetzen
          </Button>
        }
        icon={<Search className="h-12 w-12" />}
        message="Es wurden keine Angebote gefunden, die den aktuellen Filterkriterien entsprechen. Versuchen Sie, die Filter anzupassen oder zurückzusetzen."
        title="Keine Angebote gefunden"
      />
    );
  }

  return (
    <EmptyState
      icon={<FileText className="h-12 w-12" />}
      message="Es wurden noch keine Angebote erstellt. Erstellen Sie ein neues Angebot, um Ihre Leistungen anzubieten."
      title="Keine Angebote vorhanden"
    />
  );
}
