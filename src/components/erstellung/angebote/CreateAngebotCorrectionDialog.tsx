import { useMutation } from 'convex/react';
import { FilePenLine } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { AppDialogLayout } from '@/components/layout/AppDialogLayout';

interface CreateAngebotCorrectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  originalId: Id<'kunden_angebote'>;
}

export function CreateAngebotCorrectionDialog({
  isOpen,
  onClose,
  originalId,
}: CreateAngebotCorrectionDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  const createCorrection = useMutation(api.erstellung.angebot.createCorrection);

  const handleSubmit = async () => {
    if (isSubmitting) {
      return;
    }

    setIsSubmitting(true);
    try {
      const correctionId = await createCorrection({
        originalId,
      });

      toast.success('Angebot-Korrektur erfolgreich erstellt.');
      onClose();
      navigate(`/erstellung/angebote/${correctionId}`);
    } catch (error) {
      toast.error(
        `Fehler beim Erstellen der Korrektur: ${
          error instanceof Error ? error.message : 'Unbekannter Fehler'
        }`
      );
      setIsSubmitting(false);
    }
  };

  return (
    <AppDialogLayout
      description="Erstellen Sie eine Korrektur für dieses Angebot. Die Korrektur wird als Entwurf erstellt und kann bearbeitet werden."
      footerAction={{
        label: 'Korrektur erstellen',
        onClick: handleSubmit,
        icon: <FilePenLine className="h-3.5 w-3.5" />,
        disabled: isSubmitting,
        loading: isSubmitting,
      }}
      icon={<FilePenLine className="h-4 w-4 text-orange-500" />}
      isOpen={isOpen}
      maxWidth="md"
      onClose={onClose}
      title="Angebot-Korrektur erstellen"
    >
      <div className="space-y-4">
        <div className="rounded-lg bg-gray-800/50 p-3 text-gray-400 text-sm">
          <p className="mb-2 font-medium">Hinweise:</p>
          <ul className="space-y-1 text-xs">
            <li>• Die Korrektur wird als Entwurf erstellt</li>
            <li>• Alle Positionen und Einstellungen werden kopiert</li>
            <li>
              • Nach der Finalisierung erhält die Korrektur eine Korrekturnummer
            </li>
            <li>• Das Original wird als "korrigiert" markiert</li>
          </ul>
        </div>
      </div>
    </AppDialogLayout>
  );
}
