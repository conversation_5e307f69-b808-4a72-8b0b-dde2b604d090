import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from 'convex/react';
import { Plus, Trash2 } from 'lucide-react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import * as z from 'zod';
import { api } from '@/../convex/_generated/api';
import type { Doc } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Input } from '@/components/_shared/Input';
import { Label } from '@/components/_shared/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/_shared/select';
import { Textarea } from '@/components/_shared/Textarea';
import { formatCurrency } from '@/lib/utils/formatUtils';

const positionSchema = z.object({
  id: z.string(),
  titel: z.string().min(1, 'Titel ist erforderlich'),
  beschreibung: z.string().optional(),
  menge: z.coerce.number().min(0.1, 'Menge muss > 0 sein'),
  einheit: z.string().min(1, 'Einheit ist erforderlich'),
  einzelpreis: z.coerce.number(),
});

const angebotSchema = z.object({
  kundenId: z.string().min(1, 'Kunde ist erforderlich'),
  gueltigBis: z.coerce.date({
    required_error: 'Gültigkeitsdatum ist erforderlich',
  }),
  bemerkung: z.string().optional(),
  positionen: z
    .array(positionSchema)
    .min(1, 'Es muss mindestens eine Position vorhanden sein'),
});

type AngebotFormData = z.infer<typeof angebotSchema>;

interface AngebotPageFormProps {
  initialData?: Partial<Doc<'kunden_angebote'>>;
  onSubmit: (data: any) => void;
  isSubmitting: boolean;
  formId: string;
}

export function AngebotPageForm({
  initialData,
  onSubmit,
  isSubmitting,
  formId,
}: AngebotPageFormProps) {
  const kunden = useQuery(api.verwaltung.kunden.list) || [];
  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<AngebotFormData>({
    resolver: zodResolver(angebotSchema),
    defaultValues: initialData
      ? {
          ...initialData,
          gueltigBis: new Date(initialData.gueltigBis ?? new Date()),
        }
      : {
          kundenId: '',
          gueltigBis: new Date(new Date().setDate(new Date().getDate() + 14)),
          positionen: [],
        },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'positionen',
  });

  const positionen = watch('positionen');
  const totals = positionen.reduce(
    (acc: { netto: number }, pos: { menge: number; einzelpreis: number }) => {
      const menge = Number(pos.menge) || 0;
      const einzelpreis = Number(pos.einzelpreis) || 0;
      const posTotal = menge * einzelpreis;
      acc.netto += posTotal;
      return acc;
    },
    { netto: 0 }
  );
  const umsatzsteuerValue = totals.netto * 0.19;
  const brutto = totals.netto + umsatzsteuerValue;

  const handleFormSubmit = (data: AngebotFormData) => {
    const finalData = {
      ...data,
      gueltigBis: new Date(data.gueltigBis).getTime(),
      gesamtsummeNetto: totals.netto,
      gesamtsummeBrutto: brutto,
    };
    onSubmit(finalData);
  };

  return (
    <form
      className="space-y-4"
      id={formId}
      onSubmit={handleSubmit(handleFormSubmit)}
    >
      <Card>
        <CardHeader>
          <CardTitle>Grunddaten</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div>
            <Label htmlFor="kundenId">Kunde</Label>
            <Controller
              control={control}
              name="kundenId"
              render={({ field }) => (
                <Select
                  defaultValue={field.value}
                  onValueChange={field.onChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Kunde auswählen" />
                  </SelectTrigger>
                  <SelectContent>
                    {kunden.map((k) => (
                      <SelectItem key={k._id} value={k._id}>
                        {k.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.kundenId && (
              <p className="mt-1 text-red-500 text-xs">
                {errors.kundenId.message}
              </p>
            )}
          </div>
          <div>
            <Label htmlFor="gueltigBis">Gültig bis</Label>
            <Input type="date" {...register('gueltigBis')} />
            {errors.gueltigBis && (
              <p className="mt-1 text-red-500 text-xs">
                {errors.gueltigBis.message}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Positionen</CardTitle>
          <Button
            onClick={() =>
              append({
                id: crypto.randomUUID(),
                titel: '',
                beschreibung: '',
                einheit: 'Stk',
                menge: 1,
                einzelpreis: 0,
              })
            }
            size="sm"
            type="button"
          >
            <Plus className="mr-2 h-4 w-4" /> Position hinzufügen
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {fields.map((field, index) => (
              <div className="space-y-2 rounded-md border p-3" key={field.id}>
                <div className="grid grid-cols-[1fr_80px_80px_120px_auto] items-start gap-2">
                  <div className="space-y-1">
                    <Input
                      {...register(`positionen.${index}.titel`)}
                      placeholder="Titel"
                    />
                    <Input
                      {...register(`positionen.${index}.beschreibung`)}
                      className="text-gray-400 text-xs"
                      placeholder="Beschreibung (optional)"
                    />
                  </div>
                  <Input
                    {...register(`positionen.${index}.menge`)}
                    placeholder="Menge"
                    step="0.1"
                    type="number"
                  />
                  <Input
                    {...register(`positionen.${index}.einheit`)}
                    placeholder="Einheit"
                  />
                  <Input
                    {...register(`positionen.${index}.einzelpreis`)}
                    placeholder="Preis/Einheit"
                    step="0.01"
                    type="number"
                  />
                  <Button
                    onClick={() => remove(index)}
                    size="icon"
                    type="button"
                    variant="destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
          {errors.positionen && (
            <p className="mt-2 text-red-500 text-xs">
              {errors.positionen.message}
            </p>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <div className="w-full max-w-sm space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Zwischensumme (Netto)</span>{' '}
            <span>{formatCurrency(totals.netto)}</span>
          </div>
          <div className="flex justify-between">
            <span>Umsatzsteuer (19%)</span>{' '}
            <span>{formatCurrency(umsatzsteuerValue)}</span>
          </div>
          <div className="flex justify-between font-bold text-base">
            <span>Gesamtsumme (Brutto)</span>{' '}
            <span>{formatCurrency(brutto)}</span>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Bemerkung</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            {...register('bemerkung')}
            placeholder="Optionale Bemerkungen zum Angebot..."
          />
        </CardContent>
      </Card>
    </form>
  );
}
