import { forwardRef, useCallback, useImperative<PERSON>andle, useState } from 'react';
import { toast } from 'sonner';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Input } from '@/components/_shared/Input';
import { Label } from '@/components/_shared/Label';

interface GrunddatenCardProps {
  angebot: {
    kundenId: string;
    kundeName: string;
    gueltigBis: number;
    status: string;
  };
  onUpdate: (data: { gueltigBis: number }) => Promise<void>;
  onChangesDetected?: (hasChanges: boolean) => void;
}

export interface GrunddatenCardRef {
  save: () => Promise<void>;
  hasChanges: boolean;
}

export const GrunddatenCard = forwardRef<
  GrunddatenCardRef,
  GrunddatenCardProps
>(({ angebot, onUpdate, onChangesDetected }, ref) => {
  const [gueltigBis, setGueltigBis] = useState(
    new Date(angebot.gueltigBis).toISOString().split('T')[0]
  );
  const [hasChanges, setHasChanges] = useState(false);

  const handleSave = useCallback(async () => {
    try {
      await onUpdate({
        gueltigBis: new Date(gueltigBis).getTime(),
      });
      const newHasChanges = false;
      setHasChanges(newHasChanges);
      onChangesDetected?.(newHasChanges);
      toast.success('Grunddaten erfolgreich aktualisiert');
    } catch (_error) {
      toast.error('Fehler beim Speichern der Grunddaten');
    }
  }, [gueltigBis, onUpdate, onChangesDetected]);

  const handleFieldChange = () => {
    const newHasChanges = true;
    setHasChanges(newHasChanges);
    onChangesDetected?.(newHasChanges);
  };

  const isEditable = angebot.status === 'entwurf';

  useImperativeHandle(ref, () => ({
    save: handleSave,
    hasChanges,
  }));

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="p-3 pb-2">
        <CardTitle className="font-medium text-base">Grunddaten</CardTitle>
      </CardHeader>
      <CardContent className="p-3 pt-0">
        <div className="space-y-3">
          {/* Gültig bis */}
          <div>
            <Label
              className="font-medium text-gray-300 text-sm"
              htmlFor="gueltigBis"
            >
              Gültig bis
            </Label>
            <Input
              className="text-sm"
              disabled={!isEditable}
              id="gueltigBis"
              onChange={(e) => {
                setGueltigBis(e.target.value);
                handleFieldChange();
              }}
              type="date"
              value={gueltigBis}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

GrunddatenCard.displayName = 'GrunddatenCard';
