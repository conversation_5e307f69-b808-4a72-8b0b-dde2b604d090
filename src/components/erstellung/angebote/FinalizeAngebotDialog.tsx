import { useMutation } from 'convex/react';
import { CheckCircle } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { AppDialogLayout } from '@/components/layout/AppDialogLayout';

interface FinalizeAngebotDialogProps {
  isOpen: boolean;
  onClose: () => void;
  angebotId: Id<'kunden_angebote'>;
  isCorrection?: boolean;
}

export function FinalizeAngebotDialog({
  isOpen,
  onClose,
  angebotId,
  isCorrection = false,
}: FinalizeAngebotDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  const finalizeAngebot = useMutation(api.erstellung.angebot.finalize);

  const handleFinalize = async () => {
    setIsSubmitting(true);
    try {
      await finalizeAngebot({ angebotId });

      toast.success(
        isCorrection
          ? 'Angebot-Korrektur erfolgreich finalisiert.'
          : 'Angebot erfolgreich finalisiert.'
      );

      onClose();

      // Redirect to angebote list
      navigate('/erstellung/angebote');
    } catch (error) {
      toast.error(
        `Fehler beim Finalisieren: ${
          error instanceof Error ? error.message : 'Unbekannter Fehler'
        }`
      );
      setIsSubmitting(false);
    }
  };

  return (
    <AppDialogLayout
      description={
        isCorrection
          ? 'Nach der Finalisierung wird eine Korrekturnummer vergeben und die Korrektur kann nicht mehr bearbeitet werden.'
          : 'Nach der Finalisierung wird eine Angebotsnummer vergeben und das Angebot kann nicht mehr bearbeitet werden.'
      }
      footerAction={{
        label: isCorrection ? 'Korrektur finalisieren' : 'Angebot finalisieren',
        onClick: handleFinalize,
        disabled: isSubmitting,
      }}
      icon={<CheckCircle className="h-4 w-4 text-green-500" />}
      isOpen={isOpen}
      maxWidth="sm"
      onClose={onClose}
      title={
        isCorrection ? 'Angebot-Korrektur finalisieren' : 'Angebot finalisieren'
      }
    >
      <div className="space-y-4">
        <p className="mb-4 text-yellow-400">
          <strong>Achtung:</strong> Diese Aktion kann nicht rückgängig gemacht
          werden. Nach der Finalisierung sind keine Änderungen mehr möglich.
        </p>

        {isCorrection && (
          <p className="text-gray-400 text-sm">
            Hinweis: Die erste Korrekturversion erhält die Nummer ".2" (Version
            ".1" wird übersprungen).
          </p>
        )}
      </div>
    </AppDialogLayout>
  );
}
