import type { ReactNode } from 'react';
import {
  type FilterConfig,
  GenericFilterControls,
} from '@/components/layout/GenericFilterControls';

interface FilterBarProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  placeholder?: string;
  rightSection?: ReactNode;
  onResetAllFilters?: () => void; // Added for consistency if a reset is ever needed here
}

export function FilterBar({
  searchValue,
  onSearchChange,
  placeholder = 'Suchen...',
  rightSection,
  onResetAllFilters,
}: FilterBarProps) {
  const filtersConfig: FilterConfig[] = [
    {
      type: 'search',
      id: 'filterBarSearch',
      value: searchValue,
      onChange: onSearchChange,
      placeholder,
      className: 'flex-1', // Original search input div was flex-1
      inputClassName: 'h-8 pl-8 text-xs bg-gray-900 border-gray-700', // Original input class
    },
  ];

  if (rightSection) {
    filtersConfig.push({
      type: 'custom',
      id: 'filterBarRightSection',
      node: <div>{rightSection}</div>, // Original div wrapper for rightSection
    });
  }

  return (
    <GenericFilterControls
      className="flex items-center justify-between gap-2"
      filters={filtersConfig}
      // Original wrapper: "flex items-center justify-between gap-2 pb-2"
      // GenericFilterControls default: "flex flex-wrap items-center gap-2"
      // We need to ensure justify-between and pb-2 are handled.
      // globalFilterContainerClassName can take pb-2
      // className can take "flex items-center justify-between gap-2"
      globalFilterContainerClassName="pb-2"
      onResetAll={onResetAllFilters}
    />
  );
}
