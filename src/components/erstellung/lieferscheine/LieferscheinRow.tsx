import {
  AlertTriangle,
  ChevronDown,
  ChevronUp,
  FileText,
  Trash2,
} from 'lucide-react';
import { Link } from 'react-router-dom';
import type { Doc, Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import { TableCell, TableRow } from '@/components/_shared/Table';
import { EmailButton } from '@/components/system/email';

interface LieferscheinRowProps {
  lieferschein: Doc<'kunden_lieferscheine'> & {
    kundeName: string;
    erstelltAmFormatiert: string;
    hatKorrektur: boolean;
  };
  korrekturen: Array<
    Doc<'kunden_lieferscheine'> & {
      kundeName: string;
      erstelltAmFormatiert: string;
      hatKorrektur: boolean;
    }
  >;
  original:
    | (Doc<'kunden_lieferscheine'> & {
        kundeName: string;
        erstelltAmFormatiert: string;
        hatKorrektur: boolean;
      })
    | null;
  onDelete: (id: Id<'kunden_lieferscheine'>) => void;
  isExpanded: boolean;
  onToggleExpand: () => void;
}

export function LieferscheinRow({
  lieferschein,
  korrekturen,
  original,
  onDelete,
  isExpanded,
  onToggleExpand,
}: LieferscheinRowProps) {
  // Bestimme, ob es Korrekturen oder ein Original gibt, die angezeigt werden können
  const hasRelatedDocuments = korrekturen.length > 0 || original !== null;

  return (
    <>
      {/* Hauptzeile */}
      <TableRow className="border-gray-800 border-b">
        <TableCell className="font-medium">
          <div className="flex items-center">
            {hasRelatedDocuments && (
              <Button
                className="mr-2 h-6 w-6 text-gray-400 hover:text-gray-300"
                onClick={onToggleExpand}
                size="icon"
                title={isExpanded ? 'Einklappen' : 'Ausklappen'}
                variant="ghost"
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            )}
            <span>
              {lieferschein.nummer}
              {lieferschein.istKorrektur && (
                <span className="ml-2 text-orange-400 text-xs">
                  (Korrektur)
                </span>
              )}
            </span>
          </div>
        </TableCell>
        <TableCell>{lieferschein.kundeName}</TableCell>
        <TableCell>{lieferschein.erstelltAmFormatiert}</TableCell>
        <TableCell>
          {lieferschein.status === 'entwurf' ? (
            <div className="flex items-center text-blue-400">
              <FileText className="mr-1 h-4 w-4" />
              <span>Entwurf</span>
            </div>
          ) : lieferschein.hatKorrektur ? (
            <div className="flex items-center text-orange-400">
              <AlertTriangle className="mr-1 h-4 w-4" />
              <span>Korrigiert</span>
            </div>
          ) : (
            <span className="text-green-400">Fertig</span>
          )}
        </TableCell>
        <TableCell>
          <div className="flex justify-center gap-1">
            <Link
              to={`/erstellung/lieferscheine/${lieferschein.nummer || lieferschein._id}`}
            >
              <Button
                className="h-8 w-8 text-gray-400 hover:text-blue-400"
                size="icon"
                title="Anzeigen"
                variant="ghost"
              >
                <FileText className="h-4 w-4" />
                <span className="sr-only">Anzeigen</span>
              </Button>
            </Link>
            {lieferschein.status === 'fertig' && (
              <EmailButton
                lieferscheinId={lieferschein._id}
                tooltipText="Per E-Mail versenden"
                type="lieferschein"
              />
            )}
            {!lieferschein.istKorrektur && lieferschein.status === 'fertig' && (
              <Link
                state={{ openCorrectionDialog: true }}
                to={`/erstellung/lieferscheine/${lieferschein.nummer || lieferschein._id}`}
              >
                <Button
                  className="h-8 w-8 text-gray-400 hover:text-orange-400"
                  size="icon"
                  title="Korrektur erstellen"
                  variant="ghost"
                >
                  <AlertTriangle className="h-4 w-4" />
                  <span className="sr-only">Korrektur</span>
                </Button>
              </Link>
            )}
            {/* Lösch-Button für Entwürfe und Korrekturen im Entwurfsstatus */}
            {lieferschein.status === 'entwurf' && (
              <Button
                className="h-8 w-8 text-gray-400 hover:text-red-400"
                onClick={() => onDelete(lieferschein._id)}
                size="icon"
                title="Löschen"
                variant="ghost"
              >
                <Trash2 className="h-4 w-4" />
                <span className="sr-only">Löschen</span>
              </Button>
            )}
          </div>
        </TableCell>
      </TableRow>

      {/* Untergeordnete Zeilen (Korrekturen und Original) */}
      {isExpanded && (
        <>
          {/* Andere Korrekturen (außer der neuesten) */}
          {korrekturen.map((korrektur) => (
            <TableRow
              className="border-gray-800 border-b bg-gray-800/20"
              key={korrektur._id}
            >
              <TableCell className="pl-10 font-medium">
                {korrektur.nummer}
                <span className="ml-2 text-orange-400 text-xs">
                  (Korrektur)
                </span>
              </TableCell>
              <TableCell>{korrektur.kundeName}</TableCell>
              <TableCell>{korrektur.erstelltAmFormatiert}</TableCell>
              <TableCell>
                {korrektur.status === 'entwurf' ? (
                  <div className="flex items-center text-blue-400">
                    <FileText className="mr-1 h-4 w-4" />
                    <span>Entwurf</span>
                  </div>
                ) : (
                  <span className="text-green-400">Fertig</span>
                )}
              </TableCell>
              <TableCell>
                <div className="flex justify-center gap-1">
                  <Link
                    to={`/erstellung/lieferscheine/${korrektur.nummer || korrektur._id}`}
                  >
                    <Button
                      className="h-8 w-8 text-gray-400 hover:text-blue-400"
                      size="icon"
                      title="Anzeigen"
                      variant="ghost"
                    >
                      <FileText className="h-4 w-4" />
                      <span className="sr-only">Anzeigen</span>
                    </Button>
                  </Link>
                  {korrektur.status === 'fertig' && (
                    <EmailButton
                      lieferscheinId={korrektur._id}
                      tooltipText="Per E-Mail versenden"
                      type="lieferschein"
                    />
                  )}
                  {korrektur.status === 'entwurf' && (
                    <Button
                      className="h-8 w-8 text-gray-400 hover:text-red-400"
                      onClick={() => onDelete(korrektur._id)}
                      size="icon"
                      title="Löschen"
                      variant="ghost"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Löschen</span>
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}

          {/* Original-Dokument (ganz unten) */}
          {original && (
            <TableRow
              className="border-gray-800 border-b bg-gray-800/30"
              key={original._id}
            >
              <TableCell className="pl-10 font-medium">
                {original.nummer}
                <span className="ml-2 text-gray-400 text-xs">(Original)</span>
              </TableCell>
              <TableCell>{original.kundeName}</TableCell>
              <TableCell>{original.erstelltAmFormatiert}</TableCell>
              <TableCell>
                <span className="text-green-400">Fertig</span>
              </TableCell>
              <TableCell>
                <div className="flex justify-center gap-1">
                  <Link
                    to={`/erstellung/lieferscheine/${original.nummer || original._id}`}
                  >
                    <Button
                      className="h-8 w-8 text-gray-400 hover:text-blue-400"
                      size="icon"
                      title="Anzeigen"
                      variant="ghost"
                    >
                      <FileText className="h-4 w-4" />
                      <span className="sr-only">Anzeigen</span>
                    </Button>
                  </Link>
                  {/* Email button for the original */}
                  <EmailButton
                    lieferscheinId={original._id}
                    tooltipText="Per E-Mail versenden"
                    type="lieferschein"
                  />
                  {/* Korrektur-Button für das Original */}
                  <Link
                    state={{ openCorrectionDialog: true }}
                    to={`/erstellung/lieferscheine/${original.nummer || original._id}`}
                  >
                    <Button
                      className="h-8 w-8 text-gray-400 hover:text-orange-400"
                      size="icon"
                      title="Korrektur erstellen"
                      variant="ghost"
                    >
                      <AlertTriangle className="h-4 w-4" />
                      <span className="sr-only">Korrektur</span>
                    </Button>
                  </Link>
                </div>
              </TableCell>
            </TableRow>
          )}
        </>
      )}
    </>
  );
}
