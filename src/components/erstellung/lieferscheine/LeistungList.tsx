import { FileText } from 'lucide-react';
import { useMemo } from 'react';
import type { Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import { EmptyState } from '@/components/layout/EmptyState';
import { LeistungCard } from './LeistungCard';

interface LeistungListProps {
  leistungen: Array<{
    _id: Id<'kunden_leistungen'>;
    startZeit: number;
    endZeit: number;
    beschreibung: string;
    art: string;
    mitarbeiterName: string;
    kontingentName: string;
    stunden: number;
    inLieferscheinen?: number;
  }>;
  selectedIds: Id<'kunden_leistungen'>[];
  onToggleLeistung: (leistungId: Id<'kunden_leistungen'>) => void;
  onToggleAll: () => void;
  emptyMessage?: string;
}

export function LeistungList({
  leistungen,
  selectedIds,
  onToggleLeistung,
  onToggleAll,
  emptyMessage = '<PERSON>ine Le<PERSON>ungen verfügbar.',
}: LeistungListProps) {
  // Einfache Sortierung: Neueste zuerst (nach startZeit absteigend)
  const sortedLeistungen = useMemo(() => {
    return [...leistungen].sort((a, b) => b.startZeit - a.startZeit);
  }, [leistungen]);

  if (sortedLeistungen.length === 0) {
    return (
      <div className="py-8">
        <EmptyState
          icon={<FileText className="h-12 w-12" />}
          message={emptyMessage}
          title=""
        />
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Header mit Auswahl-Info und Alle auswählen Button */}
      <div className="flex items-center justify-between px-1">
        <span className="text-gray-300 text-sm">
          {selectedIds.length} von {sortedLeistungen.length} ausgewählt
        </span>
        <Button
          className="h-7 text-xs"
          onClick={onToggleAll}
          size="sm"
          variant="outline"
        >
          {selectedIds.length === sortedLeistungen.length
            ? 'Alle abwählen'
            : 'Alle auswählen'}
        </Button>
      </div>

      {/* Leistungsliste */}
      <div className="max-h-[400px] space-y-2 overflow-y-auto">
        {sortedLeistungen.map((leistung) => (
          <LeistungCard
            isSelected={selectedIds.includes(leistung._id)}
            key={leistung._id}
            leistung={leistung}
            onClick={onToggleLeistung}
          />
        ))}
      </div>
    </div>
  );
}
