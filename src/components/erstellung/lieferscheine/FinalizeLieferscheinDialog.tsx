import { useMutation } from 'convex/react';
import { CheckCircle } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { AppDialogLayout } from '@/components/layout/AppDialogLayout';
import { EmailDialog } from '@/components/system/email';

interface FinalizeLieferscheinDialogProps {
  isOpen: boolean;
  onClose: () => void;
  lieferscheinId: Id<'kunden_lieferscheine'>;
  isCorrection?: boolean;
}

export function FinalizeLieferscheinDialog({
  isOpen,
  onClose,
  lieferscheinId,
  isCorrection = false,
}: FinalizeLieferscheinDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [finalizedId, setFinalizedId] =
    useState<Id<'kunden_lieferscheine'> | null>(null);
  const _navigate = useNavigate();

  const finalizeLieferschein = useMutation(
    api.erstellung.lieferschein.finalize
  );
  const finalizeCorrection = useMutation(
    api.erstellung.lieferschein.finalizeCorrection
  );

  const handleFinalize = async () => {
    setIsSubmitting(true);
    try {
      let finalizedLieferscheinId: Id<'kunden_lieferscheine'>;

      if (isCorrection) {
        const result = await finalizeCorrection({
          korrekturId: lieferscheinId,
        });

        finalizedLieferscheinId = result;
        toast.success('Korrektur erfolgreich finalisiert.');
      } else {
        const result = await finalizeLieferschein({
          lieferscheinId,
        });

        finalizedLieferscheinId = result;
        toast.success('Lieferschein erfolgreich finalisiert.');
      }

      // Set the finalized ID and show email dialog
      setFinalizedId(finalizedLieferscheinId);
      setShowEmailDialog(true);
    } catch (error) {
      toast.error(
        `Fehler beim Finalisieren: ${
          error instanceof Error ? error.message : 'Unbekannter Fehler'
        }`
      );
      // Bei einem Fehler setzen wir isSubmitting zurück
      setIsSubmitting(false);
    }
  };

  // Wenn isSubmitting true ist, dann Dialog automatisch schließen
  useEffect(() => {
    if (isSubmitting && !isOpen) {
      onClose();
    }
  }, [isSubmitting, isOpen, onClose]);

  // Wenn der Dialog geschlossen wird während isSubmitting true ist, dann Dialog schließen
  const actuallyOpen = isOpen && !isSubmitting && !showEmailDialog;

  // Handle email dialog close
  const handleEmailDialogClose = () => {
    setShowEmailDialog(false);
    onClose();

    // Redirect to the lieferscheine page
    window.location.href = '/erstellung/lieferscheine';
  };

  return (
    <>
      <AppDialogLayout
        description={
          isCorrection
            ? 'Nach der Finalisierung wird eine Korrekturnummer vergeben und die Korrektur kann nicht mehr bearbeitet werden.'
            : 'Nach der Finalisierung wird eine Lieferscheinnummer vergeben und der Lieferschein kann nicht mehr bearbeitet werden.'
        }
        footerAction={{
          label: isCorrection
            ? 'Korrektur finalisieren'
            : 'Lieferschein finalisieren',
          onClick: handleFinalize,
          disabled: isSubmitting,
        }}
        icon={<CheckCircle className="h-4 w-4 text-green-500" />}
        isOpen={actuallyOpen}
        maxWidth="sm"
        onClose={onClose}
        title={
          isCorrection ? 'Korrektur finalisieren' : 'Lieferschein finalisieren'
        }
      >
        <div className="space-y-4">
          <p className="mb-4 text-yellow-400">
            <strong>Achtung:</strong> Diese Aktion kann nicht rückgängig gemacht
            werden. Nach der Finalisierung sind keine Änderungen mehr möglich.
          </p>

          {isCorrection && (
            <p className="text-gray-400 text-sm">
              Hinweis: Die erste Korrekturversion erhält die Nummer ".2"
              (Version ".1" wird übersprungen).
            </p>
          )}
        </div>
      </AppDialogLayout>

      {/* Email dialog for sending the finalized document */}
      {showEmailDialog && finalizedId && (
        <EmailDialog
          isOpen={showEmailDialog}
          lieferscheinId={finalizedId}
          onClose={handleEmailDialogClose}
          type="lieferschein"
        />
      )}
    </>
  );
}
