import { type ReactNode, useState } from 'react';

interface TabViewProps {
  tabs: {
    id: string;
    title: string;
    content: ReactNode;
  }[];
  defaultTab?: string;
}

export function TabView({ tabs, defaultTab }: TabViewProps) {
  const [activeTab, setActiveTab] = useState<string>(defaultTab || tabs[0].id);

  return (
    <div className="flex flex-col">
      <div className="flex border-gray-800 border-b">
        {tabs.map((tab) => (
          <button
            className={`px-3 py-1.5 font-medium text-sm transition-colors ${
              activeTab === tab.id
                ? 'border-blue-500 border-b-2 text-blue-400'
                : 'text-gray-400 hover:text-gray-300'
            }`}
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.title}
          </button>
        ))}
      </div>
      <div className="py-2">
        {tabs.find((tab) => tab.id === activeTab)?.content}
      </div>
    </div>
  );
}
