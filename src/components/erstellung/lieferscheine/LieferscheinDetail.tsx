import { PDFDownloadLink, PDFViewer } from '@react-pdf/renderer';
import { useMutation, useQuery } from 'convex/react';
import {
  AlertTriangle,
  ArrowLeft,
  Download,
  Eye,
  FileText,
  Plus,
  Trash2,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Link, useLocation, useParams } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { defaultLieferscheinConfig } from '@/../convex/erstellung/lieferscheineConfig';
import { Button } from '@/components/_shared/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import { PageLayout } from '@/components/layout/PageLayout';
import { EmailButton } from '@/components/system/email';
import { formatCurrency, formatHours } from '@/lib/utils/formatUtils';
import { AddLeistungDialog } from './AddLeistungDialog';
import { CreateCorrectionDialog } from './CreateCorrectionDialog';
import { FinalizeLieferscheinDialog } from './FinalizeLieferscheinDialog';
import { PDFLieferscheinDocument } from './PDFDocument';

export function LieferscheinDetailPage() {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const [lieferscheinId, setLieferscheinId] =
    useState<Id<'kunden_lieferscheine'> | null>(null);

  const [isAddLeistungDialogOpen, setIsAddLeistungDialogOpen] = useState(false);
  const [isCorrectionDialogOpen, setIsCorrectionDialogOpen] = useState(
    location.state?.openCorrectionDialog
  );
  const [isFinalizationDialogOpen, setIsFinalizationDialogOpen] =
    useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [bemerkung, setBemerkung] = useState<string | undefined>();

  // PDF settings
  const lieferscheinSettings = defaultLieferscheinConfig.settings;
  const [includeHeaderInPDF, setIncludeHeaderInPDF] = useState(
    lieferscheinSettings.includeHeader
  );
  const [includeFooterInPDF, setIncludeFooterInPDF] = useState(
    lieferscheinSettings.includeFooter
  );
  const [showLogoInPDF, setShowLogoInPDF] = useState(
    lieferscheinSettings.showLogo
  );
  const [processedLogoUrl, setProcessedLogoUrl] = useState<string | undefined>(
    undefined
  );
  const [includeLegalTextInPDF, setIncludeLegalTextInPDF] = useState(true);
  const [includeSignatureFieldInPDF, setIncludeSignatureFieldInPDF] = useState(
    lieferscheinSettings.includeSignatureField
  );

  // Load logo from the path in config
  useEffect(() => {
    // Get the logo path from the config
    const logoPath = lieferscheinSettings.logoPath;

    try {
      // Set the logo URL from configuration
      setProcessedLogoUrl(logoPath);

      // Validate logo URL by attempting to load it
      const img = new Image();
      img.onerror = () => {
        // Fallback: disable logo if loading fails
        setProcessedLogoUrl(undefined);
      };
      img.src = logoPath;
    } catch (_error) {
      setProcessedLogoUrl(undefined);
    }
  }, [lieferscheinSettings.logoPath]);

  // Check if the id is a Convex ID or a lieferschein number
  const isConvexId = id?.startsWith('k') || id?.startsWith('q');

  const isUndefined = id === 'undefined';

  const lieferscheinDataById = useQuery(
    api.erstellung.lieferschein.get,
    isConvexId && !isUndefined
      ? { id: id as Id<'kunden_lieferscheine'> }
      : 'skip'
  );

  const lieferscheinDataByNummer = useQuery(
    api.erstellung.lieferschein.getByNummer,
    isConvexId ? 'skip' : { nummer: id || '' }
  );

  const lieferscheinData = isConvexId
    ? lieferscheinDataById
    : lieferscheinDataByNummer;

  // Update lieferscheinId when data is loaded
  useEffect(() => {
    if (lieferscheinData && !lieferscheinId) {
      setLieferscheinId(lieferscheinData.lieferschein._id);
    }
  }, [lieferscheinData, lieferscheinId]);

  // Update bemerkung when lieferschein data is loaded
  useEffect(() => {
    if (lieferscheinData?.lieferschein) {
      setBemerkung(lieferscheinData.lieferschein.bemerkung);
    }
  }, [lieferscheinData]);

  const removeLeistung = useMutation(
    api.erstellung.lieferschein.removeLeistung
  );

  const updateLieferschein = useMutation(api.erstellung.lieferschein.update);

  // Bemerkung aktualisieren
  const handleUpdateBemerkung = useCallback(async () => {
    if (!lieferscheinId) {
      toast.error('Lieferschein-ID nicht verfügbar.');
      return;
    }

    try {
      await updateLieferschein({
        id: lieferscheinId,
        bemerkung: bemerkung || undefined,
      });
      toast.success('Bemerkung erfolgreich aktualisiert.');
    } catch (_error) {
      toast.error('Fehler beim Aktualisieren der Bemerkung.');
    }
  }, [lieferscheinId, bemerkung, updateLieferschein]);

  const handleRemoveLeistung = useCallback(
    async (leistungId: Id<'kunden_leistungen'>) => {
      if (!lieferscheinId) {
        toast.error('Lieferschein-ID nicht verfügbar.');
        return;
      }

      try {
        await removeLeistung({
          lieferscheinId,
          leistungId,
        });
        toast.success('Leistung erfolgreich vom Lieferschein entfernt.');
      } catch (_error) {
        toast.error('Fehler beim Entfernen der Leistung.');
      }
    },
    [lieferscheinId, removeLeistung]
  );

  // Calculate total - moved here to ensure hooks are called in consistent order
  const _total = useMemo(() => {
    if (!lieferscheinData?.leistungen) {
      return 0;
    }
    return lieferscheinData.leistungen.reduce(
      (sum, leistung) =>
        sum +
        leistung.stunden * leistung.stundenpreis +
        (leistung.mitAnfahrt ? leistung.anfahrtskosten : 0),
      0
    );
  }, [lieferscheinData?.leistungen]);

  if (!lieferscheinData) {
    return (
      <PageLayout
        action={
          <Link to="/erstellung/lieferscheine">
            <Button variant="outline">Zurück zur Übersicht</Button>
          </Link>
        }
        subtitle="Bitte warten Sie einen Moment"
        title="Lieferschein wird geladen..."
      >
        <div className="flex h-64 items-center justify-center">
          <div className="h-10 w-10 animate-spin rounded-full border-blue-500 border-b-2" />
        </div>
      </PageLayout>
    );
  }

  const { lieferschein, leistungen, korrekturen, original } = lieferscheinData;

  // Create PDF document instance
  const pdfKey = `pdf-${lieferschein.nummer}-${leistungen.length}-${includeHeaderInPDF}-${includeFooterInPDF}-${showLogoInPDF}-${includeLegalTextInPDF}-${includeSignatureFieldInPDF}-${Date.now()}`;

  const documentInstance = (
    <PDFLieferscheinDocument
      firmenFusszeileText={lieferscheinSettings.fusszeileText}
      firmenName="innov8-IT"
      formatCurrency={formatCurrency}
      formatHours={formatHours}
      includeFooter={includeFooterInPDF}
      includeHeader={includeHeaderInPDF}
      includeLegalText={includeLegalTextInPDF}
      includeSignatureField={includeSignatureFieldInPDF}
      key={pdfKey}
      legalText={lieferscheinSettings.legalText}
      leistungen={leistungen}
      lieferschein={{
        ...lieferschein,
        nummer: lieferschein.nummer || 'Entwurf',
      }}
      logoUrl={processedLogoUrl}
      showLogo={showLogoInPDF}
      signatureText={lieferscheinSettings.signatureText}
    />
  );

  const pdfFileName = `ls${lieferschein.nummer || lieferschein._id}.pdf`;

  return (
    <PageLayout
      action={
        <div className="flex items-center gap-2">
          <Link to="/erstellung/lieferscheine">
            <Button className="gap-2" variant="outline">
              <ArrowLeft className="h-4 w-4" />
              Zurück zur Übersicht
            </Button>
          </Link>
        </div>
      }
      subtitle={`Kunde: ${lieferschein.kundeName} | Erstellt am: ${lieferschein.erstelltAmFormatiert}`}
      title={`Lieferschein: ${lieferschein.nummer || 'Entwurf'}`}
    >
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Left Column: Controls and Options */}
        <div className="space-y-6 lg:col-span-1">
          {/* Status Card */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="p-4">
              <CardTitle className="font-medium text-base">Status</CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="space-y-4">
                {lieferschein.status === 'entwurf' ? (
                  <div className="flex items-center text-blue-400">
                    <FileText className="mr-2 h-5 w-5" />
                    <span>
                      {lieferschein.istKorrektur
                        ? 'Korrektur-Entwurf'
                        : 'Lieferschein-Entwurf'}
                    </span>
                  </div>
                ) : lieferschein.istKorrektur ? (
                  <div className="flex items-center text-orange-400">
                    <AlertTriangle className="mr-2 h-5 w-5" />
                    <span>
                      Dies ist eine Korrektur zum Lieferschein{' '}
                      {original?.nummer || ''}
                    </span>
                  </div>
                ) : lieferschein.hatKorrektur ? (
                  <div className="flex items-center text-orange-400">
                    <AlertTriangle className="mr-2 h-5 w-5" />
                    <span>
                      Zu diesem Lieferschein existieren Korrekturen. Die
                      aktuellste Version sollte verwendet werden.
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center text-green-400">
                    <FileText className="mr-2 h-5 w-5" />
                    <span>Aktiver Lieferschein</span>
                  </div>
                )}

                <div className="mt-4">
                  <h3 className="mb-1 font-medium text-sm">Bemerkung:</h3>
                  {lieferschein.status === 'entwurf' ? (
                    <>
                      <textarea
                        className="w-full rounded-md border bg-gray-800 p-2 text-sm text-white"
                        onChange={(e) => setBemerkung(e.target.value)}
                        placeholder="Bemerkung hinzufügen (optional)"
                        rows={3}
                        value={bemerkung || ''}
                      />
                      {bemerkung !== lieferschein.bemerkung && !showPreview && (
                        <div className="mt-2 flex justify-end">
                          <Button
                            className="gap-1.5"
                            onClick={handleUpdateBemerkung}
                            size="sm"
                          >
                            Bemerkung speichern
                          </Button>
                        </div>
                      )}
                    </>
                  ) : lieferschein.bemerkung ? (
                    <p className="text-gray-400 text-sm">
                      {lieferschein.bemerkung}
                    </p>
                  ) : (
                    <p className="text-gray-400 text-sm italic">
                      Keine Bemerkung vorhanden
                    </p>
                  )}
                </div>

                <div className="mt-4 flex flex-wrap gap-2">
                  {/* Download Button - für alle Status verfügbar */}
                  <PDFDownloadLink
                    document={documentInstance}
                    fileName={pdfFileName}
                    key={`download-${pdfKey}`}
                  >
                    {({ loading }) => (
                      <Button
                        className="h-10 w-10 p-0"
                        disabled={loading}
                        title="PDF herunterladen"
                        variant="outline"
                      >
                        <Download className="h-5 w-5" />
                      </Button>
                    )}
                  </PDFDownloadLink>

                  {/* Vorschau Button */}
                  <Button
                    className="h-10 w-10 p-0"
                    onClick={() => setShowPreview(!showPreview)}
                    title={
                      showPreview ? 'Vorschau ausblenden' : 'Vorschau anzeigen'
                    }
                    variant="outline"
                  >
                    <Eye className="h-5 w-5" />
                  </Button>

                  {/* Finalisieren Button - nur für Entwürfe */}
                  {lieferschein.status === 'entwurf' && (
                    <Button
                      className="h-10 w-10 bg-green-600 p-0 hover:bg-green-700"
                      onClick={() => setIsFinalizationDialogOpen(true)}
                      title={
                        lieferschein.istKorrektur
                          ? 'Korrektur finalisieren'
                          : 'Lieferschein finalisieren'
                      }
                    >
                      <FileText className="h-5 w-5" />
                    </Button>
                  )}

                  {/* E-Mail Button - nur für finalisierte Lieferscheine */}
                  {lieferschein.status === 'fertig' && lieferscheinId && (
                    <EmailButton
                      className="h-10 w-10 p-0"
                      kundeId={lieferschein.kundenId}
                      lieferscheinId={lieferschein._id}
                      type="lieferschein"
                      variant="outline"
                    />
                  )}

                  {/* Korrektur Button - nur für finalisierte Originale */}
                  {!lieferschein.istKorrektur &&
                    lieferschein.status === 'fertig' && (
                      <Button
                        className="h-10 w-10 p-0"
                        onClick={() => setIsCorrectionDialogOpen(true)}
                        title="Korrektur erstellen"
                        variant="outline"
                      >
                        <AlertTriangle className="h-5 w-5" />
                      </Button>
                    )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Related Documents Card */}
          {(korrekturen.length > 0 || original) && (
            <Card className="border-0 shadow-lg">
              <CardHeader className="p-4">
                <CardTitle className="font-medium text-base">
                  Zugehörige Dokumente
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="space-y-4">
                  {original && (
                    <div>
                      <h4 className="mb-2 font-medium text-sm">
                        Original-Lieferschein:
                      </h4>
                      <Link
                        className="flex items-center text-blue-400 hover:text-blue-300"
                        to={`/erstellung/lieferscheine/${original.nummer}`}
                      >
                        <FileText className="mr-1 h-4 w-4" />
                        {original.nummer} vom {original.erstelltAmFormatiert}
                      </Link>
                    </div>
                  )}

                  {korrekturen.length > 0 && (
                    <div>
                      <h4 className="mb-2 font-medium text-sm">Korrekturen:</h4>
                      <ul className="space-y-2">
                        {korrekturen.map((korrektur) => (
                          <li key={korrektur._id}>
                            <Link
                              className="flex items-center text-blue-400 hover:text-blue-300"
                              to={`/erstellung/lieferscheine/${korrektur.nummer}`}
                            >
                              <FileText className="mr-1 h-4 w-4" />
                              {korrektur.nummer} vom{' '}
                              {korrektur.erstelltAmFormatiert}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* PDF Settings Card */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="p-4">
              <CardTitle className="font-medium text-base">
                PDF-Einstellungen
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <label className="text-sm" htmlFor="includeHeader">
                    Header anzeigen
                  </label>
                  <input
                    checked={includeHeaderInPDF}
                    className="h-4 w-4"
                    id="includeHeader"
                    onChange={(e) => setIncludeHeaderInPDF(e.target.checked)}
                    type="checkbox"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label
                    className={`text-sm ${processedLogoUrl ? '' : 'opacity-50'}`}
                    htmlFor="showLogo"
                  >
                    Logo anzeigen
                    {!processedLogoUrl &&
                      ' (Logo wird aus der Konfiguration geladen)'}
                  </label>
                  <input
                    checked={showLogoInPDF}
                    className="h-4 w-4"
                    disabled={!processedLogoUrl}
                    id="showLogo"
                    onChange={(e) => setShowLogoInPDF(e.target.checked)}
                    type="checkbox"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm" htmlFor="includeFooter">
                    Footer anzeigen
                  </label>
                  <input
                    checked={includeFooterInPDF}
                    className="h-4 w-4"
                    id="includeFooter"
                    onChange={(e) => setIncludeFooterInPDF(e.target.checked)}
                    type="checkbox"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm" htmlFor="includeLegalText">
                    Rechtlichen Hinweis anzeigen
                  </label>
                  <input
                    checked={includeLegalTextInPDF}
                    className="h-4 w-4"
                    id="includeLegalText"
                    onChange={(e) => setIncludeLegalTextInPDF(e.target.checked)}
                    type="checkbox"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm" htmlFor="includeSignatureField">
                    Unterschriftsfeld anzeigen
                  </label>
                  <input
                    checked={includeSignatureFieldInPDF}
                    className="h-4 w-4"
                    id="includeSignatureField"
                    onChange={(e) =>
                      setIncludeSignatureFieldInPDF(e.target.checked)
                    }
                    type="checkbox"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column: PDF Preview or Leistungen */}
        <div className="lg:col-span-2">
          {showPreview ? (
            <Card className="h-[calc(100vh-12rem)] border-0 shadow-lg">
              {typeof window !== 'undefined' && (
                <PDFViewer
                  className="rounded-md"
                  height="100%"
                  key={pdfKey}
                  showToolbar={true}
                  width="100%"
                >
                  {documentInstance}
                </PDFViewer>
              )}
            </Card>
          ) : (
            <Card className="border-0 shadow-lg">
              <CardHeader className="flex flex-row items-center justify-between p-4">
                <div>
                  <CardTitle className="font-medium text-base">
                    Enthaltene Leistungen
                  </CardTitle>
                  <CardDescription>
                    {leistungen.length} Leistung(en) auf diesem Lieferschein
                  </CardDescription>
                </div>
                {lieferschein.status === 'entwurf' && (
                  <Button
                    className="h-10 w-10 p-0"
                    onClick={() => setIsAddLeistungDialogOpen(true)}
                    size="sm"
                    title="Leistung hinzufügen"
                  >
                    <Plus className="h-5 w-5" />
                  </Button>
                )}
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
                        <TableHead className="font-medium">
                          Datum / Kontingent
                        </TableHead>
                        <TableHead className="font-medium">
                          Zeit / Art
                        </TableHead>
                        <TableHead className="font-medium">
                          Mitarbeiter / Std.
                        </TableHead>
                        <TableHead className="font-medium">
                          Beschreibung
                        </TableHead>
                        {lieferschein.status === 'entwurf' && (
                          <TableHead className="w-16" />
                        )}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {leistungen.length === 0 ? (
                        <TableRow>
                          <TableCell
                            className="py-8 text-center text-gray-400"
                            colSpan={lieferschein.status === 'entwurf' ? 6 : 5}
                          >
                            Keine Leistungen auf diesem Lieferschein.
                          </TableCell>
                        </TableRow>
                      ) : (
                        <>
                          {leistungen.map((leistung) => (
                            <TableRow
                              className="border-gray-800 border-b"
                              key={leistung._id}
                            >
                              <TableCell>
                                {leistung.datum}
                                {leistung.kontingentName && (
                                  <div className="text-gray-400 text-xs">
                                    {leistung.kontingentName2
                                      ? `${leistung.kontingentName} / ${leistung.kontingentName2}`
                                      : leistung.kontingentName}
                                  </div>
                                )}
                              </TableCell>
                              <TableCell>
                                {leistung.startZeitFormatiert} -{' '}
                                {leistung.endZeitFormatiert}
                                <div className="text-gray-400 text-xs">
                                  {leistung.art}
                                </div>
                              </TableCell>
                              <TableCell>
                                {leistung.mitarbeiterName && (
                                  <div>{leistung.mitarbeiterName}</div>
                                )}
                                <div className="text-gray-400 text-xs">
                                  {formatHours(leistung.stunden)}
                                </div>
                              </TableCell>
                              <TableCell className="max-w-xs">
                                <div className="truncate">
                                  {leistung.beschreibung}
                                </div>
                              </TableCell>
                              {lieferschein.status === 'entwurf' && (
                                <TableCell>
                                  <Button
                                    className="h-8 w-8 text-gray-400 hover:text-red-400"
                                    onClick={() =>
                                      handleRemoveLeistung(leistung._id)
                                    }
                                    size="icon"
                                    title="Entfernen"
                                    variant="ghost"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                    <span className="sr-only">Entfernen</span>
                                  </Button>
                                </TableCell>
                              )}
                            </TableRow>
                          ))}
                          <TableRow className="bg-gray-800/30">
                            <TableCell
                              className="text-right font-medium"
                              colSpan={
                                lieferschein.status === 'entwurf' ? 2 : 2
                              }
                            >
                              Gesamtstunden:
                            </TableCell>
                            <TableCell className="font-medium">
                              {formatHours(
                                leistungen.reduce(
                                  (sum, l) => sum + l.stunden,
                                  0
                                )
                              )}
                            </TableCell>
                            <TableCell
                              colSpan={
                                lieferschein.status === 'entwurf' ? 2 : 1
                              }
                            />
                          </TableRow>
                        </>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Dialogs */}
      {isAddLeistungDialogOpen &&
        lieferscheinId &&
        lieferschein.status === 'entwurf' && (
          <AddLeistungDialog
            existingLeistungIds={leistungen.map((l) => l._id)}
            isOpen={isAddLeistungDialogOpen}
            kundenId={lieferschein.kundenId}
            lieferscheinId={lieferscheinId}
            onClose={() => setIsAddLeistungDialogOpen(false)}
          />
        )}

      {isCorrectionDialogOpen && lieferscheinId && (
        <CreateCorrectionDialog
          currentLeistungen={leistungen.map((l) => ({
            ...l,
            mitarbeiterName: l.mitarbeiterName || 'Unbekannt',
            kontingentName: l.kontingentName || 'Unbekannt',
            kontingentName2: l.kontingentName2,
          }))}
          isOpen={isCorrectionDialogOpen}
          onClose={() => setIsCorrectionDialogOpen(false)}
          originalId={lieferscheinId}
        />
      )}

      {isFinalizationDialogOpen && lieferscheinId && (
        <FinalizeLieferscheinDialog
          isCorrection={lieferschein.istKorrektur}
          isOpen={isFinalizationDialogOpen}
          lieferscheinId={lieferscheinId}
          onClose={() => setIsFinalizationDialogOpen(false)}
        />
      )}
    </PageLayout>
  );
}

export default LieferscheinDetailPage;
