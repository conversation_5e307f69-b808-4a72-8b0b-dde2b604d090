import { Clock, User } from 'lucide-react';
import type { Id } from '@/../convex/_generated/dataModel';
import { Badge } from '@/components/_shared/Badge';
import { Checkbox } from '@/components/_shared/Checkbox';
import { formatDate, formatHours, formatTime } from '@/lib/utils/formatUtils';

interface LeistungCardProps {
  leistung: {
    _id: Id<'kunden_leistungen'>;
    startZeit: number;
    endZeit: number;
    beschreibung: string;
    art: string;
    mitarbeiterName: string;
    kontingentName: string;
    kontingentName2?: string;
    stunden: number;
    inLieferscheinen?: number;
  };
  isSelected: boolean;
  onClick: (leistungId: Id<'kunden_leistungen'>) => void;
}

export function LeistungCard({
  leistung,
  isSelected,
  onClick,
}: LeistungCardProps) {
  // Prüfe ob die Leistung bereits in einem Lieferschein ist
  const isInLieferschein =
    leistung.inLieferscheinen && leistung.inLieferscheinen > 0;

  // Bestimme die Hintergrundfarbe basierend auf Status
  const getBackgroundColor = () => {
    if (isSelected) {
      return 'border-blue-500 bg-blue-900/20';
    }
    if (isInLieferschein) {
      return 'border-yellow-600/50 bg-yellow-900/20 hover:border-yellow-600/70 hover:bg-yellow-900/30';
    }
    return 'border-gray-700 bg-gray-800/50 hover:border-gray-600 hover:bg-gray-800/70';
  };

  return (
    <div
      className={`relative cursor-pointer rounded-md border p-3 transition-all ${getBackgroundColor()}`}
      onClick={() => onClick(leistung._id)}
    >
      {/* Checkbox */}
      <div className="absolute top-3 left-3">
        <Checkbox
          checked={isSelected}
          className="h-4 w-4"
          id={`leistung-${leistung._id}`}
          onCheckedChange={() => onClick(leistung._id)}
          onClick={(e) => e.stopPropagation()}
        />
      </div>

      {/* Content */}
      <div className="pl-7">
        {/* Header: Datum, Zeit, Art */}
        <div className="mb-2 flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm">
            <span className="font-medium text-white">
              {formatDate(leistung.startZeit)}
            </span>
            <span className="text-gray-400">
              {formatTime(leistung.startZeit)} - {formatTime(leistung.endZeit)}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              className={`text-xs ${
                leistung.art === 'remote'
                  ? 'bg-blue-600/80 text-blue-100'
                  : leistung.art === 'vor-Ort'
                    ? 'bg-orange-600/80 text-orange-100'
                    : 'bg-green-600/80 text-green-100'
              }`}
            >
              {leistung.art}
            </Badge>
            {isInLieferschein && (
              <Badge className="bg-yellow-600/80 text-xs text-yellow-100">
                In {leistung.inLieferscheinen} LS
              </Badge>
            )}
          </div>
        </div>

        {/* Beschreibung */}
        <div className="mb-2 line-clamp-2 text-gray-200 text-sm">
          {leistung.beschreibung}
        </div>

        {/* Footer: Mitarbeiter und Stunden */}
        <div className="flex items-center justify-between text-gray-400 text-xs">
          <div className="flex items-center gap-1">
            <User className="h-3 w-3" />
            <span>{leistung.mitarbeiterName}</span>
          </div>
          <div className="flex items-center gap-1 font-medium text-gray-300">
            <Clock className="h-3 w-3" />
            <span>{formatHours(leistung.stunden)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
