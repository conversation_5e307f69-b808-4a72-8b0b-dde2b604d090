import { useMutation, useQuery } from 'convex/react';
import { Plus } from 'lucide-react';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { AppDialogLayout } from '@/components/layout/AppDialogLayout';
import { LeistungList } from './LeistungList';

interface AddLeistungDialogProps {
  isOpen: boolean;
  onClose: () => void;
  lieferscheinId: Id<'kunden_lieferscheine'>;
  kundenId: Id<'kunden'>;
  existingLeistungIds: Id<'kunden_leistungen'>[];
}

export function AddLeistungDialog({
  isOpen,
  onClose,
  lieferscheinId,
  kundenId,
  existingLeistungIds,
}: AddLeistungDialogProps) {
  const [selectedLeistungIds, setSelectedLeistungIds] = useState<
    Id<'kunden_leistungen'>[]
  >([]);

  const allLeistungen = useQuery(api.erstellung.leistung.list) || [];
  const addLeistung = useMutation(api.erstellung.lieferschein.addLeistung);

  // Filter leistungen by kunde and exclude already added ones
  const availableLeistungen = useMemo(() => {
    const filtered = allLeistungen.filter(
      (l) => l.kundenId === kundenId && !existingLeistungIds.includes(l._id)
    );
    return filtered;
  }, [allLeistungen, kundenId, existingLeistungIds]);

  const handleSubmit = async () => {
    if (selectedLeistungIds.length === 0) {
      toast.error('Bitte wählen Sie mindestens eine Leistung aus.');
      return;
    }

    try {
      // Füge alle ausgewählten Leistungen nacheinander hinzu
      for (const leistungId of selectedLeistungIds) {
        await addLeistung({
          lieferscheinId,
          leistungId,
        });
      }

      const count = selectedLeistungIds.length;
      toast.success(
        `${count} Leistung${count !== 1 ? 'en' : ''} erfolgreich zum Lieferschein hinzugefügt.`
      );
      setSelectedLeistungIds([]);
      onClose();
    } catch (_error) {
      toast.error('Fehler beim Hinzufügen der Leistungen.');
    }
  };

  const toggleLeistung = (leistungId: Id<'kunden_leistungen'>) => {
    setSelectedLeistungIds((prev) =>
      prev.includes(leistungId)
        ? prev.filter((id) => id !== leistungId)
        : [...prev, leistungId]
    );
  };

  const toggleAllLeistungen = () => {
    if (selectedLeistungIds.length === availableLeistungen.length) {
      setSelectedLeistungIds([]);
    } else {
      setSelectedLeistungIds(availableLeistungen.map((l) => l._id));
    }
  };

  return (
    <AppDialogLayout
      footerAction={{
        label:
          selectedLeistungIds.length > 1
            ? `${selectedLeistungIds.length} Leistungen hinzufügen`
            : 'Leistung hinzufügen',
        onClick: handleSubmit,
        icon: <Plus className="h-3.5 w-3.5" />,
        disabled: selectedLeistungIds.length === 0,
      }}
      icon={<Plus className="h-4 w-4 text-blue-500" />}
      isOpen={isOpen}
      maxWidth="lg"
      onClose={onClose}
      title="Leistungen hinzufügen"
    >
      <LeistungList
        emptyMessage="Keine weiteren Leistungen für diesen Kunden verfügbar."
        leistungen={availableLeistungen}
        onToggleAll={toggleAllLeistungen}
        onToggleLeistung={toggleLeistung}
        selectedIds={selectedLeistungIds}
      />
    </AppDialogLayout>
  );
}
