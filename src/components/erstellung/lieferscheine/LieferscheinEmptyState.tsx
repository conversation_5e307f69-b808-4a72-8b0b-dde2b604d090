import { FileText, Search } from 'lucide-react';
import { Button } from '@/components/_shared/Button'; // Import Button for actions
import { EmptyState } from '@/components/layout/EmptyState'; // Use generic EmptyState

interface LieferscheinEmptyStateProps {
  searchTerm: string;
  filterStatus: string;
  filterZeitraum: string;
  filterKundeId?: string;
  onResetFilters: () => void;
}

export function LieferscheinEmptyState({
  searchTerm,
  filterStatus,
  filterZeitraum,
  filterKundeId = 'all',
  onResetFilters,
}: LieferscheinEmptyStateProps) {
  const hasFilters =
    searchTerm !== '' ||
    filterStatus !== 'all' ||
    filterZeitraum !== 'all' ||
    filterKundeId !== 'all';

  if (hasFilters) {
    return (
      <EmptyState
        actions={
          <Button onClick={onResetFilters} size="sm" variant="outline">
            Filter zurücksetzen
          </Button>
        }
        icon={<Search className="h-12 w-12" />}
        message="Es wurden keine Lieferscheine gefunden, die den aktuellen Filterkriterien entsprechen. Versuchen Sie, die Filter anzupassen oder zurückzusetzen."
        title="Keine Lieferscheine gefunden"
      />
    );
  }

  return (
    <EmptyState
      icon={<FileText className="h-12 w-12" />}
      message="Es wurden noch keine Lieferscheine erstellt. Erstellen Sie einen neuen Lieferschein, um Leistungen zu dokumentieren."
      title="Keine Lieferscheine vorhanden"
    />
  );
}
