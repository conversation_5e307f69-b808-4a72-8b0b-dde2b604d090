import {
  Document,
  Font,
  Image,
  Page,
  StyleSheet,
  Text,
  View,
} from '@react-pdf/renderer';
import { memo } from 'react';
import type {
  KontingentMitKunde,
  LeistungMitNamen,
} from '@/pages/erstellung/uebersicht/types';

// Register fonts (assuming Roboto is desired)
Font.register({
  family: 'Roboto',
  fonts: [
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf',
      fontWeight: 400,
    },
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-medium-webfont.ttf',
      fontWeight: 500,
    },
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf',
      fontWeight: 700,
    },
  ],
});

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    paddingTop: 30,
    paddingBottom: 60, // Bottom padding for footer
    paddingHorizontal: 30,
    fontFamily: 'Roboto',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center', // Center align items vertically
    marginBottom: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: '#DDDDDD',
    paddingBottom: 8,
  },
  headerTextContainer: {
    flexDirection: 'column',
    flex: 1, // Take up available space
  },
  headerLogoContainer: {
    width: 200,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: 200, // Even larger logo
    height: 100, // Even larger logo
    objectFit: 'contain', // Maintain aspect ratio
  },
  title: {
    fontSize: 16,
    fontWeight: 700,
    color: '#333333',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 10,
    color: '#555555',
    marginBottom: 2,
  },
  section: {
    marginTop: 8,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 700,
    color: '#444444',
    marginBottom: 6,
    paddingBottom: 3,
    borderBottomWidth: 0.5,
    borderBottomColor: '#DDDDDD',
  },
  table: {
    display: 'flex',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 0.5,
    borderColor: '#DDDDDD',
    marginBottom: 8,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderBottomColor: '#DDDDDD',
    alignItems: 'stretch', // Ensure columns have same height
    minHeight: 18,
  },
  tableRowHeader: {
    backgroundColor: '#F5F5F5',
    fontWeight: 700,
  },
  tableCol: {
    padding: 4,
    fontSize: 9,
    color: '#333333',
    // No vertical dividing lines
  },
  tableColLast: {
    // No special styling needed since we removed vertical lines
  },
  // Specific column widths for Leistungen
  colLeistungDatumKontingent: { width: '20%' },
  colLeistungZeitArt: { width: '15%' },
  colLeistungMitarbeiterStunden: { width: '20%' },
  colLeistungBeschreibung: { width: '45%' },
  // Specific column widths for Kontingente
  colKontingentName: { width: '20%' },
  colKontingentZeitraum: { width: '22%' },
  colKontingentStatus: { width: '13%' },
  colKontingentStunden: { width: '15%', textAlign: 'right' },
  colKontingentVerbraucht: { width: '15%', textAlign: 'right' },
  colKontingentRest: { width: '15%', textAlign: 'right' },

  textMuted: {
    fontSize: 8,
    color: '#777777',
    marginTop: 1,
  },
  footer: {
    position: 'absolute',
    bottom: 25,
    left: 30,
    right: 30,
    fontSize: 8,
    textAlign: 'center',
    color: '#888888',
    borderTopWidth: 0.5,
    borderTopColor: '#DDDDDD',
    paddingTop: 6,
  },
  summarySection: {
    marginTop: 12,
    marginBottom: 8,
    padding: 0, // Remove padding to match other sections
  },
  summaryTitle: {
    fontSize: 12,
    fontWeight: 700,
    color: '#444444',
    marginBottom: 6,
    paddingBottom: 3,
    borderBottomWidth: 0.5,
    borderBottomColor: '#DDDDDD',
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  summaryColumn: {
    width: '50%', // Two columns
    paddingRight: 8, // Space between columns
    marginBottom: 8,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 3,
  },
  summaryLabel: {
    fontSize: 9,
    color: '#555555',
  },
  summaryValue: {
    fontSize: 9,
    fontWeight: 500,
    color: '#333333',
  },
  pageNumber: {
    position: 'absolute',
    fontSize: 8,
    bottom: 8, // Position above footer line
    left: 0,
    right: 30, // Align to right
    textAlign: 'right',
    color: '#888888',
  },
});

interface PDFDocumentComponentProps {
  kundeName: string;
  zeitraum: string;
  leistungen: LeistungMitNamen[];
  kontingente: KontingentMitKunde[];
  includeSummary: boolean;
  includeLeistungsuebersicht: boolean;
  includeKontingentuebersicht: boolean;
  includeHeader: boolean;
  includeFooter: boolean;
  logoUrl?: string; // Logo URL
  firmenName?: string; // Company name
  showLogo?: boolean; // Control logo visibility
  formatDate: (timestamp: number) => string;
  formatTime: (timestamp: number) => string;
  formatHours: (hours: number) => string;
  formatCurrency: (amount: number) => string;
  // Company information for footer
  firmenStrasse?: string;
  firmenPlz?: string;
  firmenOrt?: string;
  firmenTelefon?: string;
  firmenEmail?: string;
  firmenWebsite?: string;
  firmenFusszeileText?: string;
}

export const PDFDocumentComponent = memo(
  ({
    kundeName,
    firmenName,
    logoUrl,
    showLogo,
    zeitraum,
    leistungen,
    kontingente,
    includeSummary,
    includeLeistungsuebersicht,
    includeKontingentuebersicht,
    includeHeader,
    includeFooter,
    formatDate,
    formatTime,
    formatHours,
    firmenStrasse,
    firmenPlz,
    firmenOrt,
    firmenTelefon,
    firmenEmail,
    firmenWebsite,
    firmenFusszeileText,
  }: PDFDocumentComponentProps) => {
    const totalLeistungStunden = leistungen.reduce(
      (sum, leistung) => sum + leistung.stunden,
      0
    );
    const totalKontingentStunden = kontingente.reduce(
      (sum, kontingent) => sum + kontingent.stunden,
      0
    );
    const totalVerbrauchteStunden = kontingente.reduce(
      (sum, kontingent) => sum + kontingent.verbrauchteStunden,
      0
    );
    const totalRestStunden = kontingente.reduce(
      (sum, kontingent) => sum + kontingent.restStunden,
      0
    );

    const shouldShowKontingente =
      kontingente.length > 0 && includeKontingentuebersicht;
    const shouldShowLeistungen =
      leistungen.length > 0 && includeLeistungsuebersicht;

    const getDocumentTitle = () => {
      if (shouldShowKontingente && shouldShowLeistungen) {
        return 'Kontingent- und Leistungsübersicht';
      }
      if (shouldShowKontingente) {
        return 'Kontingentübersicht';
      }
      if (shouldShowLeistungen) {
        return 'Leistungsübersicht';
      }
      return 'Übersicht';
    };

    return (
      <Document author="innov8-IT" title={getDocumentTitle()}>
        <Page size="A4" style={styles.page}>
          {includeHeader && (
            <View style={styles.headerContainer}>
              <View style={styles.headerTextContainer}>
                <Text style={styles.title}>{getDocumentTitle()}</Text>
                <Text style={styles.subtitle}>Kunde: {kundeName}</Text>
                <Text style={styles.subtitle}>Zeitraum: {zeitraum}</Text>
              </View>
              {/* Always render the container, but conditionally render its content */}
              <View style={styles.headerLogoContainer}>
                {showLogo && logoUrl ? (
                  <Image src={logoUrl} style={styles.logo} />
                ) : showLogo ? (
                  <Text
                    style={{
                      fontSize: 14,
                      color: '#007AFF',
                      fontWeight: 'bold',
                    }}
                  >
                    innov<Text style={{ color: '#50E3C2' }}>8</Text>-IT
                  </Text>
                ) : null}
              </View>
            </View>
          )}

          {shouldShowLeistungen && (
            <View style={styles.section} wrap={false}>
              <Text style={styles.sectionTitle}>Leistungsübersicht</Text>
              <View style={styles.table}>
                <View fixed style={[styles.tableRow, styles.tableRowHeader]}>
                  <Text
                    style={[styles.tableCol, styles.colLeistungDatumKontingent]}
                  >
                    Datum / Kontingent
                  </Text>
                  <Text style={[styles.tableCol, styles.colLeistungZeitArt]}>
                    Zeit / Art
                  </Text>
                  <Text
                    style={[
                      styles.tableCol,
                      styles.colLeistungMitarbeiterStunden,
                    ]}
                  >
                    Mitarbeiter / Std.
                  </Text>
                  <Text
                    style={[
                      styles.tableCol,
                      styles.colLeistungBeschreibung,
                      styles.tableColLast,
                    ]}
                  >
                    Beschreibung
                  </Text>
                </View>
                {leistungen.map((l) => (
                  <View key={l._id} style={styles.tableRow}>
                    <View
                      style={[
                        styles.tableCol,
                        styles.colLeistungDatumKontingent,
                      ]}
                    >
                      <Text>{formatDate(l.startZeit)}</Text>
                      <Text style={styles.textMuted}>
                        {l.kontingentName2
                          ? `${l.kontingentName} / ${l.kontingentName2}`
                          : l.kontingentName}
                      </Text>
                    </View>
                    <View style={[styles.tableCol, styles.colLeistungZeitArt]}>
                      <Text>
                        {formatTime(l.startZeit)} - {formatTime(l.endZeit)}
                      </Text>
                      <Text style={styles.textMuted}>{l.art}</Text>
                    </View>
                    <View
                      style={[
                        styles.tableCol,
                        styles.colLeistungMitarbeiterStunden,
                      ]}
                    >
                      <Text>{l.mitarbeiterName}</Text>
                      <Text style={styles.textMuted}>
                        {formatHours(l.stunden)}
                      </Text>
                    </View>
                    <Text
                      style={[
                        styles.tableCol,
                        styles.colLeistungBeschreibung,
                        styles.tableColLast,
                      ]}
                    >
                      {l.beschreibung}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {shouldShowKontingente && (
            <View style={styles.section} wrap={false}>
              <Text style={styles.sectionTitle}>Kontingentübersicht</Text>
              <View style={styles.table}>
                <View fixed style={[styles.tableRow, styles.tableRowHeader]}>
                  <Text style={[styles.tableCol, styles.colKontingentName]}>
                    Name
                  </Text>
                  <Text style={[styles.tableCol, styles.colKontingentZeitraum]}>
                    Zeitraum
                  </Text>
                  <Text style={[styles.tableCol, styles.colKontingentStatus]}>
                    Status
                  </Text>
                  <Text style={[styles.tableCol, styles.colKontingentStunden]}>
                    Gesamt
                  </Text>
                  <Text
                    style={[styles.tableCol, styles.colKontingentVerbraucht]}
                  >
                    Verbraucht
                  </Text>
                  <Text
                    style={[
                      styles.tableCol,
                      styles.colKontingentRest,
                      styles.tableColLast,
                    ]}
                  >
                    Rest
                  </Text>
                </View>
                {kontingente.map((k) => {
                  const now = Date.now();
                  let statusText = 'Inaktiv';
                  if (k.istAktiv) {
                    if (k.startDatum > now) {
                      statusText = 'Zukünftig';
                    } else if (k.endDatum < now) {
                      statusText = 'Abgelaufen';
                    } else {
                      statusText = 'Aktiv';
                    }
                  }
                  return (
                    <View key={k._id} style={styles.tableRow}>
                      <Text style={[styles.tableCol, styles.colKontingentName]}>
                        {k.name}
                      </Text>
                      <Text
                        style={[styles.tableCol, styles.colKontingentZeitraum]}
                      >
                        {formatDate(k.startDatum)} - {formatDate(k.endDatum)}
                      </Text>
                      <Text
                        style={[styles.tableCol, styles.colKontingentStatus]}
                      >
                        {statusText}
                      </Text>
                      <Text
                        style={[styles.tableCol, styles.colKontingentStunden]}
                      >
                        {formatHours(k.stunden)}
                      </Text>
                      <Text
                        style={[
                          styles.tableCol,
                          styles.colKontingentVerbraucht,
                        ]}
                      >
                        {formatHours(k.verbrauchteStunden)}
                      </Text>
                      <Text
                        style={[
                          styles.tableCol,
                          styles.colKontingentRest,
                          styles.tableColLast,
                        ]}
                      >
                        {formatHours(k.restStunden)}
                      </Text>
                    </View>
                  );
                })}
              </View>
            </View>
          )}

          {includeSummary &&
            (shouldShowLeistungen || shouldShowKontingente) && (
              <View style={styles.section} wrap={false}>
                <Text style={styles.sectionTitle}>Zusammenfassung</Text>
                <View style={styles.summaryGrid}>
                  {shouldShowKontingente && (
                    <View style={styles.summaryColumn}>
                      <Text
                        style={{
                          fontSize: 10,
                          fontWeight: 700,
                          marginBottom: 5,
                          color: '#444444',
                        }}
                      >
                        Kontingente
                      </Text>
                      <View style={styles.summaryRow}>
                        <Text style={styles.summaryLabel}>
                          Anzahl Kontingente:
                        </Text>
                        <Text style={styles.summaryValue}>
                          {kontingente.length}
                        </Text>
                      </View>
                      <View style={styles.summaryRow}>
                        <Text style={styles.summaryLabel}>Gesamtstunden:</Text>
                        <Text style={styles.summaryValue}>
                          {formatHours(totalKontingentStunden)}
                        </Text>
                      </View>
                      <View style={styles.summaryRow}>
                        <Text style={styles.summaryLabel}>
                          Verbrauchte Stunden:
                        </Text>
                        <Text style={styles.summaryValue}>
                          {formatHours(totalVerbrauchteStunden)}
                        </Text>
                      </View>
                      <View style={styles.summaryRow}>
                        <Text style={styles.summaryLabel}>
                          Verbleibende Stunden:
                        </Text>
                        <Text style={styles.summaryValue}>
                          {formatHours(totalRestStunden)}
                        </Text>
                      </View>
                    </View>
                  )}
                  {shouldShowLeistungen && (
                    <View style={styles.summaryColumn}>
                      <Text
                        style={{
                          fontSize: 10,
                          fontWeight: 700,
                          marginBottom: 5,
                          color: '#444444',
                        }}
                      >
                        Leistungen
                      </Text>
                      <View style={styles.summaryRow}>
                        <Text style={styles.summaryLabel}>
                          Anzahl Leistungen:
                        </Text>
                        <Text style={styles.summaryValue}>
                          {leistungen.length}
                        </Text>
                      </View>
                      <View style={styles.summaryRow}>
                        <Text style={styles.summaryLabel}>Gesamtstunden:</Text>
                        <Text style={styles.summaryValue}>
                          {formatHours(totalLeistungStunden)}
                        </Text>
                      </View>
                    </View>
                  )}
                </View>
              </View>
            )}

          {includeFooter && (
            <View fixed style={styles.footer}>
              {firmenFusszeileText ? (
                <Text>{firmenFusszeileText}</Text>
              ) : (
                <>
                  <Text style={{ marginBottom: 3 }}>
                    {firmenName || 'innov8-IT'} • Erstellt am{' '}
                    {new Date().toLocaleDateString('de-DE')}
                  </Text>
                  {(firmenStrasse ||
                    firmenPlz ||
                    firmenOrt ||
                    firmenTelefon ||
                    firmenEmail ||
                    firmenWebsite) && (
                    <Text style={{ fontSize: 7, color: '#666666' }}>
                      {[
                        firmenStrasse,
                        firmenPlz && firmenOrt
                          ? `${firmenPlz} ${firmenOrt}`
                          : firmenPlz || firmenOrt,
                        firmenTelefon && `Tel: ${firmenTelefon}`,
                        firmenEmail && `E-Mail: ${firmenEmail}`,
                        firmenWebsite && `Web: ${firmenWebsite}`,
                      ]
                        .filter(Boolean)
                        .join(' • ')}
                    </Text>
                  )}
                </>
              )}
            </View>
          )}
          <Text
            fixed
            render={({ pageNumber, totalPages }) =>
              `${pageNumber} / ${totalPages}`
            }
            style={styles.pageNumber}
          />
        </Page>
      </Document>
    );
  }
);

PDFDocumentComponent.displayName = 'PDFDocumentComponent';
