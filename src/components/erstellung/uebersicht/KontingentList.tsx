import { CalendarDays, Clock, Package } from 'lucide-react';
import { memo } from 'react';
import type { Id } from '@/../convex/_generated/dataModel';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Checkbox } from '@/components/_shared/Checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import type { KontingentMitKunde } from './types';

interface KontingentListProps {
  kontingente: KontingentMitKunde[];
  selectedKontingente: Id<'kunden_kontingente'>[];
  onSelectionChange: (id: Id<'kunden_kontingente'>, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  formatDate: (timestamp: number) => string;
}

const KontingentList = memo(function KontingentList({
  kontingente,
  selectedKontingente,
  onSelectionChange,
  onSelectAll,
  formatDate,
}: KontingentListProps) {
  const allSelected =
    kontingente.length > 0 && selectedKontingente.length === kontingente.length;

  // Helper function to determine status
  const getStatusInfo = (kontingent: KontingentMitKunde) => {
    const now = Date.now();

    if (!kontingent.istAktiv) {
      return {
        text: 'Inaktiv',
        color: 'text-red-400',
      };
    }

    if (kontingent.startDatum > now) {
      return {
        text: 'Zukünftig',
        color: 'text-yellow-400',
      };
    }

    if (kontingent.endDatum < now) {
      return {
        text: 'Abgelaufen',
        color: 'text-red-400',
      };
    }

    return {
      text: 'Aktiv',
      color: 'text-green-400',
    };
  };

  return (
    <Card className="mt-6 border-0 shadow-lg">
      <CardHeader className="border-gray-700/50 border-b p-4">
        <CardTitle className="font-medium text-lg">
          Kontingentübersicht
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
                <TableHead className="w-12 text-center">
                  <Checkbox
                    aria-label="Alle auswählen"
                    checked={allSelected}
                    onCheckedChange={(checked) => onSelectAll(!!checked)}
                  />
                </TableHead>
                <TableHead className="font-medium">Name</TableHead>
                <TableHead className="font-medium">Zeitraum</TableHead>
                <TableHead className="font-medium">Status</TableHead>
                <TableHead className="text-right font-medium">
                  Stunden
                </TableHead>
                <TableHead className="text-right font-medium">
                  Verbraucht
                </TableHead>
                <TableHead className="text-right font-medium">
                  Verbleibend
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {kontingente.map((kontingent) => {
                const status = getStatusInfo(kontingent);
                return (
                  <TableRow
                    className="border-gray-800 border-b"
                    key={kontingent._id}
                  >
                    <TableCell className="text-center">
                      <Checkbox
                        aria-label={`Kontingent ${kontingent.name} auswählen`}
                        checked={selectedKontingente.includes(kontingent._id)}
                        onCheckedChange={(checked) =>
                          onSelectionChange(kontingent._id, !!checked)
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1.5">
                        <Package className="h-3.5 w-3.5 text-gray-400" />
                        <span className="font-medium">{kontingent.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1.5">
                        <CalendarDays className="h-3.5 w-3.5 text-gray-400" />
                        <span>
                          {formatDate(kontingent.startDatum)} -{' '}
                          {formatDate(kontingent.endDatum)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className={status.color}>{status.text}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-1.5">
                        <Clock className="h-3.5 w-3.5 text-gray-400" />
                        <span>{kontingent.stunden.toFixed(2)} h</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {kontingent.verbrauchteStunden.toFixed(2)} h
                    </TableCell>
                    <TableCell className="text-right">
                      {kontingent.restStunden.toFixed(2)} h
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
});

export default KontingentList;
