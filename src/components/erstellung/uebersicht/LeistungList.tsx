import {
  Briefcase,
  CalendarDays,
  ChevronDown,
  ChevronUp,
  Clock,
  UserCircle,
} from 'lucide-react';
import { memo, useState } from 'react';
import type { Id } from '@/../convex/_generated/dataModel';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Checkbox } from '@/components/_shared/Checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import { formatKontingentNames } from '@/lib/utils/formatUtils';
import type { LeistungMitNamen } from '@/pages/erstellung/uebersicht/types';

interface LeistungListProps {
  leistungen: LeistungMitNamen[];
  selectedLeistungen: Id<'kunden_leistungen'>[];
  onSelectionChange: (id: Id<'kunden_leistungen'>, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  formatDate: (timestamp: number) => string;
  formatTime: (timestamp: number) => string;
  formatHours: (hours: number) => string;
  formatCurrency: (amount: number) => string;
}

const LeistungList = memo(function LeistungList({
  leistungen,
  selectedLeistungen,
  onSelectionChange,
  onSelectAll,
  formatDate,
  formatTime,
  formatHours,
  formatCurrency,
}: LeistungListProps) {
  const [expandedDescriptions, setExpandedDescriptions] = useState<
    Record<Id<'kunden_leistungen'>, boolean>
  >({});

  const toggleDescription = (id: Id<'kunden_leistungen'>) => {
    setExpandedDescriptions((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const allSelected =
    leistungen.length > 0 && selectedLeistungen.length === leistungen.length;

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="border-gray-700/50 border-b p-4">
        <CardTitle className="font-medium text-lg">
          Leistungsübersicht
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
                <TableHead className="w-12 text-center">
                  <Checkbox
                    aria-label="Alle auswählen"
                    checked={allSelected}
                    onCheckedChange={(checked) => onSelectAll(!!checked)}
                  />
                </TableHead>
                <TableHead className="w-[15%] font-medium">
                  Datum / Kontingent
                </TableHead>
                <TableHead className="w-[15%] font-medium">
                  Zeitraum / Art
                </TableHead>
                <TableHead className="w-[20%] font-medium">
                  Mitarbeiter / Stunden
                </TableHead>
                <TableHead className="font-medium">Beschreibung</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {leistungen.map((leistung) => (
                <TableRow
                  className="border-gray-800 border-b"
                  key={leistung._id}
                >
                  <TableCell className="text-center">
                    <Checkbox
                      aria-label={`Leistung vom ${formatDate(leistung.startZeit)} auswählen`}
                      checked={selectedLeistungen.includes(leistung._id)}
                      onCheckedChange={(checked) =>
                        onSelectionChange(leistung._id, !!checked)
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <div className="flex items-center gap-1.5 font-medium">
                        <CalendarDays className="h-3.5 w-3.5 text-gray-400" />
                        {formatDate(leistung.startZeit)}
                      </div>
                      <div className="mt-1 flex items-center gap-1.5 text-gray-400 text-xs">
                        <Briefcase className="h-3.5 w-3.5" />
                        {formatKontingentNames(
                          leistung.kontingentName,
                          leistung.kontingentName2
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <div className="flex items-center gap-1.5">
                        <Clock className="h-3.5 w-3.5 text-gray-400" />
                        {formatTime(leistung.startZeit)} -{' '}
                        {formatTime(leistung.endZeit)}
                      </div>
                      <div className="mt-1 inline-flex items-center text-xs">
                        <span
                          className={`rounded-sm px-1.5 py-0.5 font-medium text-[10px] uppercase ${
                            leistung.art === 'remote'
                              ? 'bg-blue-500/20 text-blue-300'
                              : leistung.art === 'vor-Ort'
                                ? 'bg-orange-500/20 text-orange-300'
                                : 'bg-green-500/20 text-green-300'
                          }`}
                        >
                          {leistung.art}
                        </span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <div className="flex items-center gap-1.5">
                        <UserCircle className="h-3.5 w-3.5 text-gray-400" />
                        <span>{leistung.mitarbeiterName}</span>
                      </div>
                      <div className="mt-1 flex items-center gap-1.5 text-gray-400 text-xs">
                        <Clock className="h-3.5 w-3.5" />
                        <span>{formatHours(leistung.stunden)}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center justify-between">
                      <div
                        className={`${
                          expandedDescriptions[leistung._id]
                            ? ''
                            : 'line-clamp-1'
                        }`}
                      >
                        {leistung.beschreibung}
                      </div>
                      <button
                        aria-label="Beschreibung ein-/ausklappen"
                        className="ml-2 text-gray-400 hover:text-white"
                        onClick={() => toggleDescription(leistung._id)}
                      >
                        {expandedDescriptions[leistung._id] ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
});

export default LeistungList;
