import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import type {
  KontingentMitKunde,
  LeistungMitNamen,
} from '@/pages/erstellung/uebersicht/types';

interface SummarySectionProps {
  leistungen: LeistungMitNamen[];
  kontingente: KontingentMitKunde[];
  includeSummaryInPDF: boolean;
  onIncludeSummaryChange: (include: boolean) => void;
  formatHours: (hours: number) => string;
}

export function SummarySection({
  leistungen,
  kontingente,
  includeSummaryInPDF,
  onIncludeSummaryChange,
  formatHours,
}: SummarySectionProps) {
  // Calculate total hours
  const totalLeistungStunden = leistungen.reduce(
    (sum, leistung) => sum + leistung.stunden,
    0
  );

  // Calculate total kontingent hours
  const totalKontingentStunden = kontingente.reduce(
    (sum, kontingent) => sum + kontingent.stunden,
    0
  );
  const totalVerbrauchteStunden = kontingente.reduce(
    (sum, kontingent) => sum + kontingent.verbrauchteStunden,
    0
  );
  const totalRestStunden = kontingente.reduce(
    (sum, kontingent) => sum + kontingent.restStunden,
    0
  );

  return (
    <Card className="mt-6 border-0 shadow-lg">
      <CardHeader className="border-gray-700/50 border-b p-4">
        <CardTitle className="font-medium text-lg">Zusammenfassung</CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* Kontingente Summary - Now first */}
          {kontingente.length > 0 && (
            <div>
              <h3 className="mb-2 font-medium text-sm">Kontingente</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">Anzahl Kontingente:</span>
                  <span>{kontingente.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Gesamtstunden:</span>
                  <span>{formatHours(totalKontingentStunden)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Verbrauchte Stunden:</span>
                  <span>{formatHours(totalVerbrauchteStunden)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Verbleibende Stunden:</span>
                  <span>{formatHours(totalRestStunden)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Leistungen Summary - Now second */}
          {leistungen.length > 0 && (
            <div>
              <h3 className="mb-2 font-medium text-sm">Leistungen</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">Anzahl Leistungen:</span>
                  <span>{leistungen.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Gesamtstunden:</span>
                  <span>{formatHours(totalLeistungStunden)}</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default SummarySection;
