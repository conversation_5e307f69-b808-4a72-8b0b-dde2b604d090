import { useMutation, useQuery } from 'convex/react';
import {
  AlertCircle,
  Check,
  Pencil,
  Plus,
  PlusCircle,
  Split,
  Trash2,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Doc, Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import { Input } from '@/components/_shared/Input';
import { Label } from '@/components/_shared/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/_shared/select';
import { Textarea } from '@/components/_shared/Textarea';
import { AppDialogLayout } from '@/components/layout/AppDialogLayout';
import {
  calculateHours,
  formatCurrency,
  formatHours,
  toInputDate,
  toInputTime,
} from '@/lib/utils/formatUtils';

// Interfaces
interface LeistungMitNamen {
  _id: Id<'kunden_leistungen'>;
  kundenId: Id<'kunden'>;
  mitarbeiterId: Id<'mitarbeiter'>;
  kontingentId: Id<'kunden_kontingente'>;
  kontingentId2?: Id<'kunden_kontingente'>;
  startZeit: number;
  endZeit: number;
  art: string;
  beschreibung: string;
  stundenpreis: number;
  anfahrtskosten: number;
}
interface KontingentOption {
  _id: Id<'kunden_kontingente'>;
  name: string;
  startDatum: number;
  stunden: number;
  verbrauchteStunden: number;
}
type KundeDoc = Doc<'kunden'>;
type MitarbeiterDoc = Doc<'mitarbeiter'>;

type LeistungFormData = {
  id: string;
  kundeId: string;
  mitarbeiterId: string;
  kontingentId: string;
  kontingentId2: string;
  datum: string;
  startUhrzeit: string;
  endUhrzeit: string;
  art: 'remote' | 'vor-Ort' | 'vor-Ort (free)';
  stundenpreisInput: string;
  anfahrtskostenInput: string;
  beschreibung: string;
};

interface LeistungFormProps {
  initialData: LeistungFormData & { _id?: Id<'kunden_leistungen'> };
  isEditing: boolean;
  kunden: KundeDoc[];
  mitarbeiter: MitarbeiterDoc[];
  onSubmitSuccess: () => void;
  onCancel: () => void;
}

const createNewLeistungItem = (
  kunden: KundeDoc[],
  defaultKundeId = ''
): LeistungFormData => {
  const now = new Date();
  const startTime = new Date(now.getTime() - 60 * 60 * 1000);
  const selectedKunde = kunden.find((k) => k._id === defaultKundeId);
  return {
    id: crypto.randomUUID(),
    kundeId: defaultKundeId,
    mitarbeiterId: '',
    kontingentId: '',
    kontingentId2: '',
    datum: toInputDate(now.getTime()),
    startUhrzeit: toInputTime(startTime.getTime()),
    endUhrzeit: toInputTime(now.getTime()),
    art: 'remote',
    stundenpreisInput: selectedKunde
      ? selectedKunde.stundenpreis.toString()
      : '',
    anfahrtskostenInput: selectedKunde
      ? selectedKunde.anfahrtskosten.toString()
      : '',
    beschreibung: '',
  };
};

export function LeistungForm({
  initialData,
  isEditing,
  kunden,
  mitarbeiter,
  onSubmitSuccess,
  onCancel,
}: LeistungFormProps) {
  const createLeistung = useMutation(api.erstellung.leistung.create);
  const updateLeistung = useMutation(api.erstellung.leistung.update);
  const navigate = useNavigate();

  const [selectedKundeId, setSelectedKundeId] = useState<string>(() => {
    return isEditing ? initialData.kundeId : '';
  });

  const [formState, setFormState] = useState<LeistungFormData[]>(() => {
    if (isEditing) {
      return [
        {
          ...initialData,
          id: initialData._id || crypto.randomUUID(),
        },
      ];
    }
    // Create initial item with empty values to prevent dependency issues
    const now = new Date();
    const startTime = new Date(now.getTime() - 60 * 60 * 1000);
    return [{
      id: crypto.randomUUID(),
      kundeId: '',
      mitarbeiterId: '',
      kontingentId: '',
      kontingentId2: '',
      datum: toInputDate(now.getTime()),
      startUhrzeit: toInputTime(startTime.getTime()),
      endUhrzeit: toInputTime(now.getTime()),
      art: 'remote',
      stundenpreisInput: '',
      anfahrtskostenInput: '',
      beschreibung: '',
    }];
  });

  const handleKundeChange = useCallback((kundeId: string) => {
    setSelectedKundeId(kundeId);
    const selectedKunde = kunden.find((k) => k._id === kundeId);

    setFormState((prev) =>
      prev.map((item) => ({
        ...item,
        kundeId,
        kontingentId: '',
        kontingentId2: '',
        stundenpreisInput: selectedKunde
          ? selectedKunde.stundenpreis.toString()
          : '',
        anfahrtskostenInput: selectedKunde
          ? selectedKunde.anfahrtskosten.toString()
          : '',
      }))
    );
  }, [kunden]);

  const handleAddItem = useCallback(() => {
    setFormState((prev) => [
      ...prev,
      createNewLeistungItem(kunden, selectedKundeId),
    ]);
  }, [kunden, selectedKundeId]);

  const handleRemoveItem = useCallback((id: string) => {
    setFormState((prev) => {
      if (prev.length > 1) {
        return prev.filter((item) => item.id !== id);
      }
      return prev;
    });
  }, []);

  const handleFormChange = useCallback((
    id: string,
    field: keyof LeistungFormData,
    value: string
  ) => {
    setFormState((prev) =>
      prev.map((item) => (item.id === id ? { ...item, [field]: value } : item))
    );
  }, []);

  const totalSummary = useMemo(() => {
    return formState.reduce(
      (acc, item) => {
        const timestamps = getTimestamps(
          item.datum,
          item.startUhrzeit,
          item.endUhrzeit
        );
        if (!timestamps) {
          return acc;
        }
        const stunden = calculateHours(
          timestamps.startZeit,
          timestamps.endZeit
        );
        const preis = Number.parseFloat(item.stundenpreisInput) || 0;
        const anfahrt =
          item.art === 'vor-Ort'
            ? Number.parseFloat(item.anfahrtskostenInput) || 0
            : 0;
        return {
          stunden: acc.stunden + stunden,
          gesamt: acc.gesamt + stunden * preis + anfahrt,
        };
      },
      { stunden: 0, gesamt: 0 }
    );
  }, [formState]);

  const handleSubmit = async () => {
    if (!selectedKundeId) {
      toast.error('Bitte wählen Sie einen Kunden aus.');
      return;
    }

    const promises = formState.map(async (item, index) => {
      const timestamps = getTimestamps(
        item.datum,
        item.startUhrzeit,
        item.endUhrzeit
      );
      if (!timestamps) {
        throw new Error(`Ungültiges Datum/Uhrzeit in Position ${index + 1}`);
      }

      const { startZeit, endZeit } = timestamps;
      const {
        mitarbeiterId,
        kontingentId,
        kontingentId2,
        art,
        beschreibung,
        stundenpreisInput,
        anfahrtskostenInput,
      } = item;

      const preis = stundenpreisInput
        ? Number.parseFloat(stundenpreisInput)
        : undefined;
      const anfahrt = anfahrtskostenInput
        ? Number.parseFloat(anfahrtskostenInput)
        : undefined;

      if (!(mitarbeiterId && kontingentId && beschreibung)) {
        throw new Error(
          `Bitte alle Felder in Position ${index + 1} ausfüllen.`
        );
      }

      const mutationArgs = {
        kundenId: selectedKundeId as Id<'kunden'>,
        mitarbeiterId: mitarbeiterId as Id<'mitarbeiter'>,
        kontingentId: kontingentId as Id<'kunden_kontingente'>,
        kontingentId2: kontingentId2
          ? (kontingentId2 as Id<'kunden_kontingente'>)
          : undefined,
        startZeit,
        endZeit,
        beschreibung,
        art,
        stundenpreis: preis,
        anfahrtskosten: art === 'vor-Ort' ? anfahrt : 0,
      };

      if (isEditing) {
        return updateLeistung({ id: initialData._id!, ...mutationArgs });
      }
      return createLeistung(mutationArgs);
    });

    try {
      const results = await Promise.all(promises);

      // Erfolgs-Toast mit Lieferschein-Button nur beim Erstellen neuer Leistungen (nicht beim Bearbeiten)
      if (isEditing) {
        toast.success(
          `${results.length} Leistung(en) erfolgreich aktualisiert.`
        );
      } else {
        toast.success(`${results.length} Leistung(en) erfolgreich erstellt.`, {
          action: {
            label: 'Lieferschein erstellen',
            onClick: () => {
              // Öffne das NewLieferscheinDialog mit vorausgefüllten Daten
              navigate(
                `/erstellung/lieferscheine?kundenId=${selectedKundeId}&leistungIds=${results.join(',')}&openDialog=true`
              );
            },
          },
          duration: 8000, // 8 Sekunden anzeigen
        });
      }

      onSubmitSuccess();
    } catch (error: any) {
      toast.error(`Fehler beim Speichern: ${error.message}`);
    }
  };

  const customFooter = (
    <div className="flex w-full items-center justify-between">
      <div className="flex items-center gap-4 text-gray-300 text-sm">
        <span>
          Dauer:{' '}
          <span className="font-medium text-white">
            {formatHours(totalSummary.stunden)}
          </span>
        </span>
        <span>
          Gesamtkosten:{' '}
          <span className="font-medium text-white">
            {formatCurrency(totalSummary.gesamt)}
          </span>
        </span>
      </div>
      <div className="flex gap-2">
        <Button
          className="h-8 text-xs"
          onClick={onCancel}
          size="sm"
          variant="outline"
        >
          Abbrechen
        </Button>
        <Button className="h-8 gap-1 text-xs" onClick={handleSubmit} size="sm">
          <Check className="h-4 w-4" />
          {isEditing ? 'Änderungen speichern' : 'Leistungen speichern'}
        </Button>
      </div>
    </div>
  );

  return (
    <AppDialogLayout
      footer={customFooter}
      icon={isEditing ? <Pencil /> : <PlusCircle />}
      isOpen={true}
      maxWidth="xl"
      onClose={onCancel}
      title={isEditing ? 'Leistung bearbeiten' : 'Neue Leistungen erfassen'}
    >
      <div className="space-y-1">
        {/* Globale Kundenauswahl */}
        {!isEditing && (
          <div className="mb-2 flex items-center gap-2">
            <Label
              className="min-w-[50px] whitespace-nowrap font-medium text-gray-300 text-xs"
              htmlFor="globalKundeId"
            >
              Kunde:
            </Label>
            <Select onValueChange={handleKundeChange} value={selectedKundeId}>
              <SelectTrigger
                className="h-7 flex-1 border-gray-600 bg-gray-700 text-gray-200 text-sm"
                id="globalKundeId"
              >
                <SelectValue placeholder="Kunde auswählen..." />
              </SelectTrigger>
              <SelectContent>
                {kunden.map((k) => (
                  <SelectItem key={k._id} value={k._id}>
                    {k.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedKundeId && (
              <Button
                className="h-7 w-7 shrink-0 border-gray-600 bg-gray-800 text-gray-200 hover:bg-gray-700"
                onClick={handleAddItem}
                size="icon"
                title="Position hinzufügen"
                variant="outline"
              >
                <Plus className="h-3 w-3" />
              </Button>
            )}
          </div>
        )}

        <div className="max-h-[60vh] space-y-1 overflow-y-auto pr-1">
          {formState.map((item, index) => (
            <LeistungItemForm
              index={index}
              isEditing={isEditing}
              isOnlyItem={formState.length === 1}
              item={item}
              key={item.id}
              kunden={kunden}
              mitarbeiter={mitarbeiter}
              onFormChange={handleFormChange}
              onRemove={handleRemoveItem}
              selectedKundeId={selectedKundeId}
              showKundeSelection={isEditing}
            />
          ))}
        </div>
      </div>
    </AppDialogLayout>
  );
}

// Helper to get timestamps for a single item
const getTimestamps = (
  datum: string,
  startUhrzeit: string,
  endUhrzeit: string
) => {
  try {
    const [year, month, day] = datum.split('-').map(Number);
    const [startHour, startMinute] = startUhrzeit.split(':').map(Number);
    const [endHour, endMinute] = endUhrzeit.split(':').map(Number);
    const isDST = (m: number) => m >= 3 && m <= 10;
    const offsetHours = isDST(month) ? 2 : 1;
    const startDate = new Date(
      Date.UTC(year, month - 1, day, startHour - offsetHours, startMinute, 0)
    );
    let endDate = new Date(
      Date.UTC(year, month - 1, day, endHour - offsetHours, endMinute, 0)
    );
    if (endDate <= startDate) {
      endDate = new Date(
        Date.UTC(year, month - 1, day + 1, endHour - offsetHours, endMinute, 0)
      );
    }
    return { startZeit: startDate.getTime(), endZeit: endDate.getTime() };
  } catch (_e) {
    return null;
  }
};

interface LeistungItemFormProps {
  item: LeistungFormData;
  index: number;
  onFormChange: (
    id: string,
    field: keyof LeistungFormData,
    value: string
  ) => void;
  onRemove: (id: string) => void;
  kunden: KundeDoc[];
  mitarbeiter: MitarbeiterDoc[];
  isOnlyItem: boolean;
  isEditing: boolean;
  selectedKundeId: string;
  showKundeSelection: boolean;
}

function LeistungItemForm({
  item,
  index,
  onFormChange,
  onRemove,
  kunden,
  mitarbeiter,
  isOnlyItem,
  isEditing,
  selectedKundeId,
  showKundeSelection,
}: LeistungItemFormProps) {
  const [showSecondKontingent, setShowSecondKontingent] = useState(false);

  const aktiveKontingente = useQuery(
    api.verwaltung.kontingente.getActiveByKunde,
    selectedKundeId ? { kundenId: selectedKundeId as Id<'kunden'> } : 'skip'
  ) as KontingentOption[] | undefined;

  const handleChange = useCallback((field: keyof LeistungFormData, value: string) => {
    onFormChange(item.id, field, value);
  }, [item.id, onFormChange]);

  const summaryData = useMemo(() => {
    const timestamps = getTimestamps(
      item.datum,
      item.startUhrzeit,
      item.endUhrzeit
    );
    if (!timestamps) {
      return { stunden: 0, gesamt: 0 };
    }
    const stunden = calculateHours(timestamps.startZeit, timestamps.endZeit);
    const preis = Number.parseFloat(item.stundenpreisInput) || 0;
    const anfahrt =
      item.art === 'vor-Ort'
        ? Number.parseFloat(item.anfahrtskostenInput) || 0
        : 0;
    return { stunden, gesamt: stunden * preis + anfahrt };
  }, [
    item.datum,
    item.startUhrzeit,
    item.endUhrzeit,
    item.stundenpreisInput,
    item.anfahrtskostenInput,
    item.art,
  ]);

  const selectedKontingent1 = useMemo(() => {
    return aktiveKontingente?.find((k) => k._id === item.kontingentId);
  }, [aktiveKontingente, item.kontingentId]);

  const verfuegbareStunden1 = useMemo(() => {
    if (!selectedKontingent1) {
      return 0;
    }
    return selectedKontingent1.stunden - selectedKontingent1.verbrauchteStunden;
  }, [selectedKontingent1]);

  useEffect(() => {
    if (item.kontingentId && summaryData.stunden > verfuegbareStunden1) {
      setShowSecondKontingent(true);
    } else {
      setShowSecondKontingent(false);
      handleChange('kontingentId2', '');
    }
  }, [
    summaryData.stunden,
    verfuegbareStunden1,
    item.kontingentId,
    handleChange,
  ]);

  return (
    <div className="relative space-y-2 rounded-lg border border-gray-700 bg-gray-800/50 p-2">
      {/* Main Layout: Einstellungen links, Beschreibung rechts */}
      <div className="grid grid-cols-1 gap-3 lg:grid-cols-12">
        {/* Linke Seite: Einstellungen */}
        <div className="space-y-2 lg:col-span-7">
          {/* Row 1: Mitarbeiter, Art, Datum, Zeit */}
          <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-12">
            <div className="lg:col-span-4">
              <Label
                className="font-medium text-gray-400 text-xs"
                htmlFor={`mitarbeiterId-${item.id}`}
              >
                Mitarbeiter
              </Label>
              <Select
                onValueChange={(value) => handleChange('mitarbeiterId', value)}
                value={item.mitarbeiterId}
              >
                <SelectTrigger
                  className="h-7 border-gray-600 bg-gray-700 text-gray-200 text-xs"
                  id={`mitarbeiterId-${item.id}`}
                >
                  <SelectValue placeholder="Mitarbeiter..." />
                </SelectTrigger>
                <SelectContent>
                  {mitarbeiter.map((m) => (
                    <SelectItem key={m._id} value={m._id}>
                      {m.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="lg:col-span-4">
              <Label
                className="font-medium text-gray-400 text-xs"
                htmlFor={`art-${item.id}`}
              >
                Leistung
              </Label>
              <Select
                onValueChange={(value) => handleChange('art', value)}
                value={item.art}
              >
                <SelectTrigger
                  className="h-7 border-gray-600 bg-gray-700 text-gray-200 text-xs"
                  id={`art-${item.id}`}
                >
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="remote">Remote</SelectItem>
                  <SelectItem value="vor-Ort">Vor-Ort</SelectItem>
                  <SelectItem value="vor-Ort (free)">Vor-Ort (frei)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="lg:col-span-4">
              <Label
                className="font-medium text-gray-400 text-xs"
                htmlFor={`datum-${item.id}`}
              >
                Datum
              </Label>
              <Input
                className="h-7 border-gray-600 bg-gray-700 text-gray-200 text-xs"
                id={`datum-${item.id}`}
                onChange={(e) => handleChange('datum', e.target.value)}
                type="date"
                value={item.datum}
              />
            </div>
          </div>

          {/* Row 2: Zeit */}
          <div className="grid grid-cols-2 gap-2 lg:grid-cols-12">
            <div className="lg:col-span-6">
              <Label
                className="font-medium text-gray-400 text-xs"
                htmlFor={`startUhrzeit-${item.id}`}
              >
                Von
              </Label>
              <Input
                className="h-7 border-gray-600 bg-gray-700 text-gray-200 text-xs"
                id={`startUhrzeit-${item.id}`}
                onChange={(e) => handleChange('startUhrzeit', e.target.value)}
                step="900"
                type="time"
                value={item.startUhrzeit}
              />
            </div>

            <div className="col-span-6">
              <Label
                className="font-medium text-gray-400 text-xs"
                htmlFor={`endUhrzeit-${item.id}`}
              >
                Bis
              </Label>
              <Input
                className="h-7 border-gray-600 bg-gray-700 text-gray-200 text-xs"
                id={`endUhrzeit-${item.id}`}
                onChange={(e) => handleChange('endUhrzeit', e.target.value)}
                step="900"
                type="time"
                value={item.endUhrzeit}
              />
            </div>
          </div>

          {/* Row 3: Kontingente */}
          <div className="grid grid-cols-1 gap-2 lg:grid-cols-12">
            <div
              className={
                showSecondKontingent ? 'lg:col-span-6' : 'lg:col-span-12'
              }
            >
              <Label
                className="font-medium text-gray-400 text-xs"
                htmlFor={`kontingentId-${item.id}`}
              >
                Kontingent
              </Label>
              <Select
                disabled={!(selectedKundeId && aktiveKontingente)}
                onValueChange={(value) => handleChange('kontingentId', value)}
                value={item.kontingentId}
              >
                <SelectTrigger
                  className="h-7 border-gray-600 bg-gray-700 text-gray-200 text-xs"
                  id={`kontingentId-${item.id}`}
                >
                  <SelectValue placeholder="Kontingent auswählen..." />
                </SelectTrigger>
                <SelectContent>
                  {aktiveKontingente?.map((k) => (
                    <SelectItem key={k._id} value={k._id}>
                      {k.name} ({formatHours(k.stunden - k.verbrauchteStunden)}{' '}
                      verfügbar)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {showSecondKontingent && (
              <div className="lg:col-span-6">
                <Label
                  className="font-medium text-gray-400 text-xs"
                  htmlFor={`kontingentId2-${item.id}`}
                >
                  Zweites Kontingent
                </Label>
                <Select
                  disabled={!item.kontingentId}
                  onValueChange={(value) =>
                    handleChange('kontingentId2', value)
                  }
                  value={item.kontingentId2}
                >
                  <SelectTrigger
                    className="h-7 border-gray-600 bg-gray-700 text-gray-200 text-xs"
                    id={`kontingentId2-${item.id}`}
                  >
                    <SelectValue placeholder="Zweites Kontingent..." />
                  </SelectTrigger>
                  <SelectContent>
                    {aktiveKontingente
                      ?.filter((k) => k._id !== item.kontingentId)
                      .map((k) => (
                        <SelectItem key={k._id} value={k._id}>
                          {k.name} (
                          {formatHours(k.stunden - k.verbrauchteStunden)}{' '}
                          verfügbar)
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>

        {/* Rechte Seite: Beschreibung */}
        <div className="flex flex-col lg:col-span-5">
          <Label
            className="mb-2 font-medium text-gray-400 text-xs"
            htmlFor={`beschreibung-${item.id}`}
          >
            Beschreibung
          </Label>
          <Textarea
            className="flex-1 resize-none border-gray-600 bg-gray-700 text-gray-200 text-xs"
            id={`beschreibung-${item.id}`}
            onChange={(e) => handleChange('beschreibung', e.target.value)}
            placeholder="Beschreibung der erbrachten Leistung..."
            value={item.beschreibung}
          />
        </div>
      </div>

      {/* Validation & Warnings */}
      {item.beschreibung.length > 0 && item.beschreibung.length < 3 && (
        <div className="flex items-center gap-2 rounded border border-red-700/60 bg-red-900/50 p-2 text-red-300 text-xs">
          <AlertCircle className="h-4 w-4" />
          Die Beschreibung muss mindestens drei Zeichen enthalten.
        </div>
      )}

      {/* Footer: Summary & Actions */}
      <div className="mt-2 flex items-center justify-between border-gray-700 border-t pt-2 text-xs">
        <div className="flex items-center gap-2">
          {!(isOnlyItem || isEditing) && (
            <Button
              className="h-6 w-6 text-gray-500 hover:text-red-400"
              onClick={() => onRemove(item.id)}
              size="icon"
              variant="ghost"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
          <div className="flex items-center gap-4 text-gray-300">
            <span>
              Dauer:{' '}
              <span className="font-medium text-white">
                {formatHours(summaryData.stunden)}
              </span>
            </span>
            <span>
              Gesamtkosten:{' '}
              <span className="font-medium text-white">
                {formatCurrency(summaryData.gesamt)}
              </span>
            </span>
            {showSecondKontingent && (
              <div
                className="inline-flex h-4 w-4 cursor-help items-center justify-center rounded-full border border-yellow-700/60 bg-yellow-900/50 text-yellow-300"
                title="Leistung wird auf zwei Kontingente aufgeteilt"
              >
                <Split className="h-2.5 w-2.5" />
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-gray-400">Stundenpreis:</span>
          <Input
            className="h-6 w-20 border-gray-600 bg-gray-700 text-gray-200 text-xs"
            id={`stundenpreisInput-${item.id}`}
            onChange={(e) => handleChange('stundenpreisInput', e.target.value)}
            placeholder="€/Std"
            type="number"
            value={item.stundenpreisInput}
          />
          <span className="text-gray-400">Anfahrt:</span>
          <Input
            className={`h-6 w-20 text-xs ${
              item.art === 'remote'
                ? 'cursor-not-allowed border-gray-700 bg-gray-800 text-gray-500'
                : 'border-gray-600 bg-gray-700 text-gray-200'
            }`}
            disabled={item.art === 'remote'}
            id={`anfahrtskostenInput-${item.id}`}
            onChange={(e) =>
              handleChange('anfahrtskostenInput', e.target.value)
            }
            placeholder="€"
            type="number"
            value={item.art === 'remote' ? '0' : item.anfahrtskostenInput}
          />
        </div>
      </div>
    </div>
  );
}
