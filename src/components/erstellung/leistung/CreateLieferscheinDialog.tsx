import { useMutation } from 'convex/react';
import { FileText } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { AppDialogLayout } from '@/components/layout/AppDialogLayout';
import { formatDate, formatTime } from '@/lib/utils/formatUtils';

interface CreateLieferscheinDialogProps {
  isOpen: boolean;
  onClose: () => void;
  leistung: {
    _id: Id<'kunden_leistungen'>;
    kundenId: Id<'kunden'>;
    kundeName: string;
    startZeit: number;
    beschreibung: string;
  };
}

export function CreateLieferscheinDialog({
  isOpen,
  onClose,
  leistung,
}: CreateLieferscheinDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const createLieferschein = useMutation(api.erstellung.lieferschein.create);

  const handleSubmit = async () => {
    if (isSubmitting) {
      return;
    }

    try {
      setIsSubmitting(true);

      // Erstelle einen neuen Lieferschein mit dieser Leistung
      const lieferscheinId = await createLieferschein({
        kundenId: leistung.kundenId,
        leistungIds: [leistung._id],
        // Keine automatische Bemerkung mehr setzen
      });

      toast.success('Lieferschein-Entwurf erfolgreich erstellt.');
      onClose();

      // Zur Detailseite navigieren
      if (lieferscheinId) {
        window.location.href = `/erstellung/lieferscheine/${lieferscheinId}`;
      }
    } catch (_error) {
      toast.error('Fehler beim Erstellen des Lieferscheins.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AppDialogLayout
      description="Möchten Sie einen neuen Lieferschein für diese Leistung erstellen?"
      footerAction={{
        label: 'Lieferschein erstellen',
        onClick: handleSubmit,
        disabled: isSubmitting,
        loading: isSubmitting,
      }}
      icon={<FileText className="h-4 w-4 text-blue-500" />}
      isOpen={isOpen}
      maxWidth="sm"
      onClose={onClose}
      title="Lieferschein erstellen"
    >
      <div className="space-y-4">
        <div className="rounded-md bg-gray-800/50 p-3">
          <div className="mb-1 font-medium text-sm">Leistungsdetails:</div>
          <div className="grid grid-cols-[auto,1fr] gap-x-3 gap-y-1 text-sm">
            <span className="text-gray-400">Kunde:</span>
            <span>{leistung.kundeName}</span>

            <span className="text-gray-400">Datum:</span>
            <span>{formatDate(leistung.startZeit)}</span>

            <span className="text-gray-400">Zeit:</span>
            <span>{formatTime(leistung.startZeit)}</span>

            <span className="text-gray-400">Beschreibung:</span>
            <span className="truncate">{leistung.beschreibung}</span>
          </div>
        </div>

        <p className="text-gray-400 text-sm">
          Es wird ein neuer Lieferschein-Entwurf erstellt, der diese Leistung
          enthält. Sie können den Lieferschein anschließend bearbeiten und
          weitere Leistungen hinzufügen.
        </p>
      </div>
    </AppDialogLayout>
  );
}
