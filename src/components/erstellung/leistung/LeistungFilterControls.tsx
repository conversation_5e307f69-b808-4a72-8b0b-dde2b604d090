import type { Doc } from '@/../convex/_generated/dataModel';
import {
  type FilterConfig,
  type FilterOption,
  GenericFilterControls,
} from '@/components/layout/GenericFilterControls';

// Typen übernehmen oder neu definieren
interface LeistungFilter {
  kundeId: string;
  zeitraum: 'all' | 'last7' | 'last30' | 'month' | 'lastMonth' | 'custom';
  startDatum: string;
  endDatum: string;
}
type KundeDoc = Doc<'kunden'>;

interface LeistungFilterControlsProps {
  filter: LeistungFilter;
  searchTerm: string;
  kunden: KundeDoc[];
  onFilterChange: (field: keyof LeistungFilter, value: string) => void;
  onSearchTermChange: (value: string) => void;
  onResetAllFilters?: () => void; // Optional: Handler zum Zurücksetzen aller Filter
}

export function LeistungFilterControls({
  filter,
  searchTerm,
  kunden,
  onFilterChange,
  onSearchTermChange,
  onResetAllFilters,
}: LeistungFilterControlsProps) {
  const kundenOptions: FilterOption[] = [
    { value: '__ALL__', label: 'Alle Kunden' },
    ...kunden.map((kunde) => ({
      value: kunde._id,
      label: kunde.name,
    })),
  ];

  const zeitraumOptions: FilterOption[] = [
    { value: 'all', label: 'Gesamter Zeitraum' },
    { value: 'last7', label: 'Letzte 7 Tage' },
    { value: 'last30', label: 'Letzte 30 Tage' },
    { value: 'month', label: 'Dieser Monat' },
    { value: 'lastMonth', label: 'Letzter Monat' },
    { value: 'custom', label: 'Benutzerdefiniert' },
  ];

  const filtersConfig: FilterConfig[] = [
    {
      type: 'select',
      id: 'kundeFilterLeistung',
      value: filter.kundeId || '__ALL__',
      onChange: (value) =>
        onFilterChange('kundeId', value === '__ALL__' ? '' : value),
      options: kundenOptions,
      placeholder: 'Alle Kunden',
      triggerClassName: 'h-8 text-xs bg-gray-800 border-gray-700',
      minWidth: '150px',
    },
    {
      type: 'dateRange',
      idPrefix: 'leistungZeitraum',
      zeitraumValue: filter.zeitraum,
      startDateValue: filter.startDatum,
      endDateValue: filter.endDatum,
      onZeitraumChange: (value) => onFilterChange('zeitraum', value),
      onStartDateChange: (value) => onFilterChange('startDatum', value),
      onEndDateChange: (value) => onFilterChange('endDatum', value),
      zeitraumOptions,
      selectTriggerClassName: 'h-8 text-xs bg-gray-800 border-gray-700',
      customDateInputClassName:
        'h-8 text-xs w-auto bg-gray-800 border-gray-700',
      minWidth: '130px',
    },
    {
      type: 'search',
      id: 'leistungSearch',
      value: searchTerm,
      onChange: onSearchTermChange,
      placeholder: 'Suchen...',
      className: 'flex-grow',
      inputClassName: 'h-8 text-xs pl-8 w-full bg-gray-800 border-gray-700',
    },
  ];

  return (
    <GenericFilterControls
      filters={filtersConfig}
      onResetAll={onResetAllFilters}
    />
  );
}
