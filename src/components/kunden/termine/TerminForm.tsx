import { useEffect, useState } from 'react';
import type { Doc } from '@/../convex/_generated/dataModel';
import { AppDialogLayout } from '@/components/layout/AppDialogLayout';
import { Input } from '@/components/_shared/Input';
import { Label } from '@/components/_shared/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/_shared/select';
import { Textarea } from '@/components/_shared/Textarea';
import { Calendar, Check } from 'lucide-react';

interface TerminFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  initialData?: Doc<'kunden_termine'> | null;
  title: string;
}

interface TerminFormData {
  titel: string;
  kategorie: string;
  datum: string;
  uhrzeit: string;
  notizen: string;
  istWiederholend: boolean;
  wiederholungsIntervall:
    | 'taeglich'
    | 'woechentlich'
    | 'monatlich'
    | 'jaehrlich'
    | '';
  wochentag:
    | 'montag'
    | 'dienstag'
    | 'mittwoch'
    | 'donnerstag'
    | 'freitag'
    | 'samstag'
    | 'sonntag'
    | '';
  monatlicheWiederholung:
    | 'erster_montag'
    | 'erster_dienstag'
    | 'erster_mittwoch'
    | 'erster_donnerstag'
    | 'erster_freitag'
    | 'letzter_montag'
    | 'letzter_dienstag'
    | 'letzter_mittwoch'
    | 'letzter_donnerstag'
    | 'letzter_freitag'
    | 'tag_1'
    | 'tag_15'
    | 'letzter_tag'
    | '';
  wiederholungsEnde: string;
}

const defaultFormData: TerminFormData = {
  titel: '',
  kategorie: '',
  datum: '',
  uhrzeit: '',
  notizen: '',
  istWiederholend: false,
  wiederholungsIntervall: '',
  wochentag: '',
  monatlicheWiederholung: '',
  wiederholungsEnde: '',
};

export function TerminForm({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  title,
}: TerminFormProps) {
  const [formData, setFormData] = useState<TerminFormData>(defaultFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (initialData) {
      setFormData({
        titel: initialData.titel,
        kategorie: initialData.kategorie,
        datum: initialData.datum || '',
        uhrzeit: initialData.uhrzeit || '',
        notizen: initialData.notizen || '',
        istWiederholend: initialData.istWiederholend,
        wiederholungsIntervall: initialData.wiederholungsIntervall || '',
        wochentag: initialData.wochentag || '',
        monatlicheWiederholung: initialData.monatlicheWiederholung || '',
        wiederholungsEnde: initialData.wiederholungsEnde || '',
      });
    } else {
      setFormData(defaultFormData);
    }
  }, [initialData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const submitData: any = {
        titel: formData.titel,
        kategorie: formData.kategorie,
        istWiederholend: formData.istWiederholend,
      };

      // Für einmalige Termine: Datum ist Pflicht
      if (!formData.istWiederholend) {
        submitData.datum = formData.datum;
      }

      // Optional fields
      if (formData.uhrzeit.trim()) {
        submitData.uhrzeit = formData.uhrzeit;
      }
      if (formData.notizen.trim()) {
        submitData.notizen = formData.notizen;
      }

      // Wiederholungsfelder
      if (formData.istWiederholend && formData.wiederholungsIntervall) {
        submitData.wiederholungsIntervall = formData.wiederholungsIntervall;

        if (
          formData.wiederholungsIntervall === 'woechentlich' &&
          formData.wochentag
        ) {
          submitData.wochentag = formData.wochentag;
        }

        if (
          formData.wiederholungsIntervall === 'monatlich' &&
          formData.monatlicheWiederholung
        ) {
          submitData.monatlicheWiederholung = formData.monatlicheWiederholung;
        }

        if (formData.wiederholungsEnde.trim()) {
          submitData.wiederholungsEnde = formData.wiederholungsEnde;
        }
      }

      await onSubmit(submitData);
    } catch (_error) {
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof TerminFormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: field === 'istWiederholend' ? value === 'true' : value,
    }));
  };

  return (
    <AppDialogLayout
      footerAction={{
        label: isSubmitting ? 'Speichern...' : 'Speichern',
        onClick: () => {
          const form = document.getElementById('termin-form') as HTMLFormElement;
          if (form) {
            form.requestSubmit();
          }
        },
        icon: <Check className="h-4 w-4" />,
        disabled: isSubmitting,
        loading: isSubmitting,
      }}
      icon={<Calendar className="h-3.5 w-3.5" />}
      isOpen={isOpen}
      maxWidth="lg"
      onClose={onClose}
      title={title}
      description="Termin-Details eingeben und speichern."
    >
      <form id="termin-form" className="space-y-4" onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* Titel */}
          <div className="md:col-span-2">
            <Label className="font-medium text-sm" htmlFor="titel">
              Titel *
            </Label>
            <Input
              className="mt-1"
              id="titel"
              onChange={(e) => handleInputChange('titel', e.target.value)}
              placeholder="Termin-Titel eingeben"
              required
              value={formData.titel}
            />
          </div>

          {/* Kategorie */}
          <div className="md:col-span-2">
            <Label className="font-medium text-sm" htmlFor="kategorie">
              Kategorie *
            </Label>
            <Input
              className="mt-1"
              id="kategorie"
              onChange={(e) => handleInputChange('kategorie', e.target.value)}
              placeholder="z.B. Hardware, Software, Serverwartung"
              required
              value={formData.kategorie}
            />
          </div>

            {/* Wiederholung */}
            <div className="md:col-span-2">
              <div className="flex items-center space-x-2">
                <input
                  checked={formData.istWiederholend}
                  className="rounded border-gray-300"
                  id="istWiederholend"
                  onChange={(e) =>
                    handleInputChange(
                      'istWiederholend',
                      e.target.checked.toString()
                    )
                  }
                  type="checkbox"
                />
                <Label
                  className="font-medium text-sm"
                  htmlFor="istWiederholend"
                >
                  Wiederholender Termin
                </Label>
              </div>
            </div>

            {/* Datum (nur für einmalige Termine) */}
            {!formData.istWiederholend && (
              <div>
                <Label className="font-medium text-sm" htmlFor="datum">
                  Datum *
                </Label>
                <Input
                  className="mt-1"
                  id="datum"
                  onChange={(e) => handleInputChange('datum', e.target.value)}
                  required
                  type="date"
                  value={formData.datum}
                />
              </div>
            )}

            {/* Uhrzeit */}
            <div>
              <Label className="font-medium text-sm" htmlFor="uhrzeit">
                Uhrzeit
              </Label>
              <Input
                className="mt-1"
                id="uhrzeit"
                onChange={(e) => handleInputChange('uhrzeit', e.target.value)}
                type="time"
                value={formData.uhrzeit}
              />
            </div>

            {/* Wiederholungsintervall */}
            {formData.istWiederholend && (
              <div>
                <Label
                  className="font-medium text-sm"
                  htmlFor="wiederholungsIntervall"
                >
                  Wiederholung *
                </Label>
                <Select
                  onValueChange={(value) =>
                    handleInputChange('wiederholungsIntervall', value)
                  }
                  value={formData.wiederholungsIntervall}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Intervall wählen" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="taeglich">Täglich</SelectItem>
                    <SelectItem value="woechentlich">Wöchentlich</SelectItem>
                    <SelectItem value="monatlich">Monatlich</SelectItem>
                    <SelectItem value="jaehrlich">Jährlich</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Wochentag (für wöchentliche Wiederholung) */}
            {formData.istWiederholend &&
              formData.wiederholungsIntervall === 'woechentlich' && (
                <div>
                  <Label className="font-medium text-sm" htmlFor="wochentag">
                    Wochentag *
                  </Label>
                  <Select
                    onValueChange={(value) =>
                      handleInputChange('wochentag', value)
                    }
                    value={formData.wochentag}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Wochentag wählen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="montag">Montag</SelectItem>
                      <SelectItem value="dienstag">Dienstag</SelectItem>
                      <SelectItem value="mittwoch">Mittwoch</SelectItem>
                      <SelectItem value="donnerstag">Donnerstag</SelectItem>
                      <SelectItem value="freitag">Freitag</SelectItem>
                      <SelectItem value="samstag">Samstag</SelectItem>
                      <SelectItem value="sonntag">Sonntag</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

            {/* Monatliche Wiederholung */}
            {formData.istWiederholend &&
              formData.wiederholungsIntervall === 'monatlich' && (
                <div>
                  <Label
                    className="font-medium text-sm"
                    htmlFor="monatlicheWiederholung"
                  >
                    Monatliche Wiederholung *
                  </Label>
                  <Select
                    onValueChange={(value) =>
                      handleInputChange('monatlicheWiederholung', value)
                    }
                    value={formData.monatlicheWiederholung}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Wiederholung wählen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tag_1">1. des Monats</SelectItem>
                      <SelectItem value="tag_15">15. des Monats</SelectItem>
                      <SelectItem value="letzter_tag">
                        Letzter Tag des Monats
                      </SelectItem>
                      <SelectItem value="erster_montag">
                        Erster Montag
                      </SelectItem>
                      <SelectItem value="erster_dienstag">
                        Erster Dienstag
                      </SelectItem>
                      <SelectItem value="erster_mittwoch">
                        Erster Mittwoch
                      </SelectItem>
                      <SelectItem value="erster_donnerstag">
                        Erster Donnerstag
                      </SelectItem>
                      <SelectItem value="erster_freitag">
                        Erster Freitag
                      </SelectItem>
                      <SelectItem value="letzter_montag">
                        Letzter Montag
                      </SelectItem>
                      <SelectItem value="letzter_dienstag">
                        Letzter Dienstag
                      </SelectItem>
                      <SelectItem value="letzter_mittwoch">
                        Letzter Mittwoch
                      </SelectItem>
                      <SelectItem value="letzter_donnerstag">
                        Letzter Donnerstag
                      </SelectItem>
                      <SelectItem value="letzter_freitag">
                        Letzter Freitag
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

            {/* Wiederholungsende */}
            {formData.istWiederholend && (
              <div>
                <Label
                  className="font-medium text-sm"
                  htmlFor="wiederholungsEnde"
                >
                  Wiederholung bis
                </Label>
                <Input
                  className="mt-1"
                  id="wiederholungsEnde"
                  onChange={(e) =>
                    handleInputChange('wiederholungsEnde', e.target.value)
                  }
                  type="date"
                  value={formData.wiederholungsEnde}
                />
              </div>
            )}

            {/* Notizen */}
            <div className="md:col-span-2">
              <Label className="font-medium text-sm" htmlFor="notizen">
                Notizen
              </Label>
              <Textarea
                className="mt-1"
                id="notizen"
                onChange={(e) => handleInputChange('notizen', e.target.value)}
                placeholder="Zusätzliche Notizen"
                rows={3}
                value={formData.notizen}
              />
            </div>
          </div>

        </form>
    </AppDialogLayout>
  );
}
