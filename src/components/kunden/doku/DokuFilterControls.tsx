import {
  type FilterConfig,
  GenericFilterControls,
} from '@/components/layout/GenericFilterControls';

interface DokuFilterControlsProps {
  searchTerm: string;
  onSearchTermChange: (value: string) => void;
  onResetAllFilters?: () => void;
}

export function DokuFilterControls({
  searchTerm,
  onSearchTermChange,
  onResetAllFilters,
}: DokuFilterControlsProps) {
  const filtersConfig: FilterConfig[] = [
    {
      type: 'search',
      id: 'dokuSearch',
      value: searchTerm,
      onChange: onSearchTermChange,
      placeholder: 'Kunden suchen...',
      className: 'flex-grow',
      inputClassName: 'h-8 text-xs pl-8 w-full bg-gray-800 border-gray-700',
    },
  ];

  return (
    <GenericFilterControls
      filters={filtersConfig}
      onResetAll={onResetAllFilters}
      // The original component had "flex items-center gap-2"
      // GenericFilterControls defaults to "flex flex-wrap items-center gap-2"
      // If the exact same layout is needed, we can override:
      // className="flex items-center gap-2"
    />
  );
}
