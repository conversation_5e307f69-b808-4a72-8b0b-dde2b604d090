import { Check, Co<PERSON>, MapPin } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import type { Doc } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';

interface StandorteTableProps {
  kunde: Doc<'kunden'>;
}

export function StandorteTable({ kunde }: StandorteTableProps) {
  const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});

  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        setCopiedStates((prev) => ({ ...prev, [id]: true }));
        toast.success('In Zwischenablage kopiert!');
        setTimeout(
          () => setCopiedStates((prev) => ({ ...prev, [id]: false })),
          1500
        );
      },
      (_err) => {
        toast.error('Fehler beim Kopieren.');
      }
    );
  };

  return (
    <Card className="overflow-hidden border-0 shadow-lg">
      <CardHeader className="border-gray-700/50 border-b px-4 pb-2.5">
        <CardTitle className="font-medium text-base">
          Standorte (aus Stammdaten)
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        {kunde.standorte && kunde.standorte.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
                <TableHead className="w-12 px-2.5 py-1.5 text-center text-xs">
                  Haupt
                </TableHead>
                <TableHead className="px-2.5 py-1.5 text-xs">Straße</TableHead>
                <TableHead className="px-2.5 py-1.5 text-xs">PLZ</TableHead>
                <TableHead className="px-2.5 py-1.5 text-xs">Ort</TableHead>
                <TableHead className="px-2.5 py-1.5 text-xs">Land</TableHead>
                <TableHead className="w-20 px-2.5 py-1.5 text-center text-xs">
                  Aktionen
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {kunde.standorte.map((standort, index) => {
                const addressString = `${standort.strasse}, ${standort.plz} ${standort.ort}${standort.land ? `, ${standort.land}` : ''}`;
                const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(addressString)}`;
                const copyId = `standort-${index}`;
                return (
                  <TableRow key={index}>
                    <TableCell className="px-2.5 py-1 text-center text-xs">
                      {standort.istHauptstandort && (
                        <Check className="mx-auto h-4 w-4 text-green-400" />
                      )}
                    </TableCell>
                    <TableCell className="px-2.5 py-1 text-xs">
                      {standort.strasse}
                    </TableCell>
                    <TableCell className="px-2.5 py-1 text-xs">
                      {standort.plz}
                    </TableCell>
                    <TableCell className="px-2.5 py-1 text-xs">
                      {standort.ort}
                    </TableCell>
                    <TableCell className="px-2.5 py-1 text-xs">
                      {standort.land || '-'}
                    </TableCell>
                    <TableCell className="px-2.5 py-1 text-center text-xs">
                      <div className="flex justify-center gap-1">
                        <Button
                          className={`h-6 w-6 ${copiedStates[copyId] ? 'text-green-400' : 'text-blue-400 hover:text-blue-500'} hover:bg-blue-500/10`}
                          onClick={() => copyToClipboard(addressString, copyId)}
                          size="icon"
                          title="Adresse kopieren"
                          variant="ghost"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        <Button
                          className="h-6 w-6 text-purple-400 hover:bg-purple-500/10 hover:text-purple-500"
                          onClick={() => window.open(mapsUrl, '_blank')}
                          size="icon"
                          title="In Karte öffnen"
                          variant="ghost"
                        >
                          <MapPin className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        ) : (
          <p className="p-4 text-gray-400">
            Keine Standorte in den Stammdaten hinterlegt.
          </p>
        )}
      </CardContent>
    </Card>
  );
}
