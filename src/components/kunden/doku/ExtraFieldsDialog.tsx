import { <PERSON><PERSON>, <PERSON>, <PERSON>Off, FileText } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { FIELD_TYPES } from '@/../convex/schema';
import { Button } from '@/components/_shared/Button';
import { Checkbox } from '@/components/_shared/Checkbox';
import { Input } from '@/components/_shared/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/_shared/select';
import { Textarea } from '@/components/_shared/Textarea';
import { AppDialogLayout } from '@/components/layout/AppDialogLayout';

interface ExtraFieldsDialogField {
  label: string;
  value: string | number | null | undefined;
  fieldType?: string;
  isRequired?: boolean;
  isHidden?: boolean;
  isCopyable?: boolean;
  options?: string[];
}

interface ExtraFieldsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  fields: ExtraFieldsDialogField[];
}

export function ExtraFieldsDialog({
  isOpen,
  onClose,
  title,
  fields,
}: ExtraFieldsDialogProps) {
  const [fieldVisibility, setFieldVisibility] = useState<
    Record<string, boolean>
  >({});

  // Toggle field visibility for password fields
  const toggleFieldVisibility = (fieldName: string) => {
    setFieldVisibility((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }));
  };

  // Render field display based on type
  const renderFieldDisplay = (field: ExtraFieldsDialogField, index: number) => {
    const value =
      field.value !== undefined && field.value !== null
        ? String(field.value)
        : '';
    const fieldType = field.fieldType || FIELD_TYPES.TEXT;
    const fieldKey = `${field.label}-${index}`;

    switch (fieldType) {
      case FIELD_TYPES.TEXTAREA:
        return (
          <Textarea
            className="min-h-[80px] cursor-default resize-none border-gray-700 bg-gray-800/50 text-sm"
            readOnly
            value={value}
          />
        );
      case FIELD_TYPES.PASSWORD:
        return (
          <div className="relative">
            <Input
              className={`${field.isCopyable ? 'pr-16' : 'pr-9'} h-9 cursor-default border-gray-700 bg-gray-800/50 text-sm`}
              readOnly
              type={fieldVisibility[fieldKey] ? 'text' : 'password'}
              value={value}
            />
            <div className="absolute top-0.5 right-0.5 flex gap-0.5">
              {field.isCopyable && value && (
                <Button
                  className="h-7 w-7 text-gray-400 hover:bg-gray-700/70 hover:text-white"
                  onClick={() => {
                    navigator.clipboard.writeText(value);
                    toast.success('Wert in die Zwischenablage kopiert');
                  }}
                  size="icon"
                  title="Wert kopieren"
                  type="button"
                  variant="ghost"
                >
                  <Copy className="h-3.5 w-3.5" />
                </Button>
              )}
              <Button
                className="h-7 w-7 text-gray-400 hover:bg-gray-700/70 hover:text-white"
                onClick={() => toggleFieldVisibility(fieldKey)}
                size="icon"
                title={
                  fieldVisibility[fieldKey]
                    ? 'Passwort verbergen'
                    : 'Passwort anzeigen'
                }
                type="button"
                variant="ghost"
              >
                {fieldVisibility[fieldKey] ? (
                  <EyeOff className="h-3.5 w-3.5" />
                ) : (
                  <Eye className="h-3.5 w-3.5" />
                )}
              </Button>
            </div>
          </div>
        );
      case FIELD_TYPES.CHECKBOX:
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={value === 'true' || value === '1'}
              className="cursor-default"
              disabled
            />
            <span className="text-gray-300 text-sm">
              {value === 'true' || value === '1' ? 'Ja' : 'Nein'}
            </span>
          </div>
        );
      case FIELD_TYPES.SELECT:
        return (
          <Select disabled value={value}>
            <SelectTrigger className="h-9 cursor-default border-gray-700 bg-gray-800/50 text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      case FIELD_TYPES.NUMBER:
        return (
          <div className="relative">
            <Input
              className={`${field.isCopyable ? 'pr-9' : ''} h-9 cursor-default border-gray-700 bg-gray-800/50 text-sm`}
              readOnly
              type="number"
              value={value}
            />
            {field.isCopyable && value && (
              <Button
                className="absolute top-0.5 right-0.5 h-7 w-7 text-gray-400 hover:bg-gray-700/70 hover:text-white"
                onClick={() => {
                  navigator.clipboard.writeText(value);
                  toast.success('Wert in die Zwischenablage kopiert');
                }}
                size="icon"
                title="Wert kopieren"
                type="button"
                variant="ghost"
              >
                <Copy className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>
        );
      case FIELD_TYPES.EMAIL:
        return (
          <div className="relative">
            <Input
              className={`${field.isCopyable ? 'pr-9' : ''} h-9 cursor-default border-gray-700 bg-gray-800/50 text-sm`}
              readOnly
              type="email"
              value={value}
            />
            {field.isCopyable && value && (
              <Button
                className="absolute top-0.5 right-0.5 h-7 w-7 text-gray-400 hover:bg-gray-700/70 hover:text-white"
                onClick={() => {
                  navigator.clipboard.writeText(value);
                  toast.success('Wert in die Zwischenablage kopiert');
                }}
                size="icon"
                title="Wert kopieren"
                type="button"
                variant="ghost"
              >
                <Copy className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>
        );
      case FIELD_TYPES.URL:
        return (
          <div className="relative">
            <Input
              className={`${field.isCopyable ? 'pr-9' : ''} h-9 cursor-default border-gray-700 bg-gray-800/50 text-sm`}
              readOnly
              type="url"
              value={value}
            />
            {field.isCopyable && value && (
              <Button
                className="absolute top-0.5 right-0.5 h-7 w-7 text-gray-400 hover:bg-gray-700/70 hover:text-white"
                onClick={() => {
                  navigator.clipboard.writeText(value);
                  toast.success('Wert in die Zwischenablage kopiert');
                }}
                size="icon"
                title="Wert kopieren"
                type="button"
                variant="ghost"
              >
                <Copy className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>
        );
      default:
        return (
          <div className="relative">
            <Input
              className={`${field.isCopyable ? 'pr-9' : ''} h-9 cursor-default border-gray-700 bg-gray-800/50 text-sm`}
              readOnly
              type={fieldType === FIELD_TYPES.NUMBER ? 'number' : 'text'}
              value={value}
            />
            {field.isCopyable && value && (
              <Button
                className="absolute top-0.5 right-0.5 h-7 w-7 text-gray-400 hover:bg-gray-700/70 hover:text-white"
                onClick={() => {
                  navigator.clipboard.writeText(value);
                  toast.success('Wert in die Zwischenablage kopiert');
                }}
                size="icon"
                title="Wert kopieren"
                type="button"
                variant="ghost"
              >
                <Copy className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>
        );
    }
  };

  return (
    <AppDialogLayout
      icon={<FileText className="h-3.5 w-3.5" />}
      isOpen={isOpen}
      maxWidth="lg"
      onClose={onClose}
      title={title}
    >
      <div className="space-y-3">
        {fields.map((field, index) => (
          <div className="flex flex-col gap-1.5" key={index}>
            <div className="font-medium text-gray-400 text-xs">
              {field.label}
              {field.isRequired && <span className="ml-1 text-red-500">*</span>}
              <span className="ml-1 text-purple-400">*</span>
            </div>
            {renderFieldDisplay(field, index)}
          </div>
        ))}
      </div>
    </AppDialogLayout>
  );
}
