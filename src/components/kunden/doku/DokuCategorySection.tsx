import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Off,
  Notebook<PERSON><PERSON>t,
  <PERSON>cil,
  PlusCircle,
  Trash2,
  X,
} from 'lucide-react';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';
import type { Doc, Id } from '@/../convex/_generated/dataModel';
import { FIELD_TYPES } from '@/../convex/schema';
import { Button } from '@/components/_shared/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import { ExtraFieldsDialog } from './ExtraFieldsDialog';

// Define DokuEintragMitInfo to match the output of getByKunde which includes categoryName and kundeName
interface DokuEintragMitInfo {
  _id: Id<'kunden_dokumentation'>;
  _creationTime: number;
  kundenId: Id<'kunden'>;
  kategorieID: number; // Numeric ID of the category
  feldwerte: { feldId: number; feldWert: string; feldName?: string }[];
  kategorieName: string; // Added from getByKunde
}

interface DokuCategorySectionProps {
  kategorie: Doc<'system_doku_kategorien'>;
  alleEintraegeFuerKunde: DokuEintragMitInfo[];
  showFormForKategorie: Id<'system_doku_kategorien'> | null;
  onAddEntry: (kategorieId: Id<'system_doku_kategorien'>) => void;
  onEditEntry: (
    kategorieId: Id<'system_doku_kategorien'>,
    eintragId: Id<'kunden_dokumentation'>
  ) => void;
  onCancelForm: () => void;
  onDeleteEntry: (id: Id<'kunden_dokumentation'>) => Promise<void>;
  children?: React.ReactNode; // For DokuEntryForm
}

export function DokuCategorySection({
  kategorie,
  alleEintraegeFuerKunde,
  showFormForKategorie,
  onAddEntry,
  onEditEntry,
  onCancelForm,
  onDeleteEntry,
  children,
}: DokuCategorySectionProps) {
  const [hiddenFieldsVisibility, setHiddenFieldsVisibility] = useState<
    Record<string, boolean>
  >({});
  const [passwordCopiedStates, setPasswordCopiedStates] = useState<
    Record<string, boolean>
  >({});
  const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});
  const [extraFieldsDialogOpen, setExtraFieldsDialogOpen] = useState(false);
  const [currentExtraFieldsEntry, setCurrentExtraFieldsEntry] =
    useState<DokuEintragMitInfo | null>(null);

  // Filter entries for the current category from all entries fetched for the customer
  const entriesForCurrentCategory = useMemo(
    () =>
      alleEintraegeFuerKunde.filter(
        (e: DokuEintragMitInfo) => e.kategorieID === kategorie.kategorieID
      ),
    [alleEintraegeFuerKunde, kategorie.kategorieID]
  );

  const copyToClipboard = (text: string, id: string, isPassword = false) => {
    navigator.clipboard.writeText(text).then(
      () => {
        if (isPassword) {
          setPasswordCopiedStates((prev) => ({ ...prev, [id]: true }));
          toast.success('Passwort kopiert!');
          setTimeout(
            () => setPasswordCopiedStates((prev) => ({ ...prev, [id]: false })),
            1500
          );
        } else {
          setCopiedStates((prev) => ({ ...prev, [id]: true }));
          toast.success('In Zwischenablage kopiert!');
          setTimeout(
            () => setCopiedStates((prev) => ({ ...prev, [id]: false })),
            1500
          );
        }
      },
      (_err) => {
        toast.error('Fehler beim Kopieren.');
      }
    );
  };

  const toggleHiddenFieldsVisibility = (kategorieId: string) => {
    setHiddenFieldsVisibility((prev) => ({
      ...prev,
      [kategorieId]: !prev[kategorieId],
    }));
  };

  return (
    <Card className="overflow-hidden border-0 shadow-lg" key={kategorie._id}>
      <CardHeader className="flex flex-row items-center justify-between border-gray-700/50 border-b px-4 pb-2.5">
        <CardTitle className="font-medium text-base">
          {kategorie.name}
        </CardTitle>
        <div className="flex items-center gap-1">
          {/* Eye button only if category has fields with istVersteckt=true AND form is NOT shown */}
          {!showFormForKategorie &&
            kategorie.felder.some((f) => f.istVersteckt) && (
              <Button
                className="h-6 w-6 text-gray-400 hover:bg-gray-700/70 hover:text-white"
                onClick={() => toggleHiddenFieldsVisibility(kategorie._id)}
                size="icon"
                title={
                  hiddenFieldsVisibility[kategorie._id]
                    ? 'Versteckte Felder ausblenden'
                    : 'Versteckte Felder anzeigen'
                }
                variant="ghost"
              >
                {hiddenFieldsVisibility[kategorie._id] ? (
                  <EyeOff className="h-3 w-3" />
                ) : (
                  <Eye className="h-3 w-3" />
                )}
              </Button>
            )}
          {/* Action Buttons (Add/Close) */}
          {showFormForKategorie === kategorie._id ? (
            // Close button
            <Button
              className="h-6 w-6 text-gray-400 hover:bg-gray-700/70 hover:text-white"
              onClick={onCancelForm}
              size="icon"
              title="Formular schließen"
              variant="ghost"
            >
              <X className="h-3 w-3" />
            </Button>
          ) : (
            // Add button
            <Button
              className="h-6 w-6 text-gray-400 hover:bg-gray-700/70 hover:text-white"
              onClick={() => onAddEntry(kategorie._id)}
              size="icon"
              title={`Eintrag zu "${kategorie.name}" hinzufügen`}
              variant="ghost"
            >
              <PlusCircle className="h-3 w-3" />
            </Button>
          )}
        </div>
      </CardHeader>
      {/* Only render CardContent if there are entries OR if the form is shown for this category */}
      {(entriesForCurrentCategory.length > 0 ||
        showFormForKategorie === kategorie._id) && (
        <CardContent className="p-0">
          {showFormForKategorie === kategorie._id ? (
            <div className="p-4">{children}</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
                  {kategorie.felder
                    .filter((feld) => !feld.istExtrafeld)
                    .map((feld) => (
                      <TableHead
                        className="px-2.5 py-1.5 text-xs"
                        key={feld.feldId}
                      >
                        {feld.name}
                        {feld.istErforderlich && (
                          <span className="ml-0.5 text-red-400">*</span>
                        )}
                      </TableHead>
                    ))}
                  <TableHead className="w-20 px-2.5 py-1.5 text-center text-xs">
                    Aktionen
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {entriesForCurrentCategory.map((eintrag) => (
                  <TableRow key={eintrag._id}>
                    {kategorie.felder
                      .filter((feld) => !feld.istExtrafeld)
                      .map((feld) => {
                        const feldWertObj = eintrag.feldwerte.find(
                          (fw) => fw.feldId === feld.feldId
                        );
                        const displayValue = feldWertObj?.feldWert || '-';
                        const fieldType = feld.typ as keyof typeof FIELD_TYPES;
                        const isHiddenVisible =
                          hiddenFieldsVisibility[kategorie._id];
                        const pwdCopyId = `pwd-${eintrag._id}-${feld.feldId}`;

                        return (
                          <TableCell
                            className="px-2.5 py-1 align-top text-xs"
                            key={feld.feldId}
                          >
                            {fieldType === FIELD_TYPES.PASSWORD ||
                            feld.istVersteckt ? (
                              <div className="flex items-center gap-1">
                                <span className="text-gray-500 italic">
                                  {isHiddenVisible ? displayValue : '********'}
                                </span>
                                <Button
                                  className={`h-6 w-6 ${passwordCopiedStates[pwdCopyId] ? 'text-green-400' : 'text-blue-400 hover:text-blue-500'} shrink-0 hover:bg-blue-500/10`}
                                  onClick={() =>
                                    copyToClipboard(
                                      displayValue,
                                      pwdCopyId,
                                      true
                                    )
                                  }
                                  size="icon"
                                  title="Wert kopieren"
                                  variant="ghost"
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                            ) : fieldType === FIELD_TYPES.URL ? (
                              <div className="flex items-center gap-1">
                                <a
                                  className="text-cyan-400 hover:text-cyan-300"
                                  href={
                                    displayValue.startsWith('http')
                                      ? displayValue
                                      : `https://${displayValue}`
                                  }
                                  rel="noopener noreferrer"
                                  target="_blank"
                                >
                                  {displayValue}
                                </a>
                                {feld.istKopierbar && displayValue !== '-' && (
                                  <Button
                                    className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? 'text-green-400' : 'text-blue-400 hover:text-blue-500'} shrink-0 hover:bg-blue-500/10`}
                                    onClick={() =>
                                      copyToClipboard(
                                        displayValue,
                                        `field-${eintrag._id}-${feld.feldId}`
                                      )
                                    }
                                    size="icon"
                                    title="Wert kopieren"
                                    variant="ghost"
                                  >
                                    <Copy className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                            ) : fieldType === FIELD_TYPES.EMAIL ? (
                              <div className="flex items-center gap-1">
                                <a
                                  className="text-cyan-400 hover:text-cyan-300"
                                  href={`mailto:${displayValue}`}
                                >
                                  {displayValue}
                                </a>
                                {feld.istKopierbar && displayValue !== '-' && (
                                  <Button
                                    className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? 'text-green-400' : 'text-blue-400 hover:text-blue-500'} shrink-0 hover:bg-blue-500/10`}
                                    onClick={() =>
                                      copyToClipboard(
                                        displayValue,
                                        `field-${eintrag._id}-${feld.feldId}`
                                      )
                                    }
                                    size="icon"
                                    title="Wert kopieren"
                                    variant="ghost"
                                  >
                                    <Copy className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                            ) : fieldType === FIELD_TYPES.TEXTAREA ? (
                              <div className="flex items-start gap-1">
                                <pre className="whitespace-pre-wrap font-sans text-xs">
                                  {displayValue}
                                </pre>
                                {feld.istKopierbar && displayValue !== '-' && (
                                  <Button
                                    className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? 'text-green-400' : 'text-blue-400 hover:text-blue-500'} shrink-0 hover:bg-blue-500/10`}
                                    onClick={() =>
                                      copyToClipboard(
                                        displayValue,
                                        `field-${eintrag._id}-${feld.feldId}`
                                      )
                                    }
                                    size="icon"
                                    title="Wert kopieren"
                                    variant="ghost"
                                  >
                                    <Copy className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                            ) : (
                              <div className="flex items-center gap-1">
                                <span>{displayValue}</span>
                                {feld.istKopierbar && displayValue !== '-' && (
                                  <Button
                                    className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? 'text-green-400' : 'text-blue-400 hover:text-blue-500'} shrink-0 hover:bg-blue-500/10`}
                                    onClick={() =>
                                      copyToClipboard(
                                        displayValue,
                                        `field-${eintrag._id}-${feld.feldId}`
                                      )
                                    }
                                    size="icon"
                                    title="Wert kopieren"
                                    variant="ghost"
                                  >
                                    <Copy className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                            )}
                          </TableCell>
                        );
                      })}

                    <TableCell className="px-2.5 py-1 text-center align-middle text-xs">
                      <div className="flex justify-center gap-1">
                        {/* Extrafelder-Button, nur anzeigen wenn es Extrafelder gibt */}
                        {kategorie.felder.some((feld) => feld.istExtrafeld) &&
                          eintrag.feldwerte.some((fw) => {
                            const feld = kategorie.felder.find(
                              (f) => f.feldId === fw.feldId
                            );
                            return (
                              feld?.istExtrafeld &&
                              fw.feldWert &&
                              fw.feldWert.trim() !== ''
                            );
                          }) && (
                            <Button
                              className="h-6 w-6 text-purple-400 hover:bg-purple-500/10 hover:text-purple-500"
                              onClick={() => {
                                setCurrentExtraFieldsEntry(eintrag);
                                setExtraFieldsDialogOpen(true);
                              }}
                              size="icon"
                              title="Extrafelder anzeigen"
                              variant="ghost"
                            >
                              <NotebookText className="h-3 w-3" />
                            </Button>
                          )}
                        <Button
                          className="h-6 w-6 text-blue-400 hover:bg-blue-500/10 hover:text-blue-500"
                          onClick={() =>
                            onEditEntry(kategorie._id, eintrag._id)
                          }
                          size="icon"
                          title="Eintrag bearbeiten"
                          variant="ghost"
                        >
                          <Pencil className="h-3 w-3" />
                        </Button>
                        <Button
                          className="h-6 w-6 text-red-400 hover:bg-red-500/10 hover:text-red-500"
                          onClick={() => onDeleteEntry(eintrag._id)}
                          size="icon"
                          title="Eintrag löschen"
                          variant="ghost"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      )}

      {/* ExtraFields Dialog */}
      {extraFieldsDialogOpen && currentExtraFieldsEntry && (
        <ExtraFieldsDialog
          fields={kategorie.felder
            .filter((feld) => feld.istExtrafeld)
            .map((feld) => {
              const feldWertObj = currentExtraFieldsEntry.feldwerte.find(
                (fw) => fw.feldId === feld.feldId
              );
              return {
                label: feld.name,
                value: feldWertObj?.feldWert || '',
                fieldType: feld.typ,
                isRequired: feld.istErforderlich,
                isCopyable: feld.istKopierbar,
                options: feld.optionen,
              };
            })
            .filter((field) => field.value && field.value.trim() !== '')}
          isOpen={extraFieldsDialogOpen}
          onClose={() => {
            setExtraFieldsDialogOpen(false);
            setCurrentExtraFieldsEntry(null);
          }}
          title="Extrafelder"
        />
      )}
    </Card>
  );
}
