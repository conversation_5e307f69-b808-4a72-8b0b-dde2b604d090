import { Check, Mail, QrCode } from 'lucide-react';
import { useState } from 'react';
import type { Doc } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import type { AnsprechpartnerData } from '@/components/verwaltung/kunden/KundenForm';
import { QRCodeModal } from './QRCodeModal';

interface AnsprechpartnerTableProps {
  kunde: Doc<'kunden'>;
}

export function AnsprechpartnerTable({ kunde }: AnsprechpartnerTableProps) {
  const [qrModalState, setQrModalState] = useState<{
    isOpen: boolean;
    partner: AnsprechpartnerData | null;
  }>({ isOpen: false, partner: null });

  const handleShowQRCode = (partner: AnsprechpartnerData) => {
    setQrModalState({ isOpen: true, partner });
  };

  const handleCloseQRModal = () => {
    setQrModalState({ isOpen: false, partner: null });
  };

  return (
    <>
      <Card className="overflow-hidden border-0 shadow-lg">
        <CardHeader className="border-gray-700/50 border-b px-4 pb-2.5">
          <CardTitle className="font-medium text-base">
            Ansprechpartner (aus Stammdaten)
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {kunde.ansprechpartner && kunde.ansprechpartner.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
                  <TableHead className="w-16 px-2.5 py-1.5 text-center text-xs">
                    E-Mail
                  </TableHead>
                  <TableHead className="w-12 px-2.5 py-1.5 text-center text-xs">
                    Haupt
                  </TableHead>
                  <TableHead className="px-2.5 py-1.5 text-xs">Name</TableHead>
                  <TableHead className="px-2.5 py-1.5 text-xs">
                    Position
                  </TableHead>
                  <TableHead className="px-2.5 py-1.5 text-xs">
                    E-Mail
                  </TableHead>
                  <TableHead className="px-2.5 py-1.5 text-xs">
                    Telefon
                  </TableHead>
                  <TableHead className="px-2.5 py-1.5 text-xs">Mobil</TableHead>
                  <TableHead className="w-16 px-2.5 py-1.5 text-center text-xs">
                    Aktionen
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {kunde.ansprechpartner.map((partner, index) => {
                  return (
                    <TableRow key={index}>
                      <TableCell className="px-2.5 py-1 text-center text-xs">
                        <div className="flex justify-center gap-0.5">
                          {partner.istEmailLieferscheinEmpfaenger && (
                            <div
                              className="h-3 w-3 text-blue-400"
                              title="Lieferschein-Empfänger"
                            >
                              <Mail className="h-3 w-3" />
                            </div>
                          )}
                          {partner.istEmailUebersichtEmpfaenger && (
                            <div
                              className="h-3 w-3 text-green-400"
                              title="Übersicht-Empfänger"
                            >
                              <Mail className="h-3 w-3" />
                            </div>
                          )}
                          {partner.istEmailAnrede && (
                            <div
                              className="h-3 w-3 text-purple-400"
                              title="E-Mail-Anrede"
                            >
                              <Mail className="h-3 w-3" />
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="px-2.5 py-1 text-center text-xs">
                        {partner.istHauptansprechpartner && (
                          <Check className="mx-auto h-4 w-4 text-green-400" />
                        )}
                      </TableCell>
                      <TableCell className="px-2.5 py-1 text-xs">
                        {partner.name}
                      </TableCell>
                      <TableCell className="px-2.5 py-1 text-xs">
                        {partner.position || '-'}
                      </TableCell>
                      <TableCell className="px-2.5 py-1 text-xs">
                        {partner.email ? (
                          <a
                            className="text-cyan-400 hover:text-cyan-300"
                            href={`mailto:${partner.email}`}
                          >
                            {partner.email}
                          </a>
                        ) : (
                          '-'
                        )}
                      </TableCell>
                      <TableCell className="px-2.5 py-1 text-xs">
                        {partner.telefon || '-'}
                      </TableCell>
                      <TableCell className="px-2.5 py-1 text-xs">
                        {partner.mobil || '-'}
                      </TableCell>
                      <TableCell className="px-2.5 py-1 text-center text-xs">
                        <div className="flex justify-center gap-1">
                          <Button
                            className="h-6 w-6 text-green-400 hover:bg-green-500/10 hover:text-green-500"
                            onClick={() => handleShowQRCode(partner)}
                            size="icon"
                            title="QR-Code anzeigen"
                            variant="ghost"
                          >
                            <QrCode className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          ) : (
            <p className="p-4 text-gray-400">
              Keine Ansprechpartner in den Stammdaten hinterlegt.
            </p>
          )}
        </CardContent>
      </Card>

      {qrModalState.isOpen && qrModalState.partner && (
        <QRCodeModal
          isOpen={qrModalState.isOpen}
          kunde={kunde}
          onClose={handleCloseQRModal}
          partner={qrModalState.partner}
        />
      )}
    </>
  );
}
