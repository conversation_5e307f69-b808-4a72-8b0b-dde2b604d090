import { X } from 'lucide-react';
import { QRCodeSVG } from 'qrcode.react';
import { useMemo } from 'react';
import type { Doc } from '@/../convex/_generated/dataModel';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/_shared/Dialog';
import type { AnsprechpartnerData } from '@/components/verwaltung/kunden/KundenForm';

interface QRCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  partner: AnsprechpartnerData;
  kunde: Doc<'kunden'>;
}

export function QRCodeModal({
  isOpen,
  onClose,
  partner,
  kunde,
}: QRCodeModalProps) {
  const { vCardData, formattedAddress } = useMemo(() => {
    const nameParts = partner.name.split(' ');
    const lastName = nameParts.pop() || '';
    const firstName = nameParts.join(' ') || '';

    const hauptstandort = kunde.standorte?.find((s) => s.istHauptstandort);

    // Formatierte Adresse: "Straße Nr, PLZ Ort, Land"
    let formattedAddress = '';
    if (hauptstandort) {
      formattedAddress = [
        hauptstandort.strasse || '',
        hauptstandort.plz && hauptstandort.ort
          ? `${hauptstandort.plz} ${hauptstandort.ort}`
          : hauptstandort.ort || '',
        hauptstandort.land || '',
      ]
        .filter(Boolean)
        .join(', ');
    }

    let vCard = 'BEGIN:VCARD\n';
    vCard += 'VERSION:3.0\n';
    vCard += `FN:${partner.name}\n`;
    vCard += `N:${lastName};${firstName};;;\n`;

    if (partner.email) {
      vCard += `EMAIL;TYPE=INTERNET:${partner.email}\n`;
    }

    // Telefonnummern mit expliziten Labels
    if (partner.telefon) {
      vCard += `TEL;TYPE=WORK:${partner.telefon}\n`;
    }

    if (partner.mobil) {
      vCard += `TEL;TYPE=CELL:${partner.mobil}\n`;
    }

    if (partner.position) {
      vCard += `TITLE:${partner.position}\n`;
    }

    vCard += `ORG:${kunde.name}\n`;

    // Adresse in strukturierter Form
    if (hauptstandort) {
      vCard += `ADR;TYPE=WORK:;;${hauptstandort.strasse || ''};${hauptstandort.ort || ''};${''};${hauptstandort.plz || ''};${hauptstandort.land || ''}\n`;
    }

    vCard += 'END:VCARD';

    return { vCardData: vCard, formattedAddress };
  }, [partner, kunde]);

  return (
    <Dialog onOpenChange={onClose} open={isOpen}>
      <DialogContent
        aria-describedby="qr-code-description"
        className="sm:max-w-md"
      >
        <DialogHeader>
          <DialogTitle>QR-Code: {partner.name}</DialogTitle>
          <DialogDescription id="qr-code-description">
            Scannen Sie den QR-Code mit Ihrem Smartphone, um die Kontaktdaten zu
            importieren.
          </DialogDescription>
          <DialogClose className="absolute top-4 right-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">Schließen</span>
          </DialogClose>
        </DialogHeader>

        <div className="flex flex-col items-center gap-4 py-4">
          <div className="rounded-lg bg-white p-4">
            <QRCodeSVG
              includeMargin={true}
              level="M"
              size={256}
              value={vCardData}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
