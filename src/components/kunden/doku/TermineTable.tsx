import { Calendar, Repeat } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import type { Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import {
  calculateTimeRemaining,
  formatDate,
  formatTime,
} from '@/lib/utils/dateUtils';

interface TerminData {
  _id: Id<'kunden_termine'>;
  titel: string;
  kategorie: string;
  datum?: string;
  uhrzeit?: string;
  istWiederholend: boolean;
  naechsteWiederholung?: string;
}

interface TermineTableProps {
  termine: TerminData[];
  kundenId: Id<'kunden'>;
}

export function TermineTable({ termine, kundenId }: TermineTableProps) {
  return (
    <Card className="overflow-hidden border-0 shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between border-gray-700/50 border-b px-4 pb-2.5">
        <CardTitle className="font-medium text-base">Termine</CardTitle>
        <Link to={`/kunden/termine/${kundenId}`}>
          <Button
            className="h-6 w-6 text-gray-400 hover:bg-gray-700/70 hover:text-white"
            size="icon"
            title="Alle Termine verwalten"
            variant="ghost"
          >
            <Calendar className="h-3 w-3" />
          </Button>
        </Link>
      </CardHeader>
      <CardContent className="p-0">
        {termine && termine.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
                <TableHead className="px-2.5 py-1.5 text-xs">
                  Kategorie
                </TableHead>
                <TableHead className="px-2.5 py-1.5 text-xs">Titel</TableHead>
                <TableHead className="px-2.5 py-1.5 text-xs">Datum</TableHead>
                <TableHead className="px-2.5 py-1.5 text-xs">Uhrzeit</TableHead>
                <TableHead className="px-2.5 py-1.5 text-xs">
                  Restlaufzeit
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {termine.slice(0, 5).map((termin) => {
                const timeRemaining = calculateTimeRemaining(
                  termin.istWiederholend
                    ? termin.naechsteWiederholung
                    : termin.datum,
                  termin.uhrzeit || ''
                );

                return (
                  <TableRow key={termin._id}>
                    <TableCell className="px-2.5 py-1 text-xs">
                      <span className="inline-flex items-center rounded-full bg-blue-500/10 px-1.5 py-0.5 text-blue-400 text-xs">
                        {termin.kategorie}
                      </span>
                    </TableCell>
                    <TableCell className="px-2.5 py-1 font-medium text-xs">
                      <div className="flex items-center gap-1">
                        {termin.titel}
                        {termin.istWiederholend && (
                          <div
                            className="h-3 w-3 text-blue-400"
                            title="Wiederholender Termin"
                          >
                            <Repeat className="h-3 w-3" />
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="px-2.5 py-1 text-xs">
                      {termin.istWiederholend
                        ? formatDate(termin.naechsteWiederholung || '')
                        : formatDate(termin.datum || '')}
                    </TableCell>
                    <TableCell className="px-2.5 py-1 text-xs">
                      {termin.uhrzeit ? formatTime(termin.uhrzeit) : '-'}
                    </TableCell>
                    <TableCell className="px-2.5 py-1 text-xs">
                      <span
                        className={
                          timeRemaining.startsWith('vor')
                            ? 'text-red-400'
                            : 'text-green-400'
                        }
                      >
                        {timeRemaining}
                      </span>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        ) : (
          <p className="p-4 text-gray-400">Keine Termine vorhanden.</p>
        )}
      </CardContent>
    </Card>
  );
}
