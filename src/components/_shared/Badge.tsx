import type * as React from 'react';
import { cn } from '../../lib/utils/cn';

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?:
    | 'default'
    | 'secondary'
    | 'destructive'
    | 'outline'
    | 'success'
    | 'warning';
}

function Badge({ className, variant = 'default', ...props }: BadgeProps) {
  return (
    <div
      className={cn(
        'inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold text-xs transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
        {
          'border-transparent bg-blue-500 text-white hover:bg-blue-600':
            variant === 'default',
          'border-transparent bg-gray-700 text-gray-100 hover:bg-gray-600':
            variant === 'secondary',
          'border-transparent bg-red-500 text-white hover:bg-red-600':
            variant === 'destructive',
          'border-gray-700 text-gray-100 hover:bg-gray-800':
            variant === 'outline',
          'border-transparent bg-green-500 text-white hover:bg-green-600':
            variant === 'success',
          'border-transparent bg-amber-500 text-white hover:bg-amber-600':
            variant === 'warning',
        },
        className
      )}
      {...props}
    />
  );
}

export { Badge };
