import { useClerk, useUser } from '@clerk/clerk-react';
import {
  Briefcase,
  ChevronDown,
  ChevronUp,
  LayoutDashboard,
  LogOut,
  Menu,
  Settings,
  UserCircle,
  Users,
  X,
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FeedbackButton } from '../system/feedback/FeedbackButton';

type NavItem = {
  label: string;
  path?: string;
  icon?: React.ReactNode;
  children?: { label: string; path: string }[];
};

export function Navigation() {
  const location = useLocation();
  const [openNavMenu, setOpenNavMenu] = useState<string | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [openMobileCategory, setOpenMobileCategory] = useState<string | null>(
    null
  );
  const navRef = useRef<HTMLElement>(null);

  // Get user info from Clerk
  const { isLoaded, isSignedIn, user } = useUser();
  // Get Clerk functions
  const { openUserProfile, signOut } = useClerk();

  // Get user name and profile image
  const userName = user
    ? `${user.firstName || ''} ${user.lastName || ''}`.trim()
    : '';
  const userProfileImage = user?.imageUrl;

  // Fallback to email if name is not available
  const userDisplayName =
    userName ||
    user?.primaryEmailAddress?.emailAddress ||
    user?.emailAddresses[0]?.emailAddress;

  const toggleNavMenu = (label: string) => {
    setOpenNavMenu((prevOpenMenu) => (prevOpenMenu === label ? null : label));
  };

  const toggleMobileCategory = (label: string) => {
    setOpenMobileCategory((prevCategory) =>
      prevCategory === label ? null : label
    );
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (navRef.current && !navRef.current.contains(event.target as Node)) {
        setOpenNavMenu(null);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setOpenNavMenu(null);
    setIsMobileMenuOpen(false);
    setOpenMobileCategory(null);
  }, []);

  const isActive = (path?: string, isParent = false) => {
    if (!path) {
      return false;
    }
    if (isParent) {
      return location.pathname.startsWith(path);
    }
    return location.pathname === path;
  };

  // Updated Navigation Structure
  const navItems: NavItem[] = [
    {
      label: 'Kunden',
      icon: <Users className="h-4 w-4" />,
      path: '/kunden', // Base path for highlighting
      children: [
        { label: 'Dokumentation', path: '/kunden/doku' },
        { label: 'Termine', path: '/kunden/termine' },
      ],
    },
    {
      label: 'Erstellung',
      icon: <Briefcase className="h-4 w-4" />,
      path: '/erstellung', // Base path for highlighting
      children: [
        { label: 'Leistung', path: '/erstellung/leistung' },
        { label: 'Übersicht', path: '/erstellung/uebersicht' },
        { label: 'Angebote', path: '/erstellung/angebote' },
        { label: 'Lieferscheine', path: '/erstellung/lieferscheine' },
      ],
    },
    {
      label: 'Verwaltung',
      icon: <Settings className="h-4 w-4" />,
      path: '/verwaltung', // Base path for highlighting
      children: [
        { label: 'Kunden', path: '/verwaltung/kunden' },
        { label: 'Kontingente', path: '/verwaltung/kontingente' },
        { label: 'Mitarbeiter', path: '/verwaltung/mitarbeiter' },
      ],
    },
    {
      label: 'System',
      icon: <LayoutDashboard className="h-4 w-4" />,
      path: '/system', // Base path for highlighting
      children: [
        { label: 'Feedback', path: '/system/feedback' },
        { label: 'Standards', path: '/system/standards' },
        { label: 'Doku-Kategorien', path: '/system/doku-kategorien' },
      ],
    },
  ];

  return (
    <nav
      className="sticky top-0 z-10 border-gray-700/50 border-b bg-gray-800/80 shadow-md backdrop-blur-sm"
      ref={navRef}
    >
      <div className="container mx-auto max-w-7xl px-4">
        <div className="flex h-14 items-center justify-between">
          {/* Left side: Logo/Brand */}
          <div className="flex-shrink-0">
            <Link
              className="flex items-center font-bold text-white text-xl"
              to="/"
            >
              {/* Updated Branding */}
              innov<span className="text-blue-400">8</span>-IT
            </Link>
          </div>

          {/* Center: Navigation Links */}
          <div className="hidden md:flex md:items-center md:space-x-1 lg:space-x-1">
            {navItems.map((item) => (
              <div className="group relative" key={item.label}>
                <button
                  className={`flex items-center rounded-md px-3 py-2 font-medium text-sm transition-colors ${
                    isActive(item.path, true) ||
                    (item.children?.some((child) => isActive(child.path)))
                      ? 'bg-primary/20 text-primary'
                      : 'text-gray-300 hover:bg-gray-700/70 hover:text-white'
                  }`}
                  onClick={() => item.children && toggleNavMenu(item.label)}
                >
                  {item.icon && <span className="mr-1.5">{item.icon}</span>}
                  <span>{item.label}</span>
                  {item.children &&
                    (openNavMenu === item.label ? (
                      <ChevronUp className="ml-1 h-3.5 w-3.5" />
                    ) : (
                      <ChevronDown className="ml-1 h-3.5 w-3.5" />
                    ))}
                </button>
                {item.children && openNavMenu === item.label && (
                  <div className="fade-in absolute left-0 z-10 mt-1 w-40 origin-top-left animate-in rounded-md border border-gray-700/50 bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 duration-100 focus:outline-none">
                    <div
                      aria-labelledby="options-menu"
                      aria-orientation="vertical"
                      className="py-1"
                      role="menu"
                    >
                      {item.children.map((child) => (
                        <Link
                          className={`block px-4 py-2 text-sm ${
                            isActive(child.path)
                              ? 'bg-primary/10 text-primary'
                              : 'text-gray-300 hover:bg-gray-700/70 hover:text-white'
                          }`}
                          key={child.path}
                          role="menuitem"
                          to={child.path}
                        >
                          {child.label}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Right side: User Profile and Logout */}
          <div className="relative hidden items-center gap-2 md:flex">
            {/* Show only when Clerk is loaded and user is signed in */}
            {isLoaded && isSignedIn && (
              <>
                {/* User Profile Button */}
                <button
                  className="flex items-center gap-2 rounded-md p-2 text-gray-300 text-sm hover:bg-gray-700/70 hover:text-white"
                  onClick={() => openUserProfile()}
                  title="Kontoeinstellungen öffnen"
                >
                  {/* User Profile Image */}
                  {userProfileImage ? (
                    <img
                      alt="Profilbild"
                      className="h-7 w-7 rounded-full border border-gray-700 object-cover"
                      src={userProfileImage}
                    />
                  ) : (
                    <div className="flex h-7 w-7 items-center justify-center rounded-full bg-gray-700 text-gray-300">
                      <UserCircle className="h-4 w-4" />
                    </div>
                  )}
                  {/* User Name */}
                  <span
                    className="max-w-[150px] truncate text-xs"
                    title={userDisplayName ?? undefined}
                  >
                    {userDisplayName}
                  </span>
                </button>

                {/* Feedback Button */}
                <FeedbackButton className="rounded-md p-2 text-gray-300 hover:bg-gray-700/70 hover:text-yellow-300" />

                {/* Direct Logout Button */}
                <button
                  className="rounded-md p-2 text-gray-300 hover:bg-gray-700/70 hover:text-red-300"
                  onClick={() => signOut({ redirectUrl: '/signin' })}
                  title="Abmelden"
                >
                  <LogOut className="h-4 w-4" />
                </button>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="flex items-center md:hidden">
            {/* Hamburger Button */}
            <button
              className="rounded-md p-2 text-gray-300 hover:bg-gray-700/70 hover:text-white"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu Panel */}
      {isMobileMenuOpen && (
        <div className="fade-in slide-in-from-top-2 animate-in border-gray-700/50 border-t bg-gray-800/95 backdrop-blur-sm duration-200 md:hidden">
          {/* User Info Section (Mobile) */}
          {isLoaded && isSignedIn && (
            <div className="flex items-center border-gray-700/50 border-b px-4 py-3">
              {/* User Profile Image and Name */}
              <div className="flex flex-1 items-center">
                {userProfileImage ? (
                  <img
                    alt="Profilbild"
                    className="mr-3 h-8 w-8 rounded-full border border-gray-700 object-cover"
                    src={userProfileImage}
                  />
                ) : (
                  <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-gray-700 text-gray-300">
                    <UserCircle className="h-5 w-5" />
                  </div>
                )}
                <div>
                  <div className="font-medium text-sm text-white">
                    {userName}
                  </div>
                  <div className="max-w-[200px] truncate text-gray-400 text-xs">
                    {user?.primaryEmailAddress?.emailAddress}
                  </div>
                </div>
              </div>

              {/* Account Settings Button */}
              <button
                className="rounded-md p-2 text-gray-300 hover:bg-gray-700/70 hover:text-white"
                onClick={() => openUserProfile()}
                title="Kontoeinstellungen"
              >
                <Settings className="h-5 w-5" />
              </button>

              {/* Feedback Button */}
              <FeedbackButton className="ml-1 rounded-md p-2 text-gray-300 hover:bg-gray-700/70 hover:text-yellow-300" />

              {/* Logout Button */}
              <button
                className="ml-1 rounded-md p-2 text-gray-300 hover:bg-gray-700/70 hover:text-red-300"
                onClick={() => signOut({ redirectUrl: '/signin' })}
                title="Abmelden"
              >
                <LogOut className="h-5 w-5" />
              </button>
            </div>
          )}

          {/* Navigation Menu */}
          <div className="space-y-1 px-2 pt-2 pb-3 sm:px-3">
            {navItems.map((item) => (
              <div className="mb-1" key={item.label}>
                {/* Category Header */}
                <button
                  className={`flex w-full items-center justify-between rounded-md px-3 py-2 font-medium text-sm ${
                    isActive(item.path, true) ||
                    (item.children?.some((child) => isActive(child.path)))
                      ? 'bg-primary/20 text-primary'
                      : 'bg-gray-700/30 text-gray-200 hover:bg-gray-700/50'
                  }`}
                  onClick={() => toggleMobileCategory(item.label)}
                >
                  <div className="flex items-center">
                    {item.icon && <span className="mr-2">{item.icon}</span>}
                    <span>{item.label}</span>
                  </div>
                  {item.children &&
                    (openMobileCategory === item.label ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    ))}
                </button>

                {/* Category Children */}
                {item.children && openMobileCategory === item.label && (
                  <div className="mt-1 ml-3 space-y-1 border-gray-700/50 border-l pl-2">
                    {item.children.map((child) => (
                      <Link
                        className={`flex items-center rounded-md px-3 py-2 text-sm ${
                          isActive(child.path)
                            ? 'bg-primary/10 text-primary'
                            : 'text-gray-300 hover:bg-gray-700/70 hover:text-white'
                        }`}
                        key={child.path}
                        onClick={() => setIsMobileMenuOpen(false)}
                        to={child.path}
                      >
                        {child.label}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
}
