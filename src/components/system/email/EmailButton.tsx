import { Mail } from 'lucide-react';
import { useState } from 'react';
import type { Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/_shared/Button';
import { EmailDialog } from './EmailDialog';

interface EmailButtonProps {
  type: 'lieferschein' | 'uebersicht';
  lieferscheinId?: Id<'kunden_lieferscheine'>;
  kundeId?: Id<'kunden'>;
  zeitraum?: string;
  pdfBase64?: string;
  variant?:
    | 'default'
    | 'secondary'
    | 'destructive'
    | 'outline'
    | 'ghost'
    | 'link';
  className?: string;
  size?: 'default' | 'sm' | 'lg' | 'icon';
  tooltipText?: string;
}

export function EmailButton({
  type,
  lieferscheinId,
  kundeId,
  zeitraum,
  pdfBase64,
  variant = 'ghost',
  className = 'h-8 w-8 text-gray-400 hover:text-blue-400',
  size = 'icon',
  tooltipText = 'Per E-Mail senden',
}: EmailButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <>
      <Button
        className={className}
        onClick={() => setIsDialogOpen(true)}
        size={size}
        title={tooltipText}
        variant={variant}
      >
        <Mail className="h-4 w-4" />
        <span className="sr-only">{tooltipText}</span>
      </Button>

      <EmailDialog
        isOpen={isDialogOpen}
        kundeId={kundeId}
        lieferscheinId={lieferscheinId}
        onClose={() => setIsDialogOpen(false)}
        pdfBase64={pdfBase64}
        type={type}
        zeitraum={zeitraum}
      />
    </>
  );
}
