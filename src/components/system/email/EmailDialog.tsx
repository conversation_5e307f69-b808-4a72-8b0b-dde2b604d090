import { pdf } from '@react-pdf/renderer';
import { useMutation, useQuery } from 'convex/react';
import { Building, CheckCircle, Mail, User } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { api } from '@/../convex/_generated/api';
import type { Id } from '@/../convex/_generated/dataModel';
import { defaultLieferscheinConfig } from '@/../convex/erstellung/lieferscheineConfig';
import { PDFLieferscheinDocument } from '@/components/erstellung/lieferscheine/PDFDocument';
import { formatCurrency, formatHours } from '@/lib/utils/formatUtils';

interface EmailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'lieferschein' | 'uebersicht';
  lieferscheinId?: Id<'kunden_lieferscheine'>;
  kundeId?: Id<'kunden'>;
  zeitraum?: string;
  pdfBase64?: string;
}

export function EmailDialog({
  isOpen,
  onClose,
  type,
  lieferscheinId,
  kundeId,
  zeitraum,
  pdfBase64,
}: EmailDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Get recipient info - always call hooks, use skip parameter
  const lieferscheinDetails = useQuery(
    api.erstellung.lieferschein.get,
    lieferscheinId ? { id: lieferscheinId } : 'skip'
  );

  const kundeIdToUse = kundeId || lieferscheinDetails?.lieferschein?.kundenId;
  const kunde = useQuery(
    api.verwaltung.kunden.get,
    kundeIdToUse ? { id: kundeIdToUse } : 'skip'
  );

  // Send email mutations
  const sendLieferschein = useMutation(api.system.email.sendLieferschein);
  const sendUebersicht = useMutation(api.system.email.sendUebersicht);

  // Function to get main contact for email greeting (istEmailAnrede)
  const getEmailGreetingContact = () => {
    if (!kunde?.ansprechpartner) {
      return null;
    }
    // Only return contact with istEmailAnrede = true
    return kunde.ansprechpartner.find((a) => a.istEmailAnrede) || null;
  };

  // Function to get email recipients for customers based on type
  const getCustomerEmailRecipients = () => {
    if (!kunde?.ansprechpartner) {
      return [];
    }
    if (type === 'lieferschein') {
      return kunde.ansprechpartner.filter(
        (a) => a.istEmailLieferscheinEmpfaenger && a.email
      );
    }
    if (type === 'uebersicht') {
      return kunde.ansprechpartner.filter(
        (a) => a.istEmailUebersichtEmpfaenger && a.email
      );
    }
    return [];
  };

  // Get employee IDs from Lieferschein or Übersicht
  const employeeIds =
    type === 'lieferschein' && lieferscheinDetails?.leistungen
      ? Array.from(
          new Set(
            lieferscheinDetails.leistungen
              .map((l) => l.mitarbeiterId)
              .filter(Boolean)
          )
        )
      : [];

  // Query all employees
  const allEmployees = useQuery(api.verwaltung.mitarbeiter.list);

  // Function to get employees involved in services (default selected)
  const getInvolvedEmployees = () => {
    if (!allEmployees) {
      return [];
    }
    if (type === 'lieferschein' && employeeIds.length > 0) {
      return allEmployees.filter((emp) => employeeIds.includes(emp._id));
    }
    // For Übersicht, default to all employees
    return allEmployees;
  };

  // Function to generate PDF for Lieferschein
  const generateLieferscheinPDF = async () => {
    if (
      !(lieferscheinDetails?.lieferschein && lieferscheinDetails.leistungen)
    ) {
      throw new Error('Lieferschein-Daten nicht verfügbar');
    }

    const { lieferschein, leistungen } = lieferscheinDetails;
    const settings = defaultLieferscheinConfig.settings;

    const pdfDocument = (
      <PDFLieferscheinDocument
        firmenFusszeileText={settings.fusszeileText}
        firmenName="innov8-IT"
        formatCurrency={formatCurrency}
        formatHours={formatHours}
        includeFooter={settings.includeFooter}
        includeHeader={settings.includeHeader}
        includeLegalText={!!settings.legalText}
        includeSignatureField={settings.includeSignatureField}
        legalText={settings.legalText}
        leistungen={leistungen.map((l: any) => ({
          _id: l._id,
          startZeit: l.startZeit,
          endZeit: l.endZeit,
          art: l.art,
          beschreibung: l.beschreibung,
          stunden: l.stunden,
          stundenpreis: l.stundenpreis,
          anfahrtskosten: l.anfahrtskosten,
          mitAnfahrt: l.mitAnfahrt,
          datum: new Date(l.startZeit).toLocaleDateString('de-DE'),
          startZeitFormatiert: new Date(l.startZeit).toLocaleTimeString(
            'de-DE',
            { hour: '2-digit', minute: '2-digit' }
          ),
          endZeitFormatiert: new Date(l.endZeit).toLocaleTimeString('de-DE', {
            hour: '2-digit',
            minute: '2-digit',
          }),
          kontingentName: l.kontingentName,
          kontingentName2: l.kontingentName2,
          mitarbeiterName: l.mitarbeiterName,
        }))}
        lieferschein={{
          _id: lieferschein._id,
          nummer: lieferschein.nummer || '',
          erstelltAm: lieferschein.erstelltAm,
          erstelltAmFormatiert: new Date(
            lieferschein.erstelltAm
          ).toLocaleDateString('de-DE'),
          istKorrektur: lieferschein.istKorrektur,
          bemerkung: lieferschein.bemerkung,
          kundeName: kunde?.name || '',
        }}
        logoUrl={settings.logoPath}
        showLogo={settings.showLogo}
        signatureText={settings.signatureText}
      />
    );

    const blob = await pdf(pdfDocument).toBlob();
    const buffer = await blob.arrayBuffer();
    // Use Uint8Array and btoa instead of Buffer for browser compatibility
    const uint8Array = new Uint8Array(buffer);
    let binaryString = '';
    for (let i = 0; i < uint8Array.length; i++) {
      binaryString += String.fromCharCode(uint8Array[i]);
    }
    const base64 = btoa(binaryString);
    return base64;
  };

  const handleSubmit = async () => {
    if (!isOpen) {
      return;
    }

    setIsSubmitting(true);

    try {
      if (type === 'lieferschein' && lieferscheinId) {
        try {
          // Generate PDF first
          const pdfBase64 = await generateLieferscheinPDF();

          // Prepare recipients
          const employeeRecipients =
            selectedEmployees.length > 0
              ? allEmployees
                  ?.filter((emp) => selectedEmployees.includes(emp._id))
                  .map((emp) => ({
                    email: emp.email,
                    name: emp.name,
                  })) || []
              : [];

          const customerRecipients =
            selectedCustomers.length > 0
              ? kunde?.ansprechpartner
                  ?.filter((a) => selectedCustomers.includes(a.email || ''))
                  .map((a) => ({
                    email: a.email || '',
                    name: a.name,
                  })) || []
              : [];

          // Then send email with PDF attached
          await sendLieferschein({
            lieferscheinId,
            sendToMitarbeiter: employeeRecipients.length > 0,
            sendToKunde: customerRecipients.length > 0,
            pdfBase64,
            mainContactName:
              emailGreetingContact?.name ||
              kunde?.ansprechpartner?.find((a) => a.istHauptansprechpartner)
                ?.name ||
              'Kunde',
            employeeRecipients,
            customerRecipients,
          });

          toast.success('E-Mail wurde erfolgreich versendet!');
        } catch (pdfError: any) {
          toast.error(
            `Fehler bei PDF-Generierung: ${pdfError.message || 'Unbekannter Fehler'}`
          );
          return;
        }
      } else if (type === 'uebersicht' && kundeId) {
        // Prepare recipients
        const employeeRecipients =
          selectedEmployees.length > 0
            ? allEmployees
                ?.filter((emp) => selectedEmployees.includes(emp._id))
                .map((emp) => ({
                  email: emp.email,
                  name: emp.name,
                })) || []
            : [];

        const customerRecipients =
          selectedCustomers.length > 0
            ? kunde?.ansprechpartner
                ?.filter((a) => selectedCustomers.includes(a.email || ''))
                .map((a) => ({
                  email: a.email || '',
                  name: a.name,
                })) || []
            : [];

        await sendUebersicht({
          kundeId: kundeId!,
          zeitraum: zeitraum || '',
          pdfBase64: pdfBase64 || '',
          employeeRecipients,
          customerRecipients,
          mainContactName:
            emailGreetingContact?.name ||
            kunde?.ansprechpartner?.find((a) => a.istHauptansprechpartner)
              ?.name ||
            'Kunde',
        });

        toast.success('E-Mail wurde erfolgreich versendet!');
      } else {
        throw new Error('Ungültige Parameter für E-Mail-Versand.');
      }

      onClose();
    } catch (error: any) {
      toast.error(
        `Fehler beim Versenden der E-Mail: ${error.message || 'Unbekannter Fehler'}`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get recipient details
  const emailGreetingContact = getEmailGreetingContact();
  const customerRecipients = getCustomerEmailRecipients();
  // Show all customer contacts with email addresses in CC list
  const allCustomerContacts =
    kunde?.ansprechpartner?.filter((a) => a.email) || [];
  const involvedEmployees = getInvolvedEmployees();
  // otherEmployees not needed anymore since we show all employees in one list

  // Initialize selected recipients when data loads (only once)
  useEffect(() => {
    if (!hasInitialized && isOpen) {
      if (type === 'lieferschein') {
        // For Lieferschein: Select involved employees by default
        if (involvedEmployees.length > 0) {
          setSelectedEmployees(involvedEmployees.map((e) => e._id));
        }
        // Select customers marked as email recipients by default
        if (customerRecipients.length > 0) {
          setSelectedCustomers(customerRecipients.map((c) => c.email || ''));
        }
      } else if (type === 'uebersicht') {
        // For Übersicht: Select ALL employees by default
        if (allEmployees && allEmployees.length > 0) {
          setSelectedEmployees(allEmployees.map((e) => e._id));
        }
        // For Übersicht: Select customers marked as Übersicht recipients by default
        if (customerRecipients.length > 0) {
          setSelectedCustomers(customerRecipients.map((c) => c.email || ''));
        }
      }
      setHasInitialized(true);
    }
  }, [
    type,
    involvedEmployees,
    customerRecipients,
    allEmployees,
    isOpen,
    hasInitialized,
  ]);

  // Reset initialization when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setHasInitialized(false);
      setSelectedEmployees([]);
      setSelectedCustomers([]);
    }
  }, [isOpen]);

  // Check if there are any email recipients
  const hasEmailRecipients = allCustomerContacts.length > 0;
  const hasEmployeeRecipients = allEmployees && allEmployees.length > 0;

  if (!isOpen) {
    return null;
  }

  return (
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center overflow-y-auto bg-black/70 p-4"
      style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
    >
      <div className="fade-in zoom-in-95 my-auto w-full max-w-4xl animate-in rounded-lg border border-gray-700 bg-gray-800 shadow-xl duration-200">
        <div className="flex items-center justify-between border-gray-700 border-b p-4">
          <h3 className="flex items-center font-medium text-lg text-white">
            <Mail className="mr-2 h-5 w-5 text-blue-400" />
            {type === 'lieferschein'
              ? 'Lieferschein per E-Mail versenden'
              : 'Übersicht per E-Mail versenden'}
          </h3>
          <button
            className="rounded-full p-1 text-gray-400 hover:bg-gray-700 hover:text-white"
            onClick={onClose}
          >
            <span className="sr-only">Schließen</span>
            <svg
              className="h-5 w-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M6 18L18 6M6 6l12 12"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
              />
            </svg>
          </button>
        </div>

        <div className="p-4">
          <div className="space-y-4">
            {/* E-Mail Anrede (Blue) - Top Row */}
            <div>
              <h4 className="mb-2 font-medium text-base text-white">
                E-Mail Anrede
              </h4>

              <div className="rounded-md border border-blue-700/50 bg-blue-900/30 p-3">
                <div className="mb-2 flex items-center font-medium text-blue-300 text-sm">
                  <Mail className="mr-2 h-4 w-4" />
                  Hauptempfänger (Anrede)
                </div>
                {emailGreetingContact ? (
                  <>
                    <div className="font-medium text-white">
                      {emailGreetingContact.name}
                    </div>
                    <div className="text-gray-300 text-sm">
                      {emailGreetingContact.email || 'Keine E-Mail-Adresse'}
                    </div>
                    <div className="mt-1 text-blue-300 text-xs">
                      Anrede: "Sehr geehrte(r) {emailGreetingContact.name}"
                    </div>
                    <div className="mt-1 text-green-400 text-xs">
                      ✓ Als E-Mail-Anrede definiert
                    </div>
                  </>
                ) : (
                  <>
                    <div className="font-medium text-red-300">
                      Keine E-Mail-Anrede definiert
                    </div>
                    <div className="text-gray-400 text-sm">
                      Bitte in Kundendaten einen Ansprechpartner als
                      E-Mail-Anrede markieren
                    </div>
                    <div className="mt-1 text-red-400 text-xs">
                      ❌ E-Mail kann nicht versendet werden
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Bottom Row: Mitarbeiter (Green) and Kunden (Yellow) Side by Side */}
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
              {/* Mitarbeiter (Green) - Left */}
              <div>
                <h4 className="mb-2 font-medium text-base text-white">
                  Mitarbeiter
                </h4>

                {hasEmployeeRecipients && (
                  <div className="rounded-md border border-green-700/50 bg-green-900/30 p-3">
                    <div className="mb-2 flex items-center font-medium text-green-300">
                      <User className="mr-2 h-4 w-4" />
                      {type === 'lieferschein'
                        ? 'Mitarbeiter in CC'
                        : 'Alle Mitarbeiter in CC'}
                    </div>
                    <div className="max-h-36 space-y-1.5 overflow-y-auto">
                      {allEmployees?.map((employee) => {
                        const isInvolved = involvedEmployees.some(
                          (inv) => inv._id === employee._id
                        );
                        return (
                          <div
                            className="flex items-center space-x-2"
                            key={employee._id}
                          >
                            <input
                              checked={selectedEmployees.includes(employee._id)}
                              className="h-4 w-4 rounded border-gray-600 bg-gray-700 text-green-500 focus:ring-green-500 focus:ring-opacity-25"
                              id={`employee-${employee._id}`}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedEmployees((prev) => [
                                    ...prev,
                                    employee._id,
                                  ]);
                                } else {
                                  setSelectedEmployees((prev) =>
                                    prev.filter((id) => id !== employee._id)
                                  );
                                }
                              }}
                              type="checkbox"
                            />
                            <label
                              className="flex-1 text-gray-300 text-sm"
                              htmlFor={`employee-${employee._id}`}
                            >
                              <div className="flex items-center gap-2 font-medium">
                                {employee.name}
                                {isInvolved && (
                                  <span className="rounded bg-green-900/50 px-1.5 py-0.5 text-green-400 text-xs">
                                    {type === 'lieferschein'
                                      ? 'beteiligt'
                                      : 'standard'}
                                  </span>
                                )}
                              </div>
                              <div className="text-gray-400 text-xs">
                                {employee.email}
                              </div>
                            </label>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {!hasEmployeeRecipients && (
                  <div className="rounded-md border border-amber-700/50 bg-amber-900/30 p-3">
                    <p className="text-amber-300 text-sm">
                      ⚠️ Keine Mitarbeiter verfügbar.
                    </p>
                  </div>
                )}
              </div>

              {/* Kunden Ansprechpartner (Yellow) - Right */}
              <div>
                <h4 className="mb-2 font-medium text-base text-white">
                  Kunden-Ansprechpartner
                </h4>

                {hasEmailRecipients && (
                  <div className="rounded-md border border-yellow-700/50 bg-yellow-900/30 p-3">
                    <div className="mb-2 flex items-center font-medium text-yellow-300">
                      <Building className="mr-2 h-4 w-4" />
                      Weitere Ansprechpartner in CC
                    </div>
                    <div className="max-h-36 space-y-1.5 overflow-y-auto">
                      {allCustomerContacts.map((contact) => (
                        <div
                          className="flex items-center space-x-2"
                          key={contact.email}
                        >
                          <input
                            checked={selectedCustomers.includes(
                              contact.email || ''
                            )}
                            className="h-4 w-4 rounded border-gray-600 bg-gray-700 text-yellow-500 focus:ring-yellow-500 focus:ring-opacity-25"
                            id={`customer-${contact.email}`}
                            onChange={(e) => {
                              const email = contact.email || '';
                              if (e.target.checked) {
                                setSelectedCustomers((prev) => [
                                  ...prev,
                                  email,
                                ]);
                              } else {
                                setSelectedCustomers((prev) =>
                                  prev.filter((em) => em !== email)
                                );
                              }
                            }}
                            type="checkbox"
                          />
                          <label
                            className="flex-1 text-gray-300 text-sm"
                            htmlFor={`customer-${contact.email}`}
                          >
                            <div className="flex items-center gap-2 font-medium">
                              {contact.name}
                              {contact.istEmailAnrede && (
                                <span className="rounded bg-blue-900/50 px-1.5 py-0.5 text-blue-400 text-xs">
                                  E-Mail-Anrede
                                </span>
                              )}
                              {type === 'lieferschein' &&
                                contact.istEmailLieferscheinEmpfaenger && (
                                  <span className="rounded bg-yellow-900/50 px-1.5 py-0.5 text-xs text-yellow-400">
                                    Lieferschein-Empfänger
                                  </span>
                                )}
                              {type === 'uebersicht' &&
                                contact.istEmailUebersichtEmpfaenger && (
                                  <span className="rounded bg-yellow-900/50 px-1.5 py-0.5 text-xs text-yellow-400">
                                    Übersicht-Empfänger
                                  </span>
                                )}
                            </div>
                            <div className="text-gray-400 text-xs">
                              {contact.email}
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {!hasEmailRecipients && (
                  <div className="rounded-md border border-amber-700/50 bg-amber-900/30 p-3">
                    <p className="text-amber-300 text-sm">
                      ⚠️ Warnung: Kein Ansprechpartner mit E-Mail gefunden.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Document Details Section */}
          <div className="mt-4 border-gray-700 border-t pt-3">
            {type === 'lieferschein' && lieferscheinDetails?.lieferschein && (
              <div className="rounded-md bg-gray-700/30 p-3">
                <h5 className="mb-2 font-medium text-gray-300 text-sm">
                  Dokument-Details
                </h5>
                <div className="grid grid-cols-2 gap-4 text-gray-400 text-xs">
                  <div>
                    <span className="font-medium">Lieferschein:</span>
                    <br />
                    {lieferscheinDetails.lieferschein.nummer ||
                      'Ohne Nummer (Entwurf)'}
                  </div>
                  <div>
                    <span className="font-medium">Datum:</span>
                    <br />
                    {new Date(
                      lieferscheinDetails.lieferschein.erstelltAm
                    ).toLocaleDateString('de-DE')}
                  </div>
                </div>
              </div>
            )}

            {type === 'uebersicht' && kunde && zeitraum && (
              <div className="rounded-md bg-gray-700/30 p-3">
                <h5 className="mb-2 font-medium text-gray-300 text-sm">
                  Dokument-Details
                </h5>
                <div className="grid grid-cols-2 gap-4 text-gray-400 text-xs">
                  <div>
                    <span className="font-medium">Kunde:</span>
                    <br />
                    {kunde.name}
                  </div>
                  <div>
                    <span className="font-medium">Zeitraum:</span>
                    <br />
                    {zeitraum}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-2 border-gray-700 border-t bg-gray-800 p-4">
          <button
            className="rounded-md bg-gray-700 px-4 py-2 text-gray-300 hover:bg-gray-600 focus:outline-none"
            disabled={isSubmitting}
            onClick={onClose}
          >
            Abbrechen
          </button>
          <button
            className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-500 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
            disabled={
              isSubmitting ||
              !emailGreetingContact ||
              (selectedEmployees.length === 0 && selectedCustomers.length === 0)
            }
            onClick={handleSubmit}
          >
            {isSubmitting ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-white border-b-2" />
                Wird versendet...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                E-Mail versenden
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
