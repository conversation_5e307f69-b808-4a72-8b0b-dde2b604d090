import { useUser } from '@clerk/clerk-react';
import { useMutation } from 'convex/react';
import { AlertCircle, BugIcon, Lightbulb, X } from 'lucide-react';
import { useState } from 'react';
import { api } from '@/../convex/_generated/api';

interface FeedbackDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export function FeedbackDialog({ isOpen, onClose }: FeedbackDialogProps) {
  const [feedbackText, setFeedbackText] = useState('');
  const [feedbackType, setFeedbackType] = useState<'feature' | 'bug'>(
    'feature'
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const createFeedback = useMutation(api.system.feedback.create);
  const { user } = useUser();

  // Wenn der Dialog nicht geöffnet ist, nichts rendern
  if (!isOpen) {
    return null;
  }

  const handleSubmit = async () => {
    if (!(feedbackText.trim() && user)) {
      return;
    }

    setIsSubmitting(true);

    try {
      await createFeedback({
        userName:
          `${user.firstName || ''} ${user.lastName || ''}`.trim() ||
          user.primaryEmailAddress?.emailAddress ||
          '',
        text: feedbackText,
        type: feedbackType,
      });

      // Erfolgreich gespeichert
      setFeedbackText('');
      onClose();
    } catch (_error) {
      // Error is handled by toast notification in the UI
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center overflow-y-auto bg-black/70 p-4"
      style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
    >
      <div className="fade-in zoom-in-95 my-auto w-full max-w-md animate-in rounded-lg border border-gray-700 bg-gray-800 shadow-xl duration-200">
        <div className="flex items-center justify-between border-gray-700 border-b p-4">
          <h3 className="flex items-center font-medium text-lg text-white">
            <AlertCircle className="mr-2 h-5 w-5 text-blue-400" />
            Feedback geben
          </h3>
          <button
            className="rounded-full p-1 text-gray-400 hover:bg-gray-700 hover:text-white"
            onClick={onClose}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-4">
          {/* Feedback-Typ Auswahl */}
          <div className="mb-4 flex space-x-2">
            <button
              className={`flex flex-1 items-center justify-center rounded-md px-3 py-2 ${
                feedbackType === 'feature'
                  ? 'border border-green-500/30 bg-green-500/20 text-green-400'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              onClick={() => setFeedbackType('feature')}
            >
              <Lightbulb className="mr-2 h-4 w-4" />
              Neue Funktion
            </button>
            <button
              className={`flex flex-1 items-center justify-center rounded-md px-3 py-2 ${
                feedbackType === 'bug'
                  ? 'border border-red-500/30 bg-red-500/20 text-red-400'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              onClick={() => setFeedbackType('bug')}
            >
              <BugIcon className="mr-2 h-4 w-4" />
              Bug melden
            </button>
          </div>

          {/* Feedback-Text */}
          <div className="mb-4">
            <textarea
              className="h-32 w-full rounded-md border border-gray-700 bg-gray-900 px-3 py-2 text-sm text-white placeholder-gray-500 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
              onChange={(e) => setFeedbackText(e.target.value)}
              placeholder={
                feedbackType === 'feature'
                  ? 'Beschreiben Sie Ihre Idee für eine neue Funktion...'
                  : 'Beschreiben Sie den gefundenen Fehler...'
              }
              value={feedbackText}
            />
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-2">
            <button
              className="rounded-md bg-gray-700 px-4 py-2 text-gray-300 hover:bg-gray-600 focus:outline-none"
              disabled={isSubmitting}
              onClick={onClose}
            >
              Abbrechen
            </button>
            <button
              className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-500 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
              disabled={!feedbackText.trim() || isSubmitting}
              onClick={handleSubmit}
            >
              {isSubmitting ? 'Wird gesendet...' : 'Absenden'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
