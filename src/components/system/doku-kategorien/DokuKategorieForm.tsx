import { <PERSON>, <PERSON>, <PERSON>Off, <PERSON><PERSON><PERSON><PERSON>, Trash2, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import type { Id } from '@/../convex/_generated/dataModel';
import { FIELD_TYPES } from '@/../convex/schema';
import { Button } from '@/components/_shared/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Checkbox } from '@/components/_shared/Checkbox';
import { Input } from '@/components/_shared/Input';
import { Label } from '@/components/_shared/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/_shared/Select';
import { Textarea } from '@/components/_shared/Textarea';

interface FormFeldData {
  feldId: number;
  name: string;
  typ: string;
  istErforderlich: boolean;
  optionen: string;
  placeholder: string;
}

interface KategorieData {
  _id: Id<'system_doku_kategorien'>;
  name: string;
  beschreibung: string;
  istVordefiniert: boolean;
  felder: {
    feldId: number;
    name: string;
    typ: string;
    istErforderlich: boolean;
    optionen?: string[];
    placeholder?: string;
  }[];
  reihenfolge: number;
}

interface DokuKategorieFormProps {
  kategorien: KategorieData[];
  editingKategorie: KategorieData | null;
  onSubmitSuccess: () => void;
  onCancel: () => void;
}

export function DokuKategorieForm({
  kategorien,
  editingKategorie,
  onSubmitSuccess,
  onCancel,
}: DokuKategorieFormProps) {
  // const createDokuKategorie = useMutation(api.system.dokuKategorien.create);
  // const updateDokuKategorie = useMutation(api.system.dokuKategorien.update);

  const [formState, setFormState] = useState<{
    name: string;
    beschreibung: string;
    reihenfolge: string;
    felder: FormFeldData[];
  }>({
    name: '',
    beschreibung: '',
    reihenfolge: '10',
    felder: [],
  });

  const [passwordVisibility, setPasswordVisibility] = useState<
    Record<string, boolean>
  >({});

  useEffect(() => {
    if (editingKategorie) {
      setFormState({
        name: editingKategorie.name,
        beschreibung: editingKategorie.beschreibung,
        reihenfolge: editingKategorie.reihenfolge.toString(),
        felder: editingKategorie.felder.map((feld) => ({
          feldId: feld.feldId,
          name: feld.name,
          typ: feld.typ,
          istErforderlich: feld.istErforderlich,
          optionen: feld.optionen ? feld.optionen.join(', ') : '',
          placeholder: feld.placeholder || '',
        })),
      });
    } else {
      setFormState({
        name: '',
        beschreibung: '',
        reihenfolge: '10',
        felder: [
          {
            feldId: 0,
            name: '',
            typ: FIELD_TYPES.TEXT,
            istErforderlich: true,
            optionen: '',
            placeholder: '',
          },
        ],
      });
    }
  }, [editingKategorie]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { id, value } = e.target;
    setFormState((prev) => ({ ...prev, [id]: value }));
  };

  const handleFieldInputChange = (
    index: number,
    fieldKey: keyof Omit<FormFeldData, 'feldId'>,
    value: string | boolean
  ) => {
    setFormState((prev) => {
      const newFelder = [...prev.felder];
      (newFelder[index] as any)[fieldKey] = value;
      return { ...prev, felder: newFelder };
    });
  };

  const addField = () => {
    setFormState((prev) => ({
      ...prev,
      felder: [
        ...prev.felder,
        {
          feldId:
            prev.felder.length > 0
              ? Math.max(...prev.felder.map((f) => f.feldId)) + 1
              : 0,
          name: '',
          typ: FIELD_TYPES.TEXT,
          istErforderlich: true,
          optionen: '',
          placeholder: '',
        },
      ],
    }));
  };

  const removeField = (index: number) => {
    setFormState((prev) => {
      const newFelder = prev.felder.filter((_, i) => i !== index);
      return { ...prev, felder: newFelder };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Basic validation (can be expanded)
    if (!formState.name || formState.felder.some((f) => !(f.name && f.typ))) {
      toast.error(
        'Bitte Name der Kategorie und Name/Typ für alle Felder angeben.'
      );
      return;
    }

    try {
      /* if (isEditing && editingKategorie) {
				await updateDokuKategorie({ id: editingKategorie._id, ...formData });
				toast.success("Kategorie erfolgreich aktualisiert.");
			} else {
				await createDokuKategorie(formData);
				toast.success("Kategorie erfolgreich erstellt.");
			} */
      toast.info(
        'Speichern von Kategorien erfolgt über die Konfigurationsdatei und Synchronisation.'
      );
      onSubmitSuccess();
    } catch (_error) {
      toast.error('Fehler beim Speichern der Kategorie.');
    }
  };

  const isPredefinedEditing = editingKategorie?.istVordefiniert ?? false;

  const togglePasswordVisibility = (feldId: number) => {
    setPasswordVisibility((prev) => ({
      ...prev,
      [feldId.toString()]: !prev[feldId.toString()],
    }));
  };

  return (
    <Card className="mb-6 border-0 shadow-lg">
      <CardHeader className="border-gray-700/50 border-b px-5 pb-3">
        <CardTitle className="font-medium text-lg">
          {editingKategorie
            ? 'Kategorie bearbeiten'
            : 'Neue Kategorie erstellen'}
        </CardTitle>
      </CardHeader>
      <CardContent className="p-5">
        <form className="space-y-4" onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label className="text-sm" htmlFor="name">
                Name *
              </Label>
              <Input
                className="border-gray-700 bg-gray-800/60"
                disabled={isPredefinedEditing}
                id="name"
                onChange={handleInputChange}
                placeholder="Kategoriename"
                required
                value={formState.name}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm" htmlFor="reihenfolge">
                Reihenfolge *
              </Label>
              <Input
                className="border-gray-700 bg-gray-800/60"
                disabled={isPredefinedEditing}
                id="reihenfolge"
                min="1"
                onChange={handleInputChange}
                placeholder="Reihenfolge"
                required
                step="10"
                type="number"
                value={formState.reihenfolge}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm" htmlFor="beschreibung">
              Beschreibung *
            </Label>
            <Textarea
              className="min-h-[100px] border-gray-700 bg-gray-800/60"
              disabled={isPredefinedEditing}
              id="beschreibung"
              onChange={handleInputChange}
              placeholder="Beschreibung der Kategorie"
              required
              value={formState.beschreibung}
            />
          </div>

          <div className="space-y-2 border-gray-700/30 border-t pt-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-300 text-sm">Felder</h3>
              <Button
                className="gap-1"
                disabled={isPredefinedEditing}
                onClick={addField}
                size="sm"
                type="button"
                variant="outline"
              >
                <PlusCircle className="h-3.5 w-3.5" /> Feld hinzufügen
              </Button>
            </div>

            {formState.felder.map((feld, index) => (
              <div
                className="space-y-3 rounded-md border border-gray-700/50 bg-gray-800/30 p-3"
                key={feld.feldId}
              >
                <div className="grid grid-cols-1 gap-3 md:grid-cols-4">
                  <div className="space-y-1">
                    <Label className="text-xs" htmlFor={`feld-name-${index}`}>
                      Feldname *
                    </Label>
                    <Input
                      className="h-9 border-gray-700 bg-gray-800/60"
                      disabled={isPredefinedEditing}
                      id={`feld-name-${index}`}
                      onChange={(e) =>
                        handleFieldInputChange(index, 'name', e.target.value)
                      }
                      placeholder="Feldname"
                      required
                      value={feld.name}
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs" htmlFor={`feld-typ-${index}`}>
                      Feldtyp *
                    </Label>
                    <Select
                      disabled={isPredefinedEditing}
                      onValueChange={(value) =>
                        handleFieldInputChange(index, 'typ', value)
                      }
                      value={feld.typ}
                    >
                      <SelectTrigger
                        className="h-9 border-gray-700 bg-gray-800/60"
                        id={`feld-typ-${index}`}
                      >
                        <SelectValue placeholder="Feldtyp auswählen" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={FIELD_TYPES.TEXT}>Text</SelectItem>
                        <SelectItem value={FIELD_TYPES.TEXTAREA}>
                          Textbereich
                        </SelectItem>
                        <SelectItem value={FIELD_TYPES.NUMBER}>Zahl</SelectItem>
                        <SelectItem value={FIELD_TYPES.PASSWORD}>
                          Passwort
                        </SelectItem>
                        <SelectItem value={FIELD_TYPES.URL}>URL</SelectItem>
                        <SelectItem value={FIELD_TYPES.EMAIL}>
                          E-Mail
                        </SelectItem>
                        <SelectItem value={FIELD_TYPES.DATE}>Datum</SelectItem>
                        <SelectItem value={FIELD_TYPES.CHECKBOX}>
                          Checkbox
                        </SelectItem>
                        <SelectItem value={FIELD_TYPES.SELECT}>
                          Auswahl (Select)
                        </SelectItem>
                        <SelectItem value={FIELD_TYPES.PHONE}>
                          Telefon
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-1">
                    <Label
                      className="text-xs"
                      htmlFor={`feld-placeholder-${index}`}
                    >
                      Platzhaltertext
                    </Label>
                    <Input
                      className="h-9 border-gray-700 bg-gray-800/60"
                      disabled={isPredefinedEditing}
                      id={`feld-placeholder-${index}`}
                      onChange={(e) =>
                        handleFieldInputChange(
                          index,
                          'placeholder',
                          e.target.value
                        )
                      }
                      placeholder="Optionaler Platzhalter"
                      value={feld.placeholder}
                    />
                  </div>
                  <div className="flex items-end gap-3">
                    <div className="flex h-9 items-center space-x-2">
                      <Checkbox
                        checked={feld.istErforderlich}
                        disabled={isPredefinedEditing}
                        id={`feld-required-${index}`}
                        onCheckedChange={(checked) =>
                          handleFieldInputChange(
                            index,
                            'istErforderlich',
                            !!checked
                          )
                        }
                      />
                      <Label
                        className="text-xs"
                        htmlFor={`feld-required-${index}`}
                      >
                        Erforderlich
                      </Label>
                    </div>
                    {!isPredefinedEditing && formState.felder.length > 1 && (
                      <Button
                        className="h-9 w-9 text-red-400 hover:bg-red-500/10 hover:text-red-500"
                        onClick={() => removeField(index)}
                        size="icon"
                        type="button"
                        variant="ghost"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>

                {feld.typ === FIELD_TYPES.PASSWORD && (
                  <div className="relative">
                    <Label
                      className="text-xs"
                      htmlFor={`feld-value-pw-${index}`}
                    >
                      Passwortwert
                    </Label>
                    <Input
                      className="border-gray-700 bg-gray-800/60 pr-10"
                      disabled={isPredefinedEditing}
                      id={`feld-value-pw-${index}`}
                      onChange={(e) =>
                        handleFieldInputChange(
                          index,
                          'optionen',
                          e.target.value
                        )
                      }
                      placeholder="Passwort eingeben..."
                      required={feld.istErforderlich}
                      type={
                        passwordVisibility[feld.feldId.toString()]
                          ? 'text'
                          : 'password'
                      }
                      value={feld.optionen}
                    />
                    <Button
                      className="absolute top-1 right-1 h-8 w-8 text-gray-400"
                      disabled={isPredefinedEditing}
                      onClick={() => togglePasswordVisibility(feld.feldId)}
                      size="icon"
                      type="button"
                      variant="ghost"
                    >
                      {passwordVisibility[feld.feldId.toString()] ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                )}

                {feld.typ === FIELD_TYPES.SELECT && (
                  <div className="space-y-1">
                    <Label
                      className="text-xs"
                      htmlFor={`feld-options-${index}`}
                    >
                      Optionen * (durch Komma getrennt)
                    </Label>
                    <Input
                      className="h-9 border-gray-700 bg-gray-800/60"
                      disabled={isPredefinedEditing}
                      id={`feld-options-${index}`}
                      onChange={(e) =>
                        handleFieldInputChange(
                          index,
                          'optionen',
                          e.target.value
                        )
                      }
                      placeholder="Option 1, Option 2, Option 3"
                      required
                      value={feld.optionen}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="flex gap-2 border-gray-700/30 border-t pt-4">
            <Button
              className="gap-1"
              disabled={isPredefinedEditing}
              size="sm"
              type="submit"
            >
              <Check className="h-4 w-4" />
              {editingKategorie ? 'Aktualisieren' : 'Speichern'}
            </Button>
            <Button
              className="gap-1"
              onClick={onCancel}
              size="sm"
              type="button"
              variant="outline"
            >
              <X className="h-4 w-4" /> Abbrechen
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
