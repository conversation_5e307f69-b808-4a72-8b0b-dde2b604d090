import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MessageSquareText,
  <PERSON>rk<PERSON>,
} from 'lucide-react';
import type { Id } from '@/../convex/_generated/dataModel';
import { FIELD_TYPES } from '@/../convex/schema';
import { Badge } from '@/components/_shared/Badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import { cn } from '@/lib/utils/cn';

const fieldTypeColors: Record<string, string> = {
  [FIELD_TYPES.TEXT]: 'bg-sky-600/70 text-sky-100 border-sky-500',
  [FIELD_TYPES.TEXTAREA]: 'bg-teal-600/70 text-teal-100 border-teal-500',
  [FIELD_TYPES.NUMBER]: 'bg-amber-600/70 text-amber-100 border-amber-500',
  [FIELD_TYPES.PASSWORD]: 'bg-rose-600/70 text-rose-100 border-rose-500',
  [FIELD_TYPES.URL]: 'bg-indigo-600/70 text-indigo-100 border-indigo-500',
  [FIELD_TYPES.EMAIL]: 'bg-purple-600/70 text-purple-100 border-purple-500',
  [FIELD_TYPES.DATE]: 'bg-lime-600/70 text-lime-100 border-lime-500',
  [FIELD_TYPES.CHECKBOX]: 'bg-pink-600/70 text-pink-100 border-pink-500',
  [FIELD_TYPES.SELECT]: 'bg-cyan-600/70 text-cyan-100 border-cyan-500',
  [FIELD_TYPES.PHONE]: 'bg-orange-600/70 text-orange-100 border-orange-500',
  default: 'bg-gray-600/70 text-gray-100 border-gray-500',
};

interface FeldPropBadgeProps {
  condition?: boolean;
  IconComponent: React.ElementType;
  tooltip: string;
  className: string;
  char?: string;
}

const FeldPropBadge: React.FC<FeldPropBadgeProps> = ({
  condition,
  IconComponent,
  tooltip,
  className,
  char,
}) => {
  if (!condition) {
    return null;
  }
  return (
    <Badge
      className={cn('border px-1.5 py-0.5 font-normal text-xs', className)}
      title={tooltip}
      variant="outline"
    >
      <IconComponent className="mr-0.5 h-3 w-3" /> {char}
    </Badge>
  );
};

interface DokuKategorienTableProps {
  kategorien: {
    _id: Id<'system_doku_kategorien'>;
    name: string;
    beschreibung: string;
    felder: {
      feldId: number;
      name: string;
      typ: string;
      istErforderlich: boolean;
      optionen?: string[];
      placeholder?: string;
      istKopierbar?: boolean;
      istVersteckt?: boolean;
      istExtrafeld?: boolean;
    }[];
    kategorieID?: number;
    reihenfolge: number;
  }[];
}

export function DokuKategorienTable({ kategorien }: DokuKategorienTableProps) {
  return (
    <div className="overflow-x-auto rounded-lg border border-gray-700/80 shadow-sm">
      <Table>
        <TableHeader>
          <TableRow className="border-b-gray-700/80 bg-gray-800/50 hover:bg-gray-800/50">
            <TableHead className="w-[35%] font-semibold text-gray-200">
              Kategorie Details
            </TableHead>
            <TableHead className="w-[65%] font-semibold text-gray-200">
              Felder
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {kategorien.length === 0 ? (
            <TableRow className="border-gray-700/80">
              <TableCell className="h-24 text-center text-gray-400" colSpan={2}>
                Keine Kategorien vorhanden
              </TableCell>
            </TableRow>
          ) : (
            kategorien.map((kategorie) => (
              <TableRow
                className="border-gray-700/80 hover:bg-gray-800/30"
                key={kategorie._id}
              >
                <TableCell className="py-3 align-top">
                  <div className="flex flex-col">
                    <span className="mb-1 font-semibold text-base text-gray-100">
                      {kategorie.name}
                    </span>
                    <p className="mb-1.5 text-gray-400 text-xs leading-relaxed">
                      {kategorie.beschreibung}
                    </p>
                    <span className="block text-gray-500 text-xs">
                      Reihenfolge: {kategorie.reihenfolge}
                    </span>
                    {kategorie.kategorieID !== undefined && (
                      <span className="block text-gray-500 text-xs">
                        ID: {kategorie.kategorieID}
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell className="py-3 align-top">
                  {kategorie.felder.length === 0 ? (
                    <span className="text-gray-500 text-xs">
                      Keine Felder definiert.
                    </span>
                  ) : (
                    <div className="space-y-1.5">
                      {kategorie.felder.map((feld) => (
                        <div
                          className="group flex flex-wrap items-center gap-1.5"
                          key={feld.feldId || feld.name}
                        >
                          <span
                            className={cn(
                              'whitespace-nowrap rounded-md border px-1.5 py-0.5 font-medium text-gray-200 text-sm',
                              fieldTypeColors[
                                feld.typ as keyof typeof FIELD_TYPES
                              ] || fieldTypeColors.default
                            )}
                          >
                            {feld.name}
                          </span>
                          <FeldPropBadge
                            char="R"
                            className="border-red-500/40 bg-red-500/20 text-red-300"
                            condition={feld.istErforderlich}
                            IconComponent={AlertTriangle}
                            tooltip="Erforderlich"
                          />
                          <FeldPropBadge
                            char="K"
                            className="border-blue-500/40 bg-blue-500/20 text-blue-300"
                            condition={feld.istKopierbar}
                            IconComponent={Copy}
                            tooltip="Kopierbar"
                          />
                          <FeldPropBadge
                            char="V"
                            className="border-purple-500/40 bg-purple-500/20 text-purple-300"
                            condition={feld.istVersteckt}
                            IconComponent={EyeOff}
                            tooltip="Versteckt"
                          />
                          <FeldPropBadge
                            char="E"
                            className="border-green-500/40 bg-green-500/20 text-green-300"
                            condition={feld.istExtrafeld}
                            IconComponent={Sparkles}
                            tooltip="Extrafeld"
                          />
                          <FeldPropBadge
                            char="O"
                            className="border-indigo-500/40 bg-indigo-500/20 text-indigo-300"
                            condition={
                              !!feld.optionen && feld.optionen.length > 0
                            }
                            IconComponent={ListChecks}
                            tooltip={`Optionen: ${feld.optionen?.join(', ')}`}
                          />
                          <FeldPropBadge
                            char="P"
                            className="border-yellow-500/40 bg-yellow-500/20 text-yellow-300"
                            condition={!!feld.placeholder}
                            IconComponent={MessageSquareText}
                            tooltip={`Placeholder: ${feld.placeholder}`}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
