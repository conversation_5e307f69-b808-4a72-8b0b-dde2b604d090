import { TrendingUp } from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/_shared/Card';
import { Skeleton } from '@/components/_shared/Skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/_shared/Table';
import { formatCurrency, formatHours } from '@/lib/utils/formatUtils';

interface PerformanceData {
  stunden: number;
  umsatz: number;
  anzahlLeistungen: number;
  anzahlAnfahrten: number;
}

interface CompactPerformanceCardProps {
  tagesLeistung: PerformanceData | null;
  monatsLeistung: PerformanceData | null;
  isLoading: boolean;
}

export function CompactPerformanceCard({
  tagesLeistung,
  monatsLeistung,
  isLoading,
}: CompactPerformanceCardProps) {
  if (isLoading) {
    return (
      <Card className="overflow-hidden border-0 bg-gray-800/40 shadow-md">
        <CardHeader className="border-gray-700/50 border-b p-3">
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent className="p-0">
          <div className="p-4">
            {[...new Array(2)].map((_, i) => (
              <div className="flex justify-between py-2" key={i}>
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-5 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden border-0 bg-gray-800/40 shadow-md">
      <CardHeader className="border-gray-700/50 border-b p-3">
        <CardTitle className="flex items-center font-medium text-sm">
          <TrendingUp className="mr-1.5 h-4 w-4" />
          Leistungsübersicht
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow className="border-gray-700/50 border-b hover:bg-transparent">
              <TableHead className="w-[40%] px-3 py-3 font-medium text-gray-400 text-xs">
                Zeitraum
              </TableHead>
              <TableHead className="px-3 py-3 text-right font-medium text-gray-400 text-xs">
                Stunden
              </TableHead>
              <TableHead className="px-3 py-3 text-right font-medium text-gray-400 text-xs">
                Umsatz
              </TableHead>
              <TableHead className="px-3 py-3 text-right font-medium text-gray-400 text-xs">
                Leistungen
              </TableHead>
              <TableHead className="px-3 py-3 text-right font-medium text-gray-400 text-xs">
                Anfahrten
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow className="border-gray-700/50 border-b hover:bg-gray-700/30">
              <TableCell className="px-3 py-2 font-medium text-xs">
                Heute
              </TableCell>
              {tagesLeistung && tagesLeistung.anzahlLeistungen > 0 ? (
                <>
                  <TableCell className="px-3 py-2 text-right text-xs">
                    {formatHours(tagesLeistung.stunden)}
                  </TableCell>
                  <TableCell className="px-3 py-2 text-right text-xs">
                    {formatCurrency(tagesLeistung.umsatz)}
                  </TableCell>
                  <TableCell className="px-3 py-2 text-right text-xs">
                    {tagesLeistung.anzahlLeistungen}
                  </TableCell>
                  <TableCell className="px-3 py-2 text-right text-xs">
                    {tagesLeistung.anzahlAnfahrten}
                  </TableCell>
                </>
              ) : (
                <TableCell
                  className="px-3 py-2 text-right text-gray-400 text-xs italic"
                  colSpan={4}
                >
                  Für den ausgewählten Zeitraum wurden keine Daten gefunden.
                </TableCell>
              )}
            </TableRow>
            <TableRow className="border-gray-700/50 border-b last:border-b-0 hover:bg-gray-700/30">
              <TableCell className="px-3 py-2 font-medium text-xs">
                Monat
              </TableCell>
              {monatsLeistung && monatsLeistung.anzahlLeistungen > 0 ? (
                <>
                  <TableCell className="px-3 py-2 text-right text-xs">
                    {formatHours(monatsLeistung.stunden)}
                  </TableCell>
                  <TableCell className="px-3 py-2 text-right text-xs">
                    {formatCurrency(monatsLeistung.umsatz)}
                  </TableCell>
                  <TableCell className="px-3 py-2 text-right text-xs">
                    {monatsLeistung.anzahlLeistungen}
                  </TableCell>
                  <TableCell className="px-3 py-2 text-right text-xs">
                    {monatsLeistung.anzahlAnfahrten}
                  </TableCell>
                </>
              ) : (
                <TableCell
                  className="px-3 py-2 text-right text-gray-400 text-xs italic"
                  colSpan={4}
                >
                  Für den ausgewählten Zeitraum wurden keine Daten gefunden.
                </TableCell>
              )}
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
