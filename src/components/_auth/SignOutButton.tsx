import { useClerk } from '@clerk/clerk-react';
import { LogOut } from 'lucide-react';

interface SignOutButtonProps {
  className?: string;
}

export function SignOutButton({ className }: SignOutButtonProps) {
  const { signOut } = useClerk();

  const handleSignOut = () => {
    // Redirect to sign-in page after sign out
    void signOut({ redirectUrl: '/signin' }).catch(() => {
      // Sign out error handled by Clerk
    });
  };

  return (
    <button className={className} onClick={handleSignOut} type="button">
      <LogOut className="mr-2 h-4 w-4" />
      Abmelden
    </button>
  );
}
