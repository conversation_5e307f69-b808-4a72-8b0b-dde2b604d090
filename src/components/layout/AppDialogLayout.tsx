import type { ReactNode } from 'react';
import { Button } from '@/components/_shared/Button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/_shared/Dialog';

interface AppDialogLayoutProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  icon?: ReactNode;
  children: ReactNode;
  footer?: ReactNode;
  footerAction?: {
    label: string;
    onClick: () => void;
    icon?: ReactNode;
    disabled?: boolean;
    loading?: boolean; // Added from LeistungDialogLayout
  };
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl';
}

export function AppDialogLayout({
  isOpen,
  onClose,
  title,
  description,
  icon,
  children,
  footer,
  footerAction,
  maxWidth = 'md',
}: AppDialogLayoutProps) {
  const maxWidthClasses = {
    sm: 'w-[95vw] max-w-[95vw] sm:max-w-[500px]',
    md: 'w-[95vw] max-w-[95vw] sm:max-w-[600px]',
    lg: 'w-[95vw] max-w-[95vw] sm:max-w-[700px]',
    xl: 'w-[95vw] max-w-[95vw] sm:max-w-[800px]',
    '2xl': 'w-[95vw] max-w-[95vw] sm:max-w-[900px]',
    '3xl': 'w-[95vw] max-w-[95vw] sm:max-w-[1000px]',
    '4xl': 'w-[95vw] max-w-[95vw] sm:max-w-[1200px]',
    '5xl': 'w-[95vw] max-w-[95vw] sm:max-w-[1400px]',
  };

  return (
    <Dialog onOpenChange={(open) => !open && onClose()} open={isOpen}>
      <DialogContent
        className={`${maxWidthClasses[maxWidth]} flex max-h-[95vh] flex-col overflow-hidden p-0`}
      >
        <DialogHeader className="flex-shrink-0 p-4 pb-1">
          <DialogTitle className="flex items-center gap-1.5 text-base">
            {icon}
            {title}
          </DialogTitle>
          {description && (
            <DialogDescription className="text-xs">
              {description}
            </DialogDescription>
          )}
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-4 py-2">{children}</div>

        <div className="flex flex-shrink-0 items-center justify-between gap-2 border-gray-800 border-t bg-gray-900 p-3">
          {footer ? (
            footer
          ) : (
            <>
              <div />
              <div className="flex gap-2">
                <Button
                  className="h-8 text-xs"
                  onClick={onClose}
                  size="sm"
                  variant="outline"
                >
                  Abbrechen
                </Button>
                {footerAction && (
                  <Button
                    className="h-8 gap-1 text-xs"
                    disabled={footerAction.disabled || footerAction.loading}
                    onClick={footerAction.onClick}
                    size="sm"
                  >
                    {footerAction.loading ? (
                      'Wird verarbeitet...'
                    ) : (
                      <>
                        {footerAction.icon}
                        {footerAction.label}
                      </>
                    )}
                  </Button>
                )}
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
