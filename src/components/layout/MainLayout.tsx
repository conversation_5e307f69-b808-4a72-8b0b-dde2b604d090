import type React from 'react'; // Import React for type React.PropsWithChildren
import { Outlet } from 'react-router-dom';
import { Navigation } from '../navigation/Navigation';

export function MainLayout({ children }: React.PropsWithChildren) {
  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-b from-gray-900 to-gray-950 text-gray-100">
      <Navigation />
      <main className="container mx-auto max-w-7xl flex-grow px-5 py-6">
        {children ? children : <Outlet />}{' '}
        {/* Render children if passed, otherwise Outlet for nested routes */}
      </main>
    </div>
  );
}

export default MainLayout;
