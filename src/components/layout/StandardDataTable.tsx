import type { ReactNode } from 'react';
import { Card, CardHeader, CardTitle } from '@/components/_shared/Card';

interface StandardDataTableProps {
  title: string;
  infoSlot?: ReactNode;
  filterSlot: ReactNode;
  children: ReactNode;
}

/**
 * A standardized layout for data tables in the application.
 * Provides consistent styling for tables with header, filters and content.
 */
export function StandardDataTable({
  title,
  infoSlot,
  filterSlot,
  children,
}: StandardDataTableProps) {
  return (
    <Card className="overflow-hidden border-0 shadow-lg">
      <CardHeader className="border-gray-700/50 border-b px-5 pb-3">
        <div className="flex flex-col gap-4">
          <div className="flex flex-wrap items-center justify-between">
            <CardTitle className="font-medium text-lg">{title}</CardTitle>
            {infoSlot && (
              <div className="flex items-center gap-2 rounded-full bg-gray-800/70 px-3 py-1 text-gray-400 text-xs">
                {infoSlot}
              </div>
            )}
          </div>
          {filterSlot}
        </div>
      </CardHeader>
      {children}
    </Card>
  );
}
