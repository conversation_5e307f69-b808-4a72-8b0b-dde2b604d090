import type React from 'react';
import type { ReactNode } from 'react';

interface EmptyStateProps {
  icon?: ReactNode;
  title: string;
  message?: string;
  actions?: ReactNode;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  message,
  actions,
}) => {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      {icon && <div className="mb-4 text-4xl text-gray-400">{icon}</div>}
      <h2 className="mb-2 font-semibold text-gray-700 text-xl">{title}</h2>
      {message && <p className="mb-4 text-gray-500">{message}</p>}
      {actions && <div className="mt-2">{actions}</div>}
    </div>
  );
};
