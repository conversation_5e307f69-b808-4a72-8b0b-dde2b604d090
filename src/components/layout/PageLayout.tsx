import type { ReactNode } from 'react';
import React from 'react'; // Import React for forwardRef

interface PageLayoutProps {
  children: ReactNode;
  title: string;
  subtitle?: string;
  action?: ReactNode;
}

// Wrap PageLayout with React.forwardRef
export const PageLayout = React.forwardRef<
  HTMLDivElement, // Type of the element the ref will point to
  PageLayoutProps
>(({ children, title, subtitle, action }, ref) => {
  return (
    // Attach the ref to the main div of the PageLayout
    <div className="space-y-6" ref={ref}>
      <div className="mb-2 flex items-center justify-between border-gray-800 border-b pb-4">
        <div>
          <h1 className="font-semibold text-2xl tracking-tight">{title}</h1>
          {subtitle && <p className="mt-1 text-gray-400 text-sm">{subtitle}</p>}
        </div>
        {action && <div>{action}</div>}
      </div>
      {children}
    </div>
  );
});

PageLayout.displayName = 'PageLayout'; // Optional: for better debugging names

export default PageLayout;
