import { RotateCcw, Search, X } from 'lucide-react';
import React from 'react';
import { Button } from '@/components/_shared/Button';
import { Input } from '@/components/_shared/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/_shared/select';
import { cn } from '@/lib/utils/cn';

export type FilterOption = {
  value: string;
  label: string;
  disabled?: boolean;
};

export type SearchFilterConfig = {
  type: 'search';
  id: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
  inputClassName?: string;
};

export type SelectFilterConfig = {
  type: 'select';
  id: string;
  label?: string;
  placeholder?: string;
  value: string;
  options: FilterOption[];
  onChange: (value: string) => void;
  triggerClassName?: string;
  contentClassName?: string;
  itemClassName?: string;
  minWidth?: string;
  disabled?: boolean;
};

export type DateRangeFilterConfig = {
  type: 'dateRange';
  idPrefix: string;
  zeitraumValue: string;
  startDateValue: string;
  endDateValue: string;
  onZeitraumChange: (value: string) => void;
  onStartDateChange: (value: string) => void;
  onEndDateChange: (value: string) => void;
  zeitraumOptions: FilterOption[];
  customDateInputClassName?: string;
  selectTriggerClassName?: string;
  minWidth?: string;
  disabled?: boolean;
};

export type CustomFilterConfig = {
  type: 'custom';
  id: string;
  node: React.ReactNode;
};

export type FilterConfig =
  | SearchFilterConfig
  | SelectFilterConfig
  | DateRangeFilterConfig
  | CustomFilterConfig;

export interface GenericFilterControlsProps {
  filters: FilterConfig[];
  onResetAll?: () => void;
  resetButtonText?: string;
  className?: string;
  globalFilterContainerClassName?: string;
}

export function GenericFilterControls({
  filters,
  onResetAll,
  resetButtonText = 'Zurücksetzen',
  className = 'flex flex-wrap items-center gap-2',
  globalFilterContainerClassName,
}: GenericFilterControlsProps) {
  return (
    <div className={cn(globalFilterContainerClassName)}>
      <div className={cn(className)}>
        {filters.map((filter) => {
          if (filter.type === 'search') {
            return (
              <div
                className={cn('relative flex-grow', filter.className)}
                key={filter.id}
              >
                <Search className="-translate-y-1/2 absolute top-1/2 left-2.5 h-3.5 w-3.5 transform text-gray-400" />
                <Input
                  className={cn(
                    'h-8 w-full border-gray-700 bg-gray-800 pl-8 text-xs',
                    filter.inputClassName
                  )}
                  id={filter.id}
                  onChange={(e) => filter.onChange(e.target.value)}
                  placeholder={filter.placeholder || 'Suchen...'}
                  value={filter.value}
                />
                {filter.value && (
                  <Button
                    aria-label="Suche zurücksetzen"
                    className="-translate-y-1/2 absolute top-1/2 right-1 h-6 w-6 transform text-gray-400 hover:text-white"
                    onClick={() => filter.onChange('')}
                    size="icon"
                    variant="ghost"
                  >
                    <X className="h-3.5 w-3.5" />
                  </Button>
                )}
              </div>
            );
          }
          if (filter.type === 'select') {
            return (
              <div className="flex-shrink-0" key={filter.id}>
                {filter.label && (
                  <label className="sr-only" htmlFor={filter.id}>
                    {filter.label}
                  </label>
                )}
                <Select
                  disabled={filter.disabled}
                  onValueChange={filter.onChange}
                  value={filter.value}
                >
                  <SelectTrigger
                    aria-label={filter.label || filter.placeholder}
                    className={cn(
                      'h-8 border-gray-700 bg-gray-800 text-xs',
                      filter.triggerClassName
                    )}
                    id={filter.id}
                    style={{ minWidth: filter.minWidth }}
                  >
                    <SelectValue placeholder={filter.placeholder} />
                  </SelectTrigger>
                  <SelectContent className={filter.contentClassName}>
                    {filter.options.map((option) => (
                      <SelectItem
                        className={filter.itemClassName}
                        disabled={option.disabled}
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            );
          }
          if (filter.type === 'dateRange') {
            return (
              <React.Fragment key={filter.idPrefix}>
                <div className="flex-shrink-0">
                  <Select
                    disabled={filter.disabled}
                    onValueChange={filter.onZeitraumChange}
                    value={filter.zeitraumValue}
                  >
                    <SelectTrigger
                      aria-label="Zeitraum auswählen"
                      className={cn(
                        'h-8 border-gray-700 bg-gray-800 text-xs',
                        filter.selectTriggerClassName
                      )}
                      style={{ minWidth: filter.minWidth }}
                    >
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {filter.zeitraumOptions.map((option) => (
                        <SelectItem
                          disabled={option.disabled}
                          key={option.value}
                          value={option.value}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                {filter.zeitraumValue === 'custom' && !filter.disabled && (
                  <div className="flex flex-shrink-0 items-center gap-1">
                    <Input
                      aria-label="Startdatum"
                      className={cn(
                        'h-8 w-auto border-gray-700 bg-gray-800 text-xs',
                        filter.customDateInputClassName
                      )}
                      onChange={(e) => filter.onStartDateChange(e.target.value)}
                      title="Startdatum"
                      type="date"
                      value={filter.startDateValue}
                    />
                    <span className="text-gray-400 text-xs">-</span>
                    <Input
                      aria-label="Enddatum"
                      className={cn(
                        'h-8 w-auto border-gray-700 bg-gray-800 text-xs',
                        filter.customDateInputClassName
                      )}
                      onChange={(e) => filter.onEndDateChange(e.target.value)}
                      title="Enddatum"
                      type="date"
                      value={filter.endDateValue}
                    />
                  </div>
                )}
              </React.Fragment>
            );
          }
          if (filter.type === 'custom') {
            return (
              <React.Fragment key={filter.id}>{filter.node}</React.Fragment>
            );
          }
          return null;
        })}
        {onResetAll && (
          <div className="flex-shrink-0">
            <Button
              aria-label="Alle Filter zurücksetzen"
              className="h-8 border-gray-700 bg-gray-800 text-xs hover:bg-gray-700"
              onClick={onResetAll}
              size="sm"
              variant="outline"
            >
              <RotateCcw className="mr-1.5 h-3.5 w-3.5" />
              {resetButtonText}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
