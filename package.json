{"name": "8it-verwal<PERSON>g", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node ./scripts/load-env.js .env.local 'vite'", "build": "node ./scripts/load-env.js .env.production 'tsc && vite build'", "lint": "biome lint ./src ./convex", "format": "biome format --write ./src ./convex", "check": "biome check --apply ./src ./convex", "start": "node ./scripts/load-env.js .env.production 'vite preview'"}, "dependencies": {"@clerk/clerk-react": "^5.30.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf/renderer": "^4.3.0", "@types/qrcode.react": "^1.0.5", "clsx": "^2.1.1", "convex": "^1.24.0", "date-fns": "^4.1.0", "lucide-react": "^0.503.0", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-router-dom": "^7.5.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "vcf": "^2.1.2", "zod": "^3.25.76"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@tailwindcss/line-clamp": "^0.4.4", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/vcf": "^2.0.7", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "~10", "dotenv": "^16.4.7", "lint-staged": "^16.1.2", "postcss": "~8", "tailwindcss": "~3", "tailwindcss-animate": "^1.0.7", "typescript": "~5.7.2", "ultracite": "5.0.35", "vite": "^6.2.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,jsonc,css,scss,md,mdx}": ["npx ultracite format", "npx ultracite format"]}}