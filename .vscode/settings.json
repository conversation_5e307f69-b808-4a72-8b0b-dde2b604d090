{"editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript][typescript][javascriptreact][typescriptreact][json][jsonc][css][graphql]": {"editor.defaultFormatter": "biomejs.biome"}, "typescript.tsdk": "node_modules/typescript/lib", "editor.formatOnSave": true, "editor.formatOnPaste": true, "emmet.showExpandedAbbreviation": "never", "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}}