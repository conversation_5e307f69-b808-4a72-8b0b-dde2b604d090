#!/usr/bin/env node

import { spawn } from 'node:child_process';
import { readFileSync } from 'node:fs';
import { join } from 'node:path';

// Get the environment file path from command line argument
const envFile = process.argv[2];
if (!envFile) {
  console.error('Error: Environment file path is required');
  console.error('Usage: node load-env.js <env-file> <command>');
  process.exit(1);
}

const command = process.argv.slice(3).join(' ');
if (!command) {
  console.error('Error: Command is required');
  console.error('Usage: node load-env.js <env-file> <command>');
  process.exit(1);
}

try {
  // Read the env file
  const envPath = join(process.cwd(), envFile);
  const envContent = readFileSync(envPath, 'utf8');

  // Parse env file and set environment variables
  envContent.split('\n').forEach((line) => {
    line = line.trim();
    if (line && !line.startsWith('#') && line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=');
      process.env[key.trim()] = value.trim();
    }
  });

  console.log(`Loading environment from: ${envFile}`);
  console.log(`Executing: ${command}`);

  // Execute the command
  const child = spawn('sh', ['-c', command], {
    stdio: 'inherit',
    env: process.env,
  });

  child.on('exit', (code) => {
    process.exit(code || 0);
  });
} catch (error) {
  console.error(
    'Error loading environment or executing command:',
    error.message
  );
  process.exit(1);
}
