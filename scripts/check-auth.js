#!/usr/bin/env node

/**
 * <PERSON>ript to scan Convex functions for missing authentication checks.
 *
 * Usage:
 * node scripts/check-auth.js
 *
 * This script scans all Convex function files (query, mutation, action) and checks
 * if they include authentication checks. It reports any functions that might be
 * missing proper authentication.
 */

import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONVEX_DIR = path.join(__dirname, '..', 'convex');
const IGNORE_DIRS = ['_generated', 'node_modules'];
const IGNORE_FILES = ['schema.ts', 'auth.config.ts'];
const INTERNAL_FUNCTION_TYPES = [
  'internalQuery',
  'internalMutation',
  'internalAction',
];
const PUBLIC_FUNCTION_TYPES = ['query', 'mutation', 'action'];

// Authentication check patterns
const AUTH_CHECK_PATTERNS = [
  'const identity = await ctx.auth.getUserIdentity()',
  'await ctx.auth.getUserIdentity()',
];

// ANSI color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

/**
 * Recursively get all files in a directory
 */
async function getFiles(dir) {
  const subdirs = await fs.readdir(dir);
  const files = await Promise.all(
    subdirs.map(async (subdir) => {
      const res = path.resolve(dir, subdir);
      if (IGNORE_DIRS.includes(subdir)) {
        return [];
      }

      try {
        const stats = await fs.stat(res);
        if (stats.isDirectory()) {
          return getFiles(res);
        }
        if (
          stats.isFile() &&
          !IGNORE_FILES.includes(subdir) &&
          (subdir.endsWith('.ts') || subdir.endsWith('.js'))
        ) {
          return res;
        }
        return [];
      } catch {
        return [];
      }
    })
  );
  return files.flat();
}

/**
 * Check if a function has authentication checks
 */
function hasAuthCheck(functionContent) {
  return AUTH_CHECK_PATTERNS.some((pattern) =>
    functionContent.includes(pattern)
  );
}

/**
 * Extract functions from a file
 */
function extractFunctions(fileContent) {
  const functions = [];

  // Match all export declarations for Convex functions
  const functionTypes = [
    ...PUBLIC_FUNCTION_TYPES,
    ...INTERNAL_FUNCTION_TYPES,
  ].join('|');
  const functionRegex = new RegExp(
    `export\\s+const\\s+([\\w]+)\\s*=\\s*(${functionTypes})\\s*\\({([\\s\\S]*?)handler:\\s*async\\s*\\([\\s\\S]*?\\)\\s*=>\\s*{([\\s\\S]*?)},`,
    'g'
  );

  let match;
  while ((match = functionRegex.exec(fileContent)) !== null) {
    const [, functionName, functionType, , functionBody] = match;

    functions.push({
      name: functionName,
      type: functionType,
      isInternal: INTERNAL_FUNCTION_TYPES.includes(functionType),
      body: functionBody,
      hasAuthCheck: hasAuthCheck(functionBody),
    });
  }

  return functions;
}

/**
 * Main function
 */
async function main() {
  const files = await getFiles(CONVEX_DIR);
  let totalFunctions = 0;
  let missingAuthFunctions = 0;
  let internalFunctions = 0;

  // Process files sequentially to avoid issues
  for (const file of files) {
    const relativePath = path.relative(process.cwd(), file);
    const content = await fs.readFile(file, 'utf8');
    const functions = extractFunctions(content);

    if (functions.length === 0) {
      continue;
    }

    let hasIssue = false;

    for (const func of functions) {
      totalFunctions++;

      if (func.isInternal) {
        internalFunctions++;
        continue; // Skip internal functions
      }

      if (!func.hasAuthCheck) {
        if (!hasIssue) {
          hasIssue = true;
          console.log(`\n${colors.yellow}📁 ${relativePath}${colors.reset}`);
        }
        console.log(
          `  ${colors.red}❌ ${func.name} (${func.type})${colors.reset} - Missing auth check`
        );
        missingAuthFunctions++;
      }
    }
  }

  console.log(`\n${colors.cyan}🔍 Authentication Check Summary${colors.reset}`);
  console.log(
    `${colors.blue}📊 Total functions checked: ${totalFunctions - internalFunctions}${colors.reset}`
  );
  console.log(
    `${colors.yellow}🔒 Internal functions (skipped): ${internalFunctions}${colors.reset}`
  );

  if (missingAuthFunctions > 0) {
    console.log(
      `${colors.red}⚠️  Functions missing auth checks: ${missingAuthFunctions}${colors.reset}`
    );
    console.log(
      `\n${colors.yellow}💡 Add authentication checks with:${colors.reset}`
    );
    console.log('   const identity = await ctx.auth.getUserIdentity();');
    console.log('   if (!identity) throw new Error("Unauthorized");');
    process.exit(1);
  } else {
    console.log(
      `${colors.green}✅ All functions have proper authentication checks!${colors.reset}`
    );
  }
}

main().catch(console.error);
