import { v } from 'convex/values';
import { internalMutation, mutation, query } from '../_generated/server';

// Helper function to calculate next repetition date
function calculateNextRepetition(
  interval: string,
  uhrzeit?: string,
  wochentag?: string,
  monatlicheWiederholung?: string
): string {
  const now = new Date();
  let nextDate = new Date();

  switch (interval) {
    case 'taeglich':
      // Nächster Tag zur gleichen Uhrzeit
      nextDate.setDate(now.getDate() + 1);
      if (uhrzeit) {
        const [hours, minutes] = uhrzeit.split(':').map(Number);
        nextDate.setHours(hours, minutes, 0, 0);
      }
      break;

    case 'woechentlich':
      // Nächster spezifischer Wochentag
      if (wochentag) {
        const wochentagMap = {
          montag: 1,
          dienstag: 2,
          mittwoch: 3,
          donnerstag: 4,
          freitag: 5,
          samstag: 6,
          sonntag: 0,
        };
        const targetDay = wochentagMap[wochentag as keyof typeof wochentagMap];
        const currentDay = now.getDay();
        let daysUntilTarget = targetDay - currentDay;
        if (daysUntilTarget <= 0) {
          daysUntilTarget += 7;
        }

        nextDate.setDate(now.getDate() + daysUntilTarget);
        if (uhrzeit) {
          const [hours, minutes] = uhrzeit.split(':').map(Number);
          nextDate.setHours(hours, minutes, 0, 0);
        }
      }
      break;

    case 'monatlich':
      // Nächster spezifischer Tag im Monat
      if (monatlicheWiederholung) {
        if (monatlicheWiederholung === 'tag_1') {
          nextDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        } else if (monatlicheWiederholung === 'tag_15') {
          nextDate = new Date(now.getFullYear(), now.getMonth() + 1, 15);
        } else if (monatlicheWiederholung === 'letzter_tag') {
          nextDate = new Date(now.getFullYear(), now.getMonth() + 2, 0);
        } else {
          // Erster/Letzter Wochentag im Monat
          nextDate.setMonth(now.getMonth() + 1);
        }
        if (uhrzeit) {
          const [hours, minutes] = uhrzeit.split(':').map(Number);
          nextDate.setHours(hours, minutes, 0, 0);
        }
      }
      break;

    case 'jaehrlich':
      nextDate.setFullYear(now.getFullYear() + 1);
      if (uhrzeit) {
        const [hours, minutes] = uhrzeit.split(':').map(Number);
        nextDate.setHours(hours, minutes, 0, 0);
      }
      break;
  }

  return nextDate.toISOString().split('T')[0];
}

// Define the structure for an appointment
const _terminSchema = v.object({
  _id: v.id('kunden_termine'),
  _creationTime: v.number(),
  kundenId: v.id('kunden'),
  titel: v.string(),
  kategorie: v.string(),
  datum: v.optional(v.string()), // ISO date string
  uhrzeit: v.optional(v.string()), // Time in HH:MM format
  notizen: v.optional(v.string()),
  // Wiederholung
  istWiederholend: v.boolean(),
  wiederholungsIntervall: v.optional(
    v.union(
      v.literal('taeglich'),
      v.literal('woechentlich'),
      v.literal('monatlich'),
      v.literal('jaehrlich')
    )
  ),
  wochentag: v.optional(
    v.union(
      v.literal('montag'),
      v.literal('dienstag'),
      v.literal('mittwoch'),
      v.literal('donnerstag'),
      v.literal('freitag'),
      v.literal('samstag'),
      v.literal('sonntag')
    )
  ),
  monatlicheWiederholung: v.optional(
    v.union(
      v.literal('erster_montag'),
      v.literal('erster_dienstag'),
      v.literal('erster_mittwoch'),
      v.literal('erster_donnerstag'),
      v.literal('erster_freitag'),
      v.literal('letzter_montag'),
      v.literal('letzter_dienstag'),
      v.literal('letzter_mittwoch'),
      v.literal('letzter_donnerstag'),
      v.literal('letzter_freitag'),
      v.literal('tag_1'),
      v.literal('tag_15'),
      v.literal('letzter_tag')
    )
  ),
  wiederholungsEnde: v.optional(v.string()),
  naechsteWiederholung: v.optional(v.string()),
});

// Define the structure for an appointment with customer info
const _terminMitKundeSchema = v.object({
  _id: v.id('kunden_termine'),
  _creationTime: v.number(),
  kundenId: v.id('kunden'),
  titel: v.string(),
  kategorie: v.string(),
  datum: v.optional(v.string()),
  uhrzeit: v.optional(v.string()),
  notizen: v.optional(v.string()),
  istWiederholend: v.boolean(),
  wiederholungsIntervall: v.optional(
    v.union(
      v.literal('taeglich'),
      v.literal('woechentlich'),
      v.literal('monatlich'),
      v.literal('jaehrlich')
    )
  ),
  wochentag: v.optional(
    v.union(
      v.literal('montag'),
      v.literal('dienstag'),
      v.literal('mittwoch'),
      v.literal('donnerstag'),
      v.literal('freitag'),
      v.literal('samstag'),
      v.literal('sonntag')
    )
  ),
  monatlicheWiederholung: v.optional(
    v.union(
      v.literal('erster_montag'),
      v.literal('erster_dienstag'),
      v.literal('erster_mittwoch'),
      v.literal('erster_donnerstag'),
      v.literal('erster_freitag'),
      v.literal('letzter_montag'),
      v.literal('letzter_dienstag'),
      v.literal('letzter_mittwoch'),
      v.literal('letzter_donnerstag'),
      v.literal('letzter_freitag'),
      v.literal('tag_1'),
      v.literal('tag_15'),
      v.literal('letzter_tag')
    )
  ),
  wiederholungsEnde: v.optional(v.string()),
  naechsteWiederholung: v.optional(v.string()),
  kundeName: v.string(),
});

/**
 * Alle Termine für einen Kunden abrufen
 */
export const getByKunde = query({
  args: {
    kundenId: v.id('kunden'),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    const termine = await ctx.db
      .query('kunden_termine')
      .filter((q) => q.eq(q.field('kundenId'), args.kundenId))
      .order('desc')
      .collect();

    return termine;
  },
});

/**
 * Alle Termine mit Kundeninformationen abrufen
 */
export const listWithKunden = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    const termine = await ctx.db
      .query('kunden_termine')
      .order('desc')
      .collect();
    const kunden = await ctx.db.query('kunden').collect();

    const kundenMap = new Map(kunden.map((k) => [k._id, k.name]));

    return termine.map((termin) => ({
      ...termin,
      kundeName: kundenMap.get(termin.kundenId) || 'Unbekannt',
    }));
  },
});

/**
 * Einen spezifischen Termin abrufen
 */
export const get = query({
  args: {
    id: v.id('kunden_termine'),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    return await ctx.db.get(args.id);
  },
});

/**
 * Neuen Termin erstellen
 */
export const create = mutation({
  args: {
    kundenId: v.id('kunden'),
    titel: v.string(),
    kategorie: v.string(),
    datum: v.optional(v.string()),
    uhrzeit: v.optional(v.string()),
    notizen: v.optional(v.string()),
    istWiederholend: v.boolean(),
    wiederholungsIntervall: v.optional(
      v.union(
        v.literal('taeglich'),
        v.literal('woechentlich'),
        v.literal('monatlich'),
        v.literal('jaehrlich')
      )
    ),
    wochentag: v.optional(
      v.union(
        v.literal('montag'),
        v.literal('dienstag'),
        v.literal('mittwoch'),
        v.literal('donnerstag'),
        v.literal('freitag'),
        v.literal('samstag'),
        v.literal('sonntag')
      )
    ),
    monatlicheWiederholung: v.optional(
      v.union(
        v.literal('erster_montag'),
        v.literal('erster_dienstag'),
        v.literal('erster_mittwoch'),
        v.literal('erster_donnerstag'),
        v.literal('erster_freitag'),
        v.literal('letzter_montag'),
        v.literal('letzter_dienstag'),
        v.literal('letzter_mittwoch'),
        v.literal('letzter_donnerstag'),
        v.literal('letzter_freitag'),
        v.literal('tag_1'),
        v.literal('tag_15'),
        v.literal('letzter_tag')
      )
    ),
    wiederholungsEnde: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    // Berechne naechsteWiederholung falls wiederholend
    let naechsteWiederholung: string | undefined;
    if (args.istWiederholend && args.wiederholungsIntervall) {
      naechsteWiederholung = calculateNextRepetition(
        args.wiederholungsIntervall,
        args.uhrzeit,
        args.wochentag,
        args.monatlicheWiederholung
      );
    }

    return await ctx.db.insert('kunden_termine', {
      ...args,
      naechsteWiederholung,
    });
  },
});

/**
 * Termin aktualisieren
 */
export const update = mutation({
  args: {
    id: v.id('kunden_termine'),
    titel: v.optional(v.string()),
    kategorie: v.optional(v.string()),
    datum: v.optional(v.string()),
    uhrzeit: v.optional(v.string()),
    notizen: v.optional(v.string()),
    istWiederholend: v.optional(v.boolean()),
    wiederholungsIntervall: v.optional(
      v.union(
        v.literal('taeglich'),
        v.literal('woechentlich'),
        v.literal('monatlich'),
        v.literal('jaehrlich')
      )
    ),
    wochentag: v.optional(
      v.union(
        v.literal('montag'),
        v.literal('dienstag'),
        v.literal('mittwoch'),
        v.literal('donnerstag'),
        v.literal('freitag'),
        v.literal('samstag'),
        v.literal('sonntag')
      )
    ),
    monatlicheWiederholung: v.optional(
      v.union(
        v.literal('erster_montag'),
        v.literal('erster_dienstag'),
        v.literal('erster_mittwoch'),
        v.literal('erster_donnerstag'),
        v.literal('erster_freitag'),
        v.literal('letzter_montag'),
        v.literal('letzter_dienstag'),
        v.literal('letzter_mittwoch'),
        v.literal('letzter_donnerstag'),
        v.literal('letzter_freitag'),
        v.literal('tag_1'),
        v.literal('tag_15'),
        v.literal('letzter_tag')
      )
    ),
    wiederholungsEnde: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    const { id, ...updates } = args;

    // Remove undefined values
    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    // Handle naechsteWiederholung calculation or removal
    let additionalUpdates = {};

    // Berechne naechsteWiederholung neu falls sich Wiederholung geändert hat
    if (cleanUpdates.istWiederholend && cleanUpdates.wiederholungsIntervall) {
      additionalUpdates = {
        naechsteWiederholung: calculateNextRepetition(
          cleanUpdates.wiederholungsIntervall as string,
          cleanUpdates.uhrzeit as string | undefined,
          cleanUpdates.wochentag as string | undefined,
          cleanUpdates.monatlicheWiederholung as string | undefined
        ),
      };
    } else if (cleanUpdates.istWiederholend === false) {
      // Entferne naechsteWiederholung wenn Wiederholung deaktiviert wird
      additionalUpdates = { naechsteWiederholung: undefined };
    }

    return await ctx.db.patch(id, { ...cleanUpdates, ...additionalUpdates });
  },
});

/**
 * Termine der nächsten X Tage abrufen
 */
export const getUpcoming = query({
  args: {
    days: v.optional(v.number()), // Default: 30 Tage
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    const days = args.days || 30;
    const today = new Date();
    const endDate = new Date();
    endDate.setDate(today.getDate() + days);

    const todayStr = today.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    // Hole alle Termine (einmalige und wiederholende)
    const alleTermine = await ctx.db.query('kunden_termine').collect();

    const termine = alleTermine
      .filter((termin) => {
        // Einmalige Termine
        if (!termin.istWiederholend && termin.datum) {
          return termin.datum >= todayStr && termin.datum <= endDateStr;
        }

        // Wiederholende Termine - prüfe naechsteWiederholung
        if (termin.istWiederholend && termin.naechsteWiederholung) {
          return (
            termin.naechsteWiederholung >= todayStr &&
            termin.naechsteWiederholung <= endDateStr
          );
        }

        return false;
      })
      .sort((a, b) => {
        // Sortiere nach Datum (einmalige) oder naechsteWiederholung (wiederholende)
        const dateA = a.istWiederholend ? a.naechsteWiederholung : a.datum;
        const dateB = b.istWiederholend ? b.naechsteWiederholung : b.datum;
        return (dateA || '').localeCompare(dateB || '');
      });

    const kunden = await ctx.db.query('kunden').collect();
    const kundenMap = new Map(kunden.map((k) => [k._id, k.name]));

    return termine.map((termin) => ({
      ...termin,
      kundeName: kundenMap.get(termin.kundenId) || 'Unbekannt',
    }));
  },
});

/**
 * Termin löschen
 */
export const delete_ = mutation({
  args: {
    id: v.id('kunden_termine'),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    return await ctx.db.delete(args.id);
  },
});

/**
 * Interne Funktion: Aktualisiert alle wiederholenden Termine
 * Wird täglich per Cron-Job ausgeführt
 */
export const updateRecurringAppointments = internalMutation({
  args: {},
  handler: async (ctx) => {
    const today = new Date().toISOString().split('T')[0];

    // Hole alle wiederholenden Termine
    const recurringTermine = await ctx.db
      .query('kunden_termine')
      .filter((q) => q.eq(q.field('istWiederholend'), true))
      .collect();

    let updatedCount = 0;

    for (const termin of recurringTermine) {
      // Prüfe ob naechsteWiederholung in der Vergangenheit liegt oder heute ist
      if (termin.naechsteWiederholung && termin.naechsteWiederholung <= today) {
        // Berechne nächsten Termin
        if (termin.wiederholungsIntervall) {
          const nextDate = calculateNextRepetition(
            termin.wiederholungsIntervall,
            termin.uhrzeit,
            termin.wochentag,
            termin.monatlicheWiederholung
          );

          // Prüfe ob das neue Datum vor dem Wiederholungsende liegt (falls gesetzt)
          if (
            !termin.wiederholungsEnde ||
            nextDate <= termin.wiederholungsEnde
          ) {
            await ctx.db.patch(termin._id, {
              naechsteWiederholung: nextDate,
            });
            updatedCount++;
          } else {
            // Wiederholung ist beendet - setze istWiederholend auf false
            await ctx.db.patch(termin._id, {
              istWiederholend: false,
              naechsteWiederholung: undefined,
            });
          }
        }
      }
    }
    return { updatedCount, totalRecurring: recurringTermine.length };
  },
});

/**
 * Test-Funktion für Cron-Job (nur für Entwicklung)
 */
export const testUpdateRecurringAppointments = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    const today = new Date().toISOString().split('T')[0];

    // Hole alle wiederholenden Termine
    const recurringTermine = await ctx.db
      .query('kunden_termine')
      .filter((q) => q.eq(q.field('istWiederholend'), true))
      .collect();

    let updatedCount = 0;

    for (const termin of recurringTermine) {
      // Prüfe ob naechsteWiederholung in der Vergangenheit liegt oder heute ist
      if (termin.naechsteWiederholung && termin.naechsteWiederholung <= today) {
        // Berechne nächsten Termin
        if (termin.wiederholungsIntervall) {
          const nextDate = calculateNextRepetition(
            termin.wiederholungsIntervall,
            termin.uhrzeit,
            termin.wochentag,
            termin.monatlicheWiederholung
          );

          // Prüfe ob das neue Datum vor dem Wiederholungsende liegt (falls gesetzt)
          if (
            !termin.wiederholungsEnde ||
            nextDate <= termin.wiederholungsEnde
          ) {
            await ctx.db.patch(termin._id, {
              naechsteWiederholung: nextDate,
            });
            updatedCount++;
          } else {
            // Wiederholung ist beendet - setze istWiederholend auf false
            await ctx.db.patch(termin._id, {
              istWiederholend: false,
              naechsteWiederholung: undefined,
            });
          }
        }
      }
    }
    return { updatedCount, totalRecurring: recurringTermine.length };
  },
});
