import { v } from 'convex/values';
import { mutation, query } from '../_generated/server';

// Define the structure for a field value
const feldWertSchema = v.object({
  feldId: v.number(),
  feldWert: v.string(),
  feldName: v.optional(v.string()), // For backward compatibility
});

// Define the structure for a documentation entry
const dokumentationSchema = v.object({
  _id: v.id('kunden_dokumentation'),
  _creationTime: v.number(),
  kundenId: v.id('kunden'),
  kategorieID: v.number(),
  feldwerte: v.array(feldWertSchema),
});

// Define the structure for a documentation entry with category and customer info
const dokumentationMitInfoSchema = v.object({
  _id: v.id('kunden_dokumentation'),
  _creationTime: v.number(),
  kundenId: v.id('kunden'),
  kategorieID: v.number(),
  feldwerte: v.array(feldWertSchema),
  kategorieName: v.string(),
  kundeName: v.string(),
});

/**
 * Alle Dokumentationseinträge für einen Kunden abrufen
 */
export const getByKunde = query({
  args: {
    kundenId: v.id('kunden'),
  },
  returns: v.array(dokumentationMitInfoSchema),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    // Dokumentationseinträge für den Kunden abrufen
    const eintraege = await ctx.db
      .query('kunden_dokumentation')
      .withIndex('by_kunde', (q) => q.eq('kundenId', args.kundenId))
      .collect();

    // Ergebnisse mit Kategorie- und Kundeninformationen anreichern
    const results: Array<
      (typeof eintraege)[0] & { kategorieName: string; kundeName: string }
    > = [];

    // Parallele Abfragen für bessere Performance
    const enrichmentPromises = eintraege.map(async (eintrag) => {
      // Finde die Kategorie anhand der kategorieID
      const [kategorien, kunde] = await Promise.all([
        ctx.db
          .query('system_doku_kategorien')
          .filter((q) => q.eq(q.field('kategorieID'), eintrag.kategorieID))
          .collect(),
        ctx.db.get(eintrag.kundenId),
      ]);

      const kategorie = kategorien.length > 0 ? kategorien[0] : null;

      if (kategorie && kunde) {
        return {
          ...eintrag,
          kategorieName: kategorie.name,
          kundeName: kunde.name,
        };
      }
      return null;
    });

    const enrichedResults = await Promise.all(enrichmentPromises);

    // Filter out null results
    for (const result of enrichedResults) {
      if (result !== null) {
        results.push(result);
      }
    }

    return results;
  },
});

/**
 * Dokumentationseinträge für einen Kunden und eine Kategorie abrufen
 */
export const getByKundeAndKategorie = query({
  args: {
    kundenId: v.id('kunden'),
    kategorieID: v.number(),
  },
  returns: v.array(dokumentationSchema),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    // Dokumentationseinträge für den Kunden und die Kategorie abrufen
    return await ctx.db
      .query('kunden_dokumentation')
      .withIndex('by_kunde_and_kategorie', (q) =>
        q.eq('kundenId', args.kundenId).eq('kategorieID', args.kategorieID)
      )
      .collect();
  },
});

/**
 * Einzelnen Dokumentationseintrag anhand seiner ID abrufen
 */
export const get = query({
  args: {
    id: v.id('kunden_dokumentation'),
  },
  returns: v.union(dokumentationSchema, v.null()),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    return await ctx.db.get(args.id);
  },
});

/**
 * Neuen Dokumentationseintrag erstellen
 */
export const create = mutation({
  args: {
    kundenId: v.id('kunden'),
    kategorieID: v.number(),
    feldwerte: v.array(feldWertSchema),
  },
  returns: v.id('kunden_dokumentation'),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    // Kunde prüfen
    const kunde = await ctx.db.get(args.kundenId);
    if (!kunde) {
      throw new Error('Kunde nicht gefunden.');
    }

    // Kategorie anhand der kategorieID finden
    const kategorien = await ctx.db
      .query('system_doku_kategorien')
      .filter((q) => q.eq(q.field('kategorieID'), args.kategorieID))
      .collect();

    if (kategorien.length === 0) {
      throw new Error('Kategorie nicht gefunden.');
    }

    const kategorie = kategorien[0];

    // Feldwerte validieren
    validateFieldValues(args.feldwerte, kategorie.felder);

    // Dokumentationseintrag erstellen
    return await ctx.db.insert('kunden_dokumentation', {
      kundenId: args.kundenId,
      kategorieID: args.kategorieID,
      feldwerte: args.feldwerte,
    });
  },
});

/**
 * Dokumentationseintrag aktualisieren
 */
export const update = mutation({
  args: {
    id: v.id('kunden_dokumentation'),
    feldwerte: v.optional(v.array(feldWertSchema)),
  },
  returns: v.id('kunden_dokumentation'),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    const { id, ...updateData } = args;

    // Dokumentationseintrag abrufen
    const eintrag = await ctx.db.get(id);
    if (!eintrag) {
      throw new Error('Dokumentationseintrag nicht gefunden.');
    }

    // Wenn Feldwerte aktualisiert werden, diese validieren
    if (updateData.feldwerte) {
      // Kategorie anhand der kategorieID finden
      const kategorien = await ctx.db
        .query('system_doku_kategorien')
        .filter((q) => q.eq(q.field('kategorieID'), eintrag.kategorieID))
        .collect();

      if (kategorien.length === 0) {
        throw new Error('Kategorie nicht gefunden.');
      }

      const kategorie = kategorien[0];

      validateFieldValues(updateData.feldwerte, kategorie.felder);
    }

    // Dokumentationseintrag aktualisieren
    await ctx.db.patch(id, updateData);
    return id;
  },
});

/**
 * Dokumentationseintrag löschen
 */
export const remove = mutation({
  args: { id: v.id('kunden_dokumentation') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    // Optional: Check if the document exists before attempting to delete
    const existingDoc = await ctx.db.get(args.id);
    if (!existingDoc) {
      // Depending on desired behavior, you might return something or just let it complete
      return; // Or: throw new Error("Eintrag nicht gefunden.");
    }

    // Proceed with deletion
    await ctx.db.delete(args.id);
    // No explicit return needed if returns v.null() or undefined (which becomes null)
  },
});

/**
 * Hilfsfunktion zur Validierung der Eindeutigkeit von Feld-IDs
 */
function validateUniqueFieldIds(
  feldwerte: { feldId: number; feldWert: string; feldName?: string }[]
) {
  const feldIds = feldwerte.map((fw) => fw.feldId);
  if (new Set(feldIds).size !== feldIds.length) {
    throw new Error('Feld-IDs müssen eindeutig sein.');
  }
}

/**
 * Hilfsfunktion zur Validierung erforderlicher Felder
 */
function validateRequiredFields(
  feldwerte: { feldId: number; feldWert: string; feldName?: string }[],
  kategorieFelder: { feldId: number; name: string; istErforderlich: boolean }[]
) {
  const erforderlicheFelder = kategorieFelder
    .filter((f) => f.istErforderlich)
    .map((f) => ({ feldId: f.feldId, name: f.name }));

  for (const erforderlichesFeld of erforderlicheFelder) {
    const hasValidValue = feldwerte.some(
      (fw) =>
        fw.feldId === erforderlichesFeld.feldId && fw.feldWert.trim() !== ''
    );

    if (!hasValidValue) {
      throw new Error(
        `Das Feld "${erforderlichesFeld.name}" ist erforderlich.`
      );
    }
  }
}

/**
 * Hilfsfunktion zur Validierung und Korrektur von Feld-IDs
 */
function validateAndCorrectFieldIds(
  feldwerte: { feldId: number; feldWert: string; feldName?: string }[],
  kategorieFelder: { feldId: number; name: string }[]
) {
  const gueltigeFelder = new Set(kategorieFelder.map((f) => f.feldId));

  for (const feldwert of feldwerte) {
    if (!gueltigeFelder.has(feldwert.feldId)) {
      if (feldwert.feldName) {
        const matchingFeld = kategorieFelder.find(
          (f) => f.name === feldwert.feldName
        );
        if (matchingFeld) {
          feldwert.feldId = matchingFeld.feldId;
        } else {
          throw new Error(
            `Ungültige Feld-ID: ${feldwert.feldId} und kein passender Feldname gefunden`
          );
        }
      } else {
        throw new Error(`Ungültige Feld-ID: ${feldwert.feldId}`);
      }
    }
  }
}

/**
 * Hilfsfunktion zur Validierung von Select-Feldoptionen
 */
function validateSelectFieldOptions(
  feldwerte: { feldId: number; feldWert: string; feldName?: string }[],
  kategorieFelder: {
    feldId: number;
    name: string;
    typ: string;
    optionen?: string[];
  }[]
) {
  for (const feldwert of feldwerte) {
    const kategoriefeld = kategorieFelder.find(
      (f) => f.feldId === feldwert.feldId
    );

    if (
      kategoriefeld?.typ === 'select' &&
      kategoriefeld.optionen &&
      feldwert.feldWert !== '' &&
      !kategoriefeld.optionen.includes(feldwert.feldWert)
    ) {
      throw new Error(
        `Ungültiger Wert für Select-Feld "${kategoriefeld.name}": ${feldwert.feldWert}`
      );
    }
  }
}

/**
 * Hilfsfunktion zur Validierung von Feldwerten
 */
function validateFieldValues(
  feldwerte: { feldId: number; feldWert: string; feldName?: string }[],
  kategorieFelder: {
    feldId: number;
    name: string;
    typ: string;
    istErforderlich: boolean;
    optionen?: string[];
  }[]
) {
  validateUniqueFieldIds(feldwerte);
  validateRequiredFields(feldwerte, kategorieFelder);
  validateAndCorrectFieldIds(feldwerte, kategorieFelder);
  validateSelectFieldOptions(feldwerte, kategorieFelder);
}
