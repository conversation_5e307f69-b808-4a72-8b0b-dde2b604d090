import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

// Field types for documentation categories
export const FIELD_TYPES = {
  TEXT: 'text',
  TEXTAREA: 'textarea',
  NUMBER: 'number',
  PASSWORD: 'password',
  URL: 'url',
  EMAIL: 'email',
  DATE: 'date',
  CHECKBOX: 'checkbox',
  SELECT: 'select',
  PHONE: 'phone',
};

export default defineSchema({
  kunden: defineTable({
    name: v.string(),
    stundenpreis: v.number(),
    anfahrtskosten: v.number(),
    standorte: v.array(
      v.object({
        strasse: v.string(),
        plz: v.string(),
        ort: v.string(),
        land: v.optional(v.string()),
        istHauptstandort: v.boolean(),
      })
    ),
    ansprechpartner: v.array(
      v.object({
        name: v.string(),
        email: v.optional(v.string()),
        telefon: v.optional(v.string()),
        mobil: v.optional(v.string()),
        position: v.optional(v.string()),
        istHauptansprechpartner: v.boolean(),
        istEmailLieferscheinEmpfaenger: v.optional(v.boolean()),
        istEmailUebersichtEmpfaenger: v.optional(v.boolean()),
        istEmailAngebotEmpfaenger: v.optional(v.boolean()),
        istEmailAnrede: v.optional(v.boolean()),
      })
    ),
  }),
  mitarbeiter: defineTable({
    name: v.string(),
    email: v.string(),
  }),
  kunden_kontingente: defineTable({
    kundenId: v.id('kunden'),
    name: v.string(),
    stunden: v.number(),
    verbrauchteStunden: v.number(),
    startDatum: v.number(),
    endDatum: v.number(),
    istAktiv: v.boolean(),
  })
    .index('by_kunde_and_status', ['kundenId', 'istAktiv'])
    .index('by_status', ['istAktiv']),
  kunden_leistungen: defineTable({
    kundenId: v.id('kunden'),
    mitarbeiterId: v.id('mitarbeiter'),
    kontingentId: v.id('kunden_kontingente'),
    startZeit: v.number(),
    endZeit: v.number(),
    art: v.string(),
    mitAnfahrt: v.boolean(),
    beschreibung: v.string(),
    stunden: v.number(),
    stundenpreis: v.number(),
    anfahrtskosten: v.number(),
    // For splitting Leistung across two Kontingente
    stundenKontingent1: v.optional(v.number()),
    kontingentId2: v.optional(v.id('kunden_kontingente')),
    stundenKontingent2: v.optional(v.number()),
  })
    .index('by_kunde', ['kundenId'])
    .index('by_mitarbeiter', ['mitarbeiterId'])
    .index('by_kontingent', ['kontingentId']),
  system_feedback: defineTable({
    userName: v.string(),
    text: v.string(),
    type: v.string(),
    status: v.string(),
  })
    .index('by_status', ['status'])
    .index('by_type', ['type'])
    .index('by_status_and_type', ['status', 'type']),
  system_doku_kategorien: defineTable({
    name: v.string(),
    beschreibung: v.string(),
    kategorieID: v.number(),
    felder: v.array(
      v.object({
        feldId: v.number(),
        name: v.string(),
        typ: v.string(),
        istErforderlich: v.boolean(),
        optionen: v.optional(v.array(v.string())),
        placeholder: v.optional(v.string()),
        istKopierbar: v.optional(v.boolean()),
        istVersteckt: v.optional(v.boolean()),
        istExtrafeld: v.optional(v.boolean()),
      })
    ),
    reihenfolge: v.number(),
  }).index('by_name', ['name']),
  kunden_dokumentation: defineTable({
    kundenId: v.id('kunden'),
    kategorieID: v.number(),
    feldwerte: v.array(
      v.object({
        feldId: v.number(),
        feldWert: v.string(),
      })
    ),
  })
    .index('by_kunde', ['kundenId'])
    .index('by_kunde_and_kategorie', ['kundenId', 'kategorieID']),
  kunden_lieferscheine: defineTable({
    kundenId: v.id('kunden'),
    nummer: v.optional(v.string()),
    erstelltAm: v.number(),
    istKorrektur: v.boolean(),
    originalId: v.optional(v.id('kunden_lieferscheine')),
    korrekturVersion: v.optional(v.number()),
    bemerkung: v.optional(v.string()),
    status: v.string(),
    wurdeKorrigiert: v.optional(v.boolean()),
  })
    .index('by_kunde', ['kundenId'])
    .index('by_nummer', ['nummer'])
    .index('by_original', ['originalId']),
  kunden_lieferscheine_zuordnung: defineTable({
    lieferscheinId: v.id('kunden_lieferscheine'),
    leistungId: v.id('kunden_leistungen'),
  })
    .index('by_lieferschein', ['lieferscheinId'])
    .index('by_leistung', ['leistungId'])
    .index('by_lieferschein_and_leistung', ['lieferscheinId', 'leistungId']),
  kunden_angebote: defineTable({
    kundenId: v.id('kunden'),
    nummer: v.optional(v.string()), // AG2500001 format when finalized, undefined for drafts
    erstelltAm: v.number(),
    gueltigBis: v.number(),
    status: v.string(), // "entwurf" or "fertig"
    istKorrektur: v.boolean(),
    originalId: v.optional(v.id('kunden_angebote')),
    korrekturVersion: v.optional(v.number()),
    positionen: v.array(
      v.object({
        id: v.string(),
        titel: v.string(),
        beschreibung: v.optional(v.string()),
        menge: v.number(),
        einheit: v.string(),
        einzelpreis: v.number(),
      })
    ),
    bemerkung: v.optional(v.string()),
    gesamtsummeNetto: v.number(),
    umsatzsteuer: v.number(),
    gesamtsummeBrutto: v.number(),
    wurdeKorrigiert: v.optional(v.boolean()),
  })
    .index('by_nummer', ['nummer'])
    .index('by_original', ['originalId']),
  kunden_termine: defineTable({
    kundenId: v.id('kunden'),
    titel: v.string(),
    kategorie: v.string(), // Frei eingebbare Kategorie (Hardware, Software, etc.)
    datum: v.optional(v.string()), // ISO date string - optional für wiederholende Termine
    uhrzeit: v.optional(v.string()), // Time in HH:MM format
    notizen: v.optional(v.string()),
    // Wiederholung
    istWiederholend: v.boolean(),
    wiederholungsIntervall: v.optional(
      v.union(
        v.literal('taeglich'),
        v.literal('woechentlich'),
        v.literal('monatlich'),
        v.literal('jaehrlich')
      )
    ),
    // Spezifische Wiederholungseinstellungen
    wochentag: v.optional(
      v.union(
        v.literal('montag'),
        v.literal('dienstag'),
        v.literal('mittwoch'),
        v.literal('donnerstag'),
        v.literal('freitag'),
        v.literal('samstag'),
        v.literal('sonntag')
      )
    ), // Für wöchentliche Wiederholung
    monatlicheWiederholung: v.optional(
      v.union(
        v.literal('erster_montag'),
        v.literal('erster_dienstag'),
        v.literal('erster_mittwoch'),
        v.literal('erster_donnerstag'),
        v.literal('erster_freitag'),
        v.literal('letzter_montag'),
        v.literal('letzter_dienstag'),
        v.literal('letzter_mittwoch'),
        v.literal('letzter_donnerstag'),
        v.literal('letzter_freitag'),
        v.literal('tag_1'),
        v.literal('tag_15'),
        v.literal('letzter_tag')
      )
    ), // Für monatliche Wiederholung
    wiederholungsEnde: v.optional(v.string()), // ISO date string
    naechsteWiederholung: v.optional(v.string()), // ISO date string für nächsten Termin
  })
    .index('by_kunde', ['kundenId'])
    .index('by_datum', ['datum'])
    .index('by_kategorie', ['kategorie'])
    .index('by_kunde_and_datum', ['kundenId', 'datum'])
    .index('by_naechste_wiederholung', ['naechsteWiederholung']),
});
