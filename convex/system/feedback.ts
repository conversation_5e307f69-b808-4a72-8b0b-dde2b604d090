import { v } from 'convex/values';
import { mutation, query } from '../_generated/server';

/**
 * System Feedback-Objekt-Definition
 */
const feedbackObject = v.object({
  _id: v.id('system_feedback'),
  _creationTime: v.number(),
  userName: v.string(),
  text: v.string(),
  type: v.string(),
  status: v.string(),
});

/**
 * Neues Feedback erstellen
 */
export const create = mutation({
  args: {
    userName: v.string(),
    text: v.string(),
    type: v.string(),
  },
  returns: v.id('system_feedback'),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    return await ctx.db.insert('system_feedback', {
      ...args,
      status: 'offen',
    });
  },
});

/**
 * Alle Feedback-Einträge abrufen (<PERSON> Ad<PERSON>-<PERSON>cke)
 */
export const list = query({
  args: {
    status: v.optional(v.string()),
    type: v.optional(v.string()),
  },
  returns: v.array(feedbackObject),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    // Abfrage mit optionalem Status-Filter und Type-Filter
    const feedbackQuery = ctx.db.query('system_feedback');

    // Wenn beide Filter angegeben sind, verwenden wir den kombinierten Index
    if (args.status !== undefined && args.type !== undefined) {
      const status = args.status;
      const type = args.type;
      return await feedbackQuery
        .withIndex('by_status_and_type', (queryBuilder) =>
          queryBuilder.eq('status', status).eq('type', type)
        )
        .collect();
    }

    // Filter nach Status, falls angegeben
    if (args.status !== undefined) {
      const status = args.status;
      return await feedbackQuery
        .withIndex('by_status', (queryBuilder) =>
          queryBuilder.eq('status', status)
        )
        .collect();
    }

    // Filter nach Typ, falls angegeben
    if (args.type !== undefined) {
      const type = args.type;
      return await feedbackQuery
        .withIndex('by_type', (queryBuilder) => queryBuilder.eq('type', type))
        .collect();
    }

    // Keine Filter angegeben, alle Einträge zurückgeben
    return await feedbackQuery.collect();
  },
});

/**
 * Status eines Feedback-Eintrags aktualisieren
 */
export const updateStatus = mutation({
  args: {
    id: v.id('system_feedback'),
    status: v.string(),
  },
  returns: v.id('system_feedback'),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    await ctx.db.patch(args.id, { status: args.status });
    return args.id;
  },
});

/**
 * Feedback-Eintrag löschen
 */
export const remove = mutation({
  args: {
    id: v.id('system_feedback'),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    await ctx.db.delete(args.id);
    return null;
  },
});
