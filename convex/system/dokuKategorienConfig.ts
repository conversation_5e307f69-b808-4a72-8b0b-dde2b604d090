// Interface for field definition with additional properties
export interface DokuKategorieFeld {
  feldId: number;
  name: string;
  typ: string;
  istErforderlich: boolean;
  optionen?: string[];
  placeholder?: string;
  istKopierbar?: boolean;
  istVersteckt?: boolean;
  istExtrafeld?: boolean;
}

// Interface for category definition
export interface DokuKategorie {
  name: string;
  beschreibung: string;
  reihenfolge: number;
  kategorieID: number;
  felder: DokuKategorieFeld[];
}

// Predefined documentation categories with their fields
export const DOKU_KATEGORIEN: DokuKategorie[] = [
  {
    name: 'Wichtige Hinweise & Notizen',
    beschreibung:
      'Allgemeine und wichtige Informationen, Notizen oder temporäre Hinweise.',
    reihenfolge: 0,
    kategorieID: 0,
    felder: [
      {
        feldId: 0,
        name: 'Titel / Betreff',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: '<PERSON>rze <PERSON> des Hinweises',
      },
      {
        feldId: 1,
        name: 'Priorität',
        typ: 'select',
        istErforderlich: true,
        istExtrafeld: true,
        optionen: ['Hoch', 'Mittel', 'Niedrig'],
        istKopierbar: true,
      },
      {
        feldId: 2,
        name: 'Details / Beschreibung',
        typ: 'textarea',
        istErforderlich: true,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder: 'Ausführliche Beschreibung...',
      },
      {
        feldId: 3,
        name: 'Verantwortlicher',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'Name des Verantwortlichen',
      },
      {
        feldId: 4,
        name: 'Gültig bis',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 5,
        name: 'Status',
        typ: 'select',
        istErforderlich: false,
        optionen: ['Offen', 'In Bearbeitung', 'Erledigt', 'Zur Information'],
        placeholder: 'Aktueller Status',
      },
    ],
  },
  {
    name: 'Arbeitsplätze/Clients',
    beschreibung:
      'PCs, Laptops, Thin Clients, Docking Stations und zugehörige Peripherie.',
    reihenfolge: 10,
    kategorieID: 10,
    felder: [
      {
        feldId: 0,
        name: 'Gerätename',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. PC-USER01, LT-MARKETING02',
      },
      {
        feldId: 1,
        name: 'Benutzer',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'Hauptbenutzer des Geräts',
      },
      {
        feldId: 2,
        name: 'Typ',
        typ: 'select',
        istErforderlich: true,
        optionen: [
          'Desktop-PC',
          'Laptop',
          'Thin Client',
          'Workstation',
          'Tablet',
        ],
        placeholder: 'Gerätetyp auswählen',
      },
      {
        feldId: 3,
        name: 'Modell',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'Hersteller und Modell',
      },
      {
        feldId: 4,
        name: 'Seriennummer',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 5,
        name: 'Betriebssystem',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'z.B. Windows 11 Pro, macOS Sonoma',
      },
      {
        feldId: 6,
        name: 'CPU',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
        placeholder: 'z.B. Intel Core i5-12400',
      },
      {
        feldId: 7,
        name: 'RAM (GB)',
        typ: 'number',
        istErforderlich: false,
        istKopierbar: false,
        placeholder: 'z.B. 16',
      },
      {
        feldId: 8,
        name: 'Festplatte/SSD (GB)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
        placeholder: 'z.B. 512 SSD oder 1000 HDD',
      },
      {
        feldId: 9,
        name: 'IP-Adresse',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 10,
        name: 'MAC-Adresse',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 11,
        name: 'Standort',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'Raum, Abteilung',
      },
      {
        feldId: 12,
        name: 'Kaufdatum',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 13,
        name: 'Garantie bis',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 14,
        name: 'Zugehörige Software (wichtigste)',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder:
          'Wichtige installierte Software, z.B. Office Paket, CAD-Software (Versionen)',
      },
      {
        feldId: 15,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Zugangsdaten',
    beschreibung:
      'Zugangsdaten für verschiedene Systeme, Anwendungen und Geräte.',
    reihenfolge: 20,
    kategorieID: 20,
    felder: [
      {
        feldId: 0,
        name: 'System/Dienst',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. Router, Windows Server, Office 365 Admin',
      },
      {
        feldId: 1,
        name: 'Benutzername / ID',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 2,
        name: 'Passwort',
        typ: 'password',
        istErforderlich: true,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 3,
        name: 'URL / IP-Adresse',
        typ: 'url',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'URL oder IP zur Anmeldeseite',
      },
      {
        feldId: 4,
        name: 'Zweck / Notiz',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'Zusätzliche Informationen, Zweck des Zugangs',
      },
    ],
  },
  {
    name: 'Server (Physisch)',
    beschreibung: 'Alle physischen Server (Hardware-Details, Standort, etc.).',
    reihenfolge: 30,
    kategorieID: 30,
    felder: [
      {
        feldId: 0,
        name: 'Hostname / Name',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 1,
        name: 'Hersteller',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 2,
        name: 'Modell',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 3,
        name: 'Seriennummer',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 4,
        name: 'Management IP (iDRAC, iLO)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 5,
        name: 'Betriebssystem (Host)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'z.B. VMware ESXi, Windows Server Hyper-V',
      },
      {
        feldId: 6,
        name: 'CPU',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 7,
        name: 'RAM (GB)',
        typ: 'number',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 8,
        name: 'Festplattenkonfiguration',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder: 'z.B. RAID-Level, Anzahl & Größe der Platten',
      },
      {
        feldId: 9,
        name: 'Netzwerkkarten (Anzahl & Typ)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 10,
        name: 'Standort (Raum, Rack)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 11,
        name: 'Kaufdatum',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 12,
        name: 'Garantie bis',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 13,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Server (Virtuell - VM)',
    beschreibung: 'Alle virtuellen Maschinen (VMs) und deren Konfiguration.',
    reihenfolge: 40,
    kategorieID: 40,
    felder: [
      {
        feldId: 0,
        name: 'VM-Name / Hostname',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 1,
        name: 'IP-Adresse(n)',
        typ: 'textarea',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'Eine IP pro Zeile',
      },
      {
        feldId: 2,
        name: 'Betriebssystem (Gast)',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. Windows Server 2022, Ubuntu 22.04 LTS',
      },
      {
        feldId: 3,
        name: 'Server Rollen / Hauptanwendungen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder:
          'z.B. Domain Controller, File Server, SQL Server, Webserver für App X',
      },
      {
        feldId: 4,
        name: 'CPU (vCores)',
        typ: 'number',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 5,
        name: 'RAM (GB)',
        typ: 'number',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 6,
        name: 'Festplatten (GB & Anzahl)',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'C: 100 GB (OS)\nD: 500 GB (Daten)',
      },
      {
        feldId: 7,
        name: 'Physischer Host-Server',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'Name des Host-Servers',
      },
      {
        feldId: 8,
        name: 'Backup-Status / Plan',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'Ja, täglich / Siehe Backup-Plan XYZ',
      },
      {
        feldId: 9,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Netzwerkgeräte (Switches, Router, Firewalls, APs)',
    beschreibung:
      'Alle aktiven Netzwerkkomponenten wie Switches, Router, Firewalls, WLAN Access Points.',
    reihenfolge: 50,
    kategorieID: 50,
    felder: [
      {
        feldId: 0,
        name: 'Gerätename / Hostname',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 1,
        name: 'Gerätetyp',
        typ: 'select',
        istErforderlich: true,
        optionen: ['Switch', 'Router', 'Firewall', 'USV', 'Sonstiges'],
      },
      {
        feldId: 2,
        name: 'Hersteller',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 3,
        name: 'Modell',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 4,
        name: 'IP-Adresse (Management)',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 5,
        name: 'Seriennummer',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 6,
        name: 'Standort (Raum, Rack)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 7,
        name: 'Firmware Version',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 8,
        name: 'Konfigurationsbackup (Pfad/Datum)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
      {
        feldId: 9,
        name: 'Notizen / Wichtige Ports',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Netzwerke (Logisch)',
    beschreibung: 'VLANs, IP-Subnetze, Gateways, DNS, DHCP-Bereiche.',
    reihenfolge: 60,
    kategorieID: 60,
    felder: [
      {
        feldId: 0,
        name: 'Netzwerkname / Zweck',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. Office-LAN, Server-VLAN, Gastnetz',
      },
      {
        feldId: 1,
        name: 'IP-Netzwerkadresse',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. ***********',
      },
      {
        feldId: 2,
        name: 'Subnetzmaske',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. ************* oder /24',
      },
      {
        feldId: 3,
        name: 'VLAN-ID',
        typ: 'number',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 4,
        name: 'Gateway IP',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 5,
        name: 'DNS-Server (primär/sekundär)',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'Eine IP pro Zeile',
      },
      {
        feldId: 6,
        name: 'DHCP Server IP',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 7,
        name: 'DHCP Adressbereich (von-bis)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'z.B. ************* - *************',
      },
      {
        feldId: 8,
        name: 'Domäne (AD / DNS)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'z.B. internal.firma.de',
      },
      {
        feldId: 9,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Software Lizenzen',
    beschreibung: 'Übersicht aller Software-Lizenzen und deren Details.',
    reihenfolge: 70,
    kategorieID: 70,
    felder: [
      {
        feldId: 0,
        name: 'Softwarename & Version',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. Microsoft Office 2021 Pro, Adobe Creative Cloud',
      },
      {
        feldId: 1,
        name: 'Lizenztyp',
        typ: 'select',
        istErforderlich: true,
        optionen: [
          'Einzelplatz',
          'Volumenlizenz (MAK/KMS)',
          'Abonnement',
          'Open Source',
          'Freeware',
        ],
        istKopierbar: true,
        placeholder: 'z.B. Einzelplatz, Volumenlizenz, Abo',
      },
      {
        feldId: 2,
        name: 'Lizenzschlüssel / Produktkey',
        typ: 'password',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
        placeholder: 'ABCDE-FGHIJ-KLMNO-PQRST-UVWXY',
      },
      {
        feldId: 3,
        name: 'Anzahl Lizenzen',
        typ: 'number',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: '1',
      },
      {
        feldId: 4,
        name: 'Lizenzportal URL (falls vorhanden)',
        typ: 'url',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 5,
        name: 'Zugangsdaten Lizenzportal',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: "Siehe Kategorie 'Zugangsdaten'",
      },
      {
        feldId: 6,
        name: 'Kaufdatum / Startdatum Abo',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 7,
        name: 'Ablaufdatum / Verlängerung am',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 8,
        name: 'Installationshinweise / Medien',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder: 'Link zum Download, spezielle Installationsschritte etc.',
      },
      {
        feldId: 9,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Drucker, Scanner & MFP',
    beschreibung:
      'Verwaltung von Druckern, Scannern und Multifunktionsgeräten.',
    reihenfolge: 80,
    kategorieID: 80,
    felder: [
      {
        feldId: 0,
        name: 'Gerätename / Bezeichnung',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. Drucker_EG_Farbe, Scanner_Buchhaltung',
      },
      {
        feldId: 1,
        name: 'Modell',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 2,
        name: 'Hersteller',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 3,
        name: 'IP-Adresse',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 4,
        name: 'MAC-Adresse',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 5,
        name: 'Seriennummer',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 6,
        name: 'Standort',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 7,
        name: 'Verbrauchsmaterial (Toner, Tinte)',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder: 'Modellbezeichnungen der Verbrauchsmaterialien',
      },
      {
        feldId: 8,
        name: 'Zählerstand (Datum)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
        placeholder: 'z.B. S/W: 12345, Farbe: 6789 (Stand: TT.MM.JJJJ)',
      },
      {
        feldId: 9,
        name: 'Admin-Zugang URL',
        typ: 'url',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 10,
        name: 'Admin-Passwort',
        typ: 'password',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 11,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Datensicherung (Backup)',
    beschreibung: 'Konfiguration und Status von Datensicherungsjobs.',
    reihenfolge: 90,
    kategorieID: 90,
    felder: [
      {
        feldId: 0,
        name: 'Job-Name / Aufgabe',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. Fileserver_Taeglich, SQL_DB_Stündlich',
      },
      {
        feldId: 1,
        name: 'Backup-Software & Version',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. Veeam B&R v12, Windows Server Backup',
      },
      {
        feldId: 2,
        name: 'Gesicherte Daten / Systeme',
        typ: 'textarea',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'Welche Server, VMs, Verzeichnisse, Datenbanken?',
      },
      {
        feldId: 3,
        name: 'Backup-Ziel',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'NAS, USB-Festplatte, Cloud-Speicher (Anbieter)',
      },
      {
        feldId: 4,
        name: 'Backup-Typ',
        typ: 'select',
        istErforderlich: true,
        optionen: ['Vollständig', 'Inkrementell', 'Differentiell', 'Image'],
      },
      {
        feldId: 5,
        name: 'Zeitplan / Intervall',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: false,
        placeholder: 'z.B. Täglich 22:00 Uhr, Mo-Fr 18:00 Uhr',
      },
      {
        feldId: 6,
        name: 'Letzte erfolgreiche Sicherung',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
        placeholder: 'TT.MM.JJJJ HH:MM',
      },
      {
        feldId: 7,
        name: 'Letzter Restore-Test (Datum & Ergebnis)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'TT.MM.JJJJ - Erfolgreich / Fehlgeschlagen: Grund',
      },
      {
        feldId: 8,
        name: 'Aufbewahrungszeit der Backups',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
        placeholder: 'z.B. 14 Tage, 4 Wochen, 1 Jahr',
      },
      {
        feldId: 9,
        name: 'Notizen / Besonderheiten',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Sicherheitslösungen',
    beschreibung: 'Firewalls, Antivirus-Software, EDR, VPN-Lösungen etc.',
    reihenfolge: 100,
    kategorieID: 100,
    felder: [
      {
        feldId: 0,
        name: 'Produkt / Lösung',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. Sophos XG Firewall, Microsoft Defender for Endpoint',
      },
      {
        feldId: 1,
        name: 'Hersteller',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 2,
        name: 'Version / Modell',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 3,
        name: 'Lizenzinformationen',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: "Siehe Kategorie 'Software Lizenzen'",
      },
      {
        feldId: 4,
        name: 'Management-URL / Konsole',
        typ: 'url',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 5,
        name: 'Admin-Zugangsdaten',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: "Siehe Kategorie 'Zugangsdaten'",
      },
      {
        feldId: 6,
        name: 'Wichtige Konfigurationsdetails',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder: 'Firewall-Regeln, Antivirus-Policies, VPN-Einstellungen',
      },
      {
        feldId: 7,
        name: 'Letztes Update (Definitionen/Policy)',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 8,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'E-Mail-System & Konten',
    beschreibung:
      'Konfiguration des E-Mail-Servers und Verwaltung der E-Mail-Konten.',
    reihenfolge: 110,
    kategorieID: 110,
    felder: [
      {
        feldId: 0,
        name: 'E-Mail-Dienst / Serversoftware',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder:
          'z.B. Microsoft Exchange Online, Google Workspace, Lokaler Exchange Server 2019',
      },
      {
        feldId: 1,
        name: 'Hauptdomain(s)',
        typ: 'textarea',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'firma.de\nweitere-firma.com',
      },
      {
        feldId: 2,
        name: 'Admin-Konsole URL',
        typ: 'url',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 3,
        name: 'Admin-Zugangsdaten',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: "Siehe Kategorie 'Zugangsdaten'",
      },
      {
        feldId: 4,
        name: 'Art des Kontos',
        typ: 'select',
        istErforderlich: true,
        optionen: [
          'Benutzer-Postfach',
          'Shared Mailbox',
          'Ressourcen-Postfach',
          'Verteilergruppe',
          'Security Gruppe',
        ],
        placeholder: 'Typ des E-Mail-Objekts',
      },
      {
        feldId: 5,
        name: 'E-Mail-Adresse / Name',
        typ: 'email',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'max.mustermann@firma.<NAME_EMAIL>',
      },
      {
        feldId: 6,
        name: 'Zugehöriger Benutzer / Zweck',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 7,
        name: 'Passwort (falls separates Postfach-PW)',
        typ: 'password',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 8,
        name: 'Alias-Adressen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder: 'Eine Adresse pro Zeile',
      },
      {
        feldId: 9,
        name: 'Weiterleitungen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder: 'Weiterleitung an extern/intern',
      },
      {
        feldId: 10,
        name: 'Speicherplatz / Quota (GB)',
        typ: 'number',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 11,
        name: 'MX-Record(s)',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'mail.firma.de (Prio 10)',
      },
      {
        feldId: 12,
        name: 'SPF/DKIM/DMARC Status',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'Konfiguriert / Nicht konfiguriert / Probleme',
      },
      {
        feldId: 13,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Domains & SSL-Zertifikate',
    beschreibung: 'Verwaltung von Internetdomains und SSL/TLS-Zertifikaten.',
    reihenfolge: 120,
    kategorieID: 120,
    felder: [
      {
        feldId: 0,
        name: 'Domainname',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. meinefirma.de',
      },
      {
        feldId: 1,
        name: 'Registrar',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'Anbieter der Domain',
      },
      {
        feldId: 2,
        name: 'Registrar Login URL',
        typ: 'url',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 3,
        name: 'Registrar Zugangsdaten',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: "Siehe Kategorie 'Zugangsdaten'",
      },
      {
        feldId: 4,
        name: 'Ablaufdatum Domain',
        typ: 'date',
        istErforderlich: true,
        istKopierbar: false,
      },
      {
        feldId: 5,
        name: 'Admin-C E-Mail',
        typ: 'email',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 6,
        name: 'Verwendete Nameserver',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'ns1.provider.com\nns2.provider.com',
      },
      {
        feldId: 7,
        name: 'SSL-Zertifikat für (Common Name/SANs)',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: '*.meinefirma.de, www.meinefirma.de',
      },
      {
        feldId: 8,
        name: 'SSL-Aussteller (CA)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: "z.B. Let's Encrypt, Comodo, DigiCert",
      },
      {
        feldId: 9,
        name: 'SSL-Zertifikat Typ',
        typ: 'select',
        istErforderlich: false,
        optionen: [
          'Domain Validated (DV)',
          'Organization Validated (OV)',
          'Extended Validation (EV)',
          'Wildcard',
          'Multi-Domain (SAN)',
        ],
      },
      {
        feldId: 10,
        name: 'Ablaufdatum SSL-Zertifikat',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 11,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Cloud-Dienste & Abonnements',
    beschreibung:
      'Verwaltung von Cloud-Diensten (AWS, Azure, M365 etc.) und deren Abonnements.',
    reihenfolge: 130,
    kategorieID: 130,
    felder: [
      {
        feldId: 0,
        name: 'Dienst / Plattform',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. Microsoft 365, AWS, Google Cloud Platform, Azure',
      },
      {
        feldId: 1,
        name: 'Abonnement-ID / Kundennummer',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 2,
        name: 'Admin-Portal URL',
        typ: 'url',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 3,
        name: 'Admin-Zugangsdaten',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: "Siehe Kategorie 'Zugangsdaten'",
      },
      {
        feldId: 4,
        name: 'Hauptverwendungszweck',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        placeholder:
          'z.B. E-Mail-Hosting, VM-Hosting, Datenspeicherung, Webanwendungen',
      },
      {
        feldId: 5,
        name: 'Gebuchte Lizenzen / Ressourcen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder:
          'z.B. 50x M365 Business Standard, 2x Azure VMs (D2s_v3), 1TB S3 Storage',
      },
      {
        feldId: 6,
        name: 'Kosten pro Monat/Jahr (ca.)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 7,
        name: 'Support-Level / Vertrag',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 8,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Telefonie / VoIP-System',
    beschreibung:
      'Informationen zur Telefonanlage, VoIP-Provider und Endgeräten.',
    reihenfolge: 140,
    kategorieID: 140,
    felder: [
      {
        feldId: 0,
        name: 'System / Anbieter',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. Lokale Auerswald COMpact, Cloud PBX von NFON',
      },
      {
        feldId: 1,
        name: 'Modell / Produktname',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 2,
        name: 'Admin-Zugang URL / IP',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 3,
        name: 'Admin-Zugangsdaten',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: "Siehe Kategorie 'Zugangsdaten'",
      },
      {
        feldId: 4,
        name: 'Rufnummernblöcke / Hauptrufnummer',
        typ: 'textarea',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. +49 123 45678-0 bis -99',
      },
      {
        feldId: 5,
        name: 'Anzahl der Nebenstellen / Benutzer',
        typ: 'number',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 6,
        name: 'Anschlusstyp (SIP-Trunk, ISDN)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 7,
        name: 'SIP-Provider Zugangsdaten (falls zutreffend)',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder: 'Benutzername, Passwort, Registrar',
      },
      {
        feldId: 8,
        name: 'Wichtige Telefone / Endgeräte (Modell, Standort)',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder:
          'Chef-Telefon: Yealink T54W (Büro GF)\nKonferenzraum: Polycom Trio (Raum K1)',
      },
      {
        feldId: 9,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'WLAN-Konfiguration',
    beschreibung:
      'Details zu drahtlosen Netzwerken (SSIDs, Passwörter, Access Points).',
    reihenfolge: 150,
    kategorieID: 150,
    felder: [
      {
        feldId: 0,
        name: 'SSID (Mitarbeiter-WLAN)',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 1,
        name: 'Passwort (Mitarbeiter-WLAN)',
        typ: 'password',
        istErforderlich: true,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 2,
        name: 'Verschlüsselung (Mitarbeiter-WLAN)',
        typ: 'select',
        istErforderlich: true,
        optionen: ['WPA2-PSK', 'WPA3-Personal', 'WPA2/WPA3 Enterprise'],
        placeholder: 'z.B. WPA2-PSK',
      },
      {
        feldId: 3,
        name: 'SSID (Gäste-WLAN)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 4,
        name: 'Passwort (Gäste-WLAN)',
        typ: 'password',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 5,
        name: 'Verschlüsselung (Gäste-WLAN)',
        typ: 'select',
        istErforderlich: false,
        optionen: ['Offen', 'WPA2-PSK', 'WPA3-Personal'],
        placeholder: 'z.B. WPA2-PSK',
      },
      {
        feldId: 6,
        name: 'Access Point Management (Controller URL/IP, Standalone IPs)',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        placeholder:
          'z.B. UniFi Controller: ************ oder AP1: ************, AP2: ...',
      },
      {
        feldId: 7,
        name: 'Access Point Modelle & Standorte',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
        placeholder:
          'Modell: Standort\nUbiquiti U6-LR: EG Flur\nTP-Link EAP245: OG Büro',
      },
      {
        feldId: 8,
        name: 'RADIUS Server (falls Enterprise Auth)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'IP-Adresse des RADIUS Servers',
      },
      {
        feldId: 9,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Webhosting & Webanwendungen',
    beschreibung: 'Informationen zu gehosteten Websites und Webanwendungen.',
    reihenfolge: 160,
    kategorieID: 160,
    felder: [
      {
        feldId: 0,
        name: 'Website / Anwendung URL',
        typ: 'url',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 1,
        name: 'Hosting-Anbieter',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 2,
        name: 'Hosting-Paket / Servertyp',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
        placeholder: 'z.B. Shared Hosting Pro, vServer L',
      },
      {
        feldId: 3,
        name: 'Admin-Panel URL (Kunde)',
        typ: 'url',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 4,
        name: 'Admin-Panel Zugangsdaten (Kunde)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: "Siehe Kategorie 'Zugangsdaten'",
      },
      {
        feldId: 5,
        name: 'FTP/SFTP/SSH Host',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 6,
        name: 'FTP/SFTP/SSH Zugangsdaten',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: "Siehe Kategorie 'Zugangsdaten'",
      },
      {
        feldId: 7,
        name: 'Datenbank Host',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 8,
        name: 'Datenbank Name',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 9,
        name: 'Datenbank Benutzer & Passwort',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: "Siehe Kategorie 'Zugangsdaten'",
      },
      {
        feldId: 10,
        name: 'CMS / Framework & Version',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
        placeholder: 'z.B. WordPress 6.2, Joomla 4.1, Laravel 9.x',
      },
      {
        feldId: 11,
        name: 'PHP-Version (falls relevant)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 12,
        name: 'Wichtige Plugins / Erweiterungen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
      {
        feldId: 13,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Verträge & Dienstleister',
    beschreibung:
      'Verwaltung von IT-bezogenen Verträgen und Kontaktdaten von Dienstleistern.',
    reihenfolge: 170,
    kategorieID: 170,
    felder: [
      {
        feldId: 0,
        name: 'Vertragsgegenstand / Dienstleistung',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder:
          'z.B. Internetanschluss, Softwarewartung (Produkt XY), Server-Leasing',
      },
      {
        feldId: 1,
        name: 'Dienstleister / Lieferant',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 2,
        name: 'Vertragsnummer',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 3,
        name: 'Kundennummer beim Dienstleister',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 4,
        name: 'Laufzeit von',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 5,
        name: 'Laufzeit bis / Nächste Kündigungsoption',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 6,
        name: 'Kündigungsfrist (z.B. 3 Monate zum Laufzeitende)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 7,
        name: 'Kosten (mtl./jährl.)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
        placeholder: 'z.B. 49,90€ mtl. oder 1200€ jährl.',
      },
      {
        feldId: 8,
        name: 'Ansprechpartner beim Dienstleister (Name, Tel, Mail)',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
      {
        feldId: 9,
        name: 'Speicherort Vertragskopie (digital/physisch)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'Link zum PDF, Ordnerpfad etc.',
      },
      {
        feldId: 10,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Mobile Geräte (Firmenhandys/Tablets)',
    beschreibung: 'Verwaltung von firmeneigenen Smartphones und Tablets.',
    reihenfolge: 180,
    kategorieID: 180,
    felder: [
      {
        feldId: 0,
        name: 'Benutzer / Mitarbeiter',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 1,
        name: 'Gerätetyp',
        typ: 'select',
        istErforderlich: true,
        optionen: ['Smartphone', 'Tablet'],
      },
      {
        feldId: 2,
        name: 'Modell',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. Apple iPhone 14, Samsung Galaxy S23, iPad Pro 11',
      },
      {
        feldId: 3,
        name: 'IMEI / Seriennummer',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 4,
        name: 'Telefonnummer (falls zutreffend)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 5,
        name: 'SIM-Karten-Nummer (ICCID)',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
      },
      {
        feldId: 6,
        name: 'PIN / PUK',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        istVersteckt: true,
        placeholder: 'PIN: XXXX / PUK: YYYYYYYY',
      },
      {
        feldId: 7,
        name: 'Betriebssystem & Version',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: false,
        placeholder: 'z.B. iOS 16.5, Android 13',
      },
      {
        feldId: 8,
        name: 'MDM-Status (Mobile Device Management)',
        typ: 'select',
        istErforderlich: false,
        optionen: ['Ja - Verwaltet durch XYZ', 'Nein', 'Unbekannt'],
        placeholder: 'Ist das Gerät zentral verwaltet?',
      },
      {
        feldId: 9,
        name: 'Kaufdatum / Vertragsbeginn',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 10,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
  {
    name: 'Netzwerk Infrastruktur (Physisch)',
    beschreibung:
      'Dokumentation der physischen Netzwerkverkabelung (Patchpanel, Dosen).',
    reihenfolge: 190,
    kategorieID: 190,
    felder: [
      {
        feldId: 0,
        name: 'Dosen-ID / Bezeichnung',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
        placeholder: 'z.B. R101-D01-P1 (Raum-Dose-Port)',
      },
      {
        feldId: 1,
        name: 'Raum / Standort',
        typ: 'text',
        istErforderlich: true,
        istKopierbar: true,
      },
      {
        feldId: 2,
        name: 'Patchpanel-ID',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'z.B. PP-SRV-01',
      },
      {
        feldId: 3,
        name: 'Patchpanel-Port Nr.',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 4,
        name: 'Verbunden mit Switch-Port',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'Switchname Port X/Y',
      },
      {
        feldId: 5,
        name: 'VLAN-ID (falls fest zugewiesen)',
        typ: 'number',
        istErforderlich: false,
        istKopierbar: true,
      },
      {
        feldId: 6,
        name: 'Funktion / Angeschlossenes Gerät',
        typ: 'text',
        istErforderlich: false,
        istKopierbar: true,
        placeholder: 'PC-MaxM, Drucker-Marketing, VoIP-Telefon',
      },
      {
        feldId: 7,
        name: 'Getestet / Zertifiziert am',
        typ: 'date',
        istErforderlich: false,
        istKopierbar: false,
      },
      {
        feldId: 8,
        name: 'Notizen',
        typ: 'textarea',
        istErforderlich: false,
        istKopierbar: true,
        istExtrafeld: true,
      },
    ],
  },
];
