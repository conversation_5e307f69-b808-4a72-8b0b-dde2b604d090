import { v } from 'convex/values';
// Doc, Id imports removed as they are unused
import { mutation, query } from '../_generated/server';

// Define the structure for a single Standort
const standortSchema = v.object({
  strasse: v.string(),
  plz: v.string(),
  ort: v.string(),
  land: v.optional(v.string()),
  istHauptstandort: v.boolean(),
});

// Define the structure for a single Ansprechpartner
const ansprechpartnerSchema = v.object({
  name: v.string(),
  email: v.optional(v.string()),
  telefon: v.optional(v.string()),
  mobil: v.optional(v.string()),
  position: v.optional(v.string()),
  istHauptansprechpartner: v.boolean(),
  istEmailLieferscheinEmpfaenger: v.optional(v.boolean()),
  istEmailUebersichtEmpfaenger: v.optional(v.boolean()),
  istEmailAnrede: v.optional(v.boolean()),
});

// Define the structure for the Kunde document
const kundeSchema = v.object({
  _id: v.id('kunden'),
  _creationTime: v.number(),
  name: v.string(),
  stundenpreis: v.number(),
  anfahrtskosten: v.number(),
  standorte: v.array(standortSchema),
  ansprechpartner: v.array(ansprechpartnerSchema),
});

// Helper function to validate primary flags
const validatePrimaryFlags = <
  T extends { istHauptstandort?: boolean; istHauptansprechpartner?: boolean },
>(
  items: T[],
  flagKey: 'istHauptstandort' | 'istHauptansprechpartner'
): boolean => {
  return items.filter((item) => item[flagKey]).length <= 1;
};

/**
 * Liste aller Kunden zurückgeben, inklusive neuer Felder für Standorte und Ansprechpartner
 */
export const list = query({
  args: {},
  returns: v.array(kundeSchema),
  handler: async (ctx) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    return await ctx.db.query('kunden').collect();
  },
});

/**
 * Neuen Kunden erstellen, inklusive neuer Felder für Standorte und Ansprechpartner
 */
export const create = mutation({
  args: {
    name: v.string(),
    stundenpreis: v.number(),
    anfahrtskosten: v.number(),
    standorte: v.array(standortSchema),
    ansprechpartner: v.array(ansprechpartnerSchema),
  },
  returns: v.id('kunden'),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    // Validate primary flags
    if (!validatePrimaryFlags(args.standorte, 'istHauptstandort')) {
      throw new Error('Es darf maximal ein Hauptstandort festgelegt werden.');
    }
    if (
      !validatePrimaryFlags(args.ansprechpartner, 'istHauptansprechpartner')
    ) {
      throw new Error(
        'Es darf maximal ein Hauptansprechpartner festgelegt werden.'
      );
    }

    // Ensure at least one Standort and Ansprechpartner if arrays are not empty
    if (
      args.standorte.length > 0 &&
      !args.standorte.some((s) => s.istHauptstandort)
    ) {
      // If no Hauptstandort is explicitly set, make the first one the Hauptstandort.
      // Or throw error: throw new Error("Ein Hauptstandort muss ausgewählt werden, wenn Standorte vorhanden sind.");
      args.standorte[0].istHauptstandort = true;
    }
    if (
      args.ansprechpartner.length > 0 &&
      !args.ansprechpartner.some((a) => a.istHauptansprechpartner)
    ) {
      // If no Hauptansprechpartner is explicitly set, make the first one the Hauptansprechpartner.
      // Or throw error: throw new Error("Ein Hauptansprechpartner muss ausgewählt werden, wenn Ansprechpartner vorhanden sind.");
      args.ansprechpartner[0].istHauptansprechpartner = true;
    }

    return await ctx.db.insert('kunden', {
      name: args.name,
      stundenpreis: args.stundenpreis,
      anfahrtskosten: args.anfahrtskosten,
      standorte: args.standorte,
      ansprechpartner: args.ansprechpartner.map((ap) => ({
        ...ap,
        mobil: ap.mobil || undefined,
      })),
    });
  },
});

/**
 * Kunden aktualisieren, inklusive neuer Felder für Standorte und Ansprechpartner
 */
export const update = mutation({
  args: {
    id: v.id('kunden'),
    name: v.optional(v.string()),
    stundenpreis: v.optional(v.number()),
    anfahrtskosten: v.optional(v.number()),
    standorte: v.optional(v.array(standortSchema)),
    ansprechpartner: v.optional(v.array(ansprechpartnerSchema)),
  },
  returns: v.id('kunden'),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    const { id, ...data } = args;

    // Validate primary flags if arrays are being updated
    if (
      data.standorte &&
      !validatePrimaryFlags(data.standorte, 'istHauptstandort')
    ) {
      throw new Error('Es darf maximal ein Hauptstandort festgelegt werden.');
    }
    if (
      data.ansprechpartner &&
      !validatePrimaryFlags(data.ansprechpartner, 'istHauptansprechpartner')
    ) {
      throw new Error(
        'Es darf maximal ein Hauptansprechpartner festgelegt werden.'
      );
    }

    // Ensure at least one primary if arrays are present and being updated
    if (
      data.standorte &&
      data.standorte.length > 0 &&
      !data.standorte.some((s) => s.istHauptstandort)
    ) {
      data.standorte[0].istHauptstandort = true;
    }
    if (
      data.ansprechpartner &&
      data.ansprechpartner.length > 0 &&
      !data.ansprechpartner.some((a) => a.istHauptansprechpartner)
    ) {
      data.ansprechpartner[0].istHauptansprechpartner = true;
    }

    // If ansprechpartner data is being updated, ensure mobil is handled correctly
    if (data.ansprechpartner) {
      data.ansprechpartner = data.ansprechpartner.map((ap) => ({
        ...ap,
        mobil: ap.mobil || undefined,
      }));
    }

    await ctx.db.patch(id, data);
    return id;
  },
});

/**
 * Kunden löschen
 */
export const remove = mutation({
  args: {
    id: v.id('kunden'),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }

    // Zukünftig: Hier auch zugehörige Dokumente, Kontingente, Leistungen etc. prüfen/löschen
    // Consider cascading deletes or warnings about related data.
    const leistungsVerknuepfungen = await ctx.db
      .query('kunden_leistungen')
      .withIndex('by_kunde', (q) => q.eq('kundenId', args.id))
      .collect();
    if (leistungsVerknuepfungen.length > 0) {
      throw new Error(
        `Kunde kann nicht gelöscht werden, da noch ${leistungsVerknuepfungen.length} Leistungen verknüpft sind.`
      );
    }
    const kontingentVerknuepfungen = await ctx.db
      .query('kunden_kontingente')
      .withIndex('by_kunde_and_status', (q) => q.eq('kundenId', args.id))
      .collect();
    if (kontingentVerknuepfungen.length > 0) {
      throw new Error(
        `Kunde kann nicht gelöscht werden, da noch ${kontingentVerknuepfungen.length} Kontingente verknüpft sind.`
      );
    }

    await ctx.db.delete(args.id);
    return null;
  },
});

/**
 * Einzelnen Kunden anhand seiner ID abrufen
 */
export const get = query({
  args: { id: v.id('kunden') },
  returns: v.union(kundeSchema, v.null()),
  handler: async (ctx, args) => {
    // Authentifizierung prüfen
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Nicht authentifiziert.');
    }
    return await ctx.db.get(args.id);
  },
});
